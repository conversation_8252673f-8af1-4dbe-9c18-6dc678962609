html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel API Documentation</title>
    <!-- Embedded CSS -->
    <style>
        /* Reset and Base Styles */
        *, *::before, *::after {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        html {
            scroll-behavior: smooth;
            font-size: 16px; /* Base font size */
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
            line-height: 1.6;
            color: var(--text-color);
            background-color: var(--bg-color);
            display: flex;
            min-height: 100vh;
            transition: background-color 0.3s, color 0.3s;
        }

        /* CSS Variables for Theming */
        :root {
            --bg-color: #ffffff;
            --sidebar-bg: #f8f9fa;
            --content-bg: #ffffff;
            --text-color: #343a40;
            --heading-color: #212529;
            --link-color: #007bff;
            --link-hover-color: #0056b3;
            --border-color: #dee2e6;
            --code-bg: #e9ecef;
            --code-color: #333;
            --pre-bg: #f1f3f5; /* Slightly different background for pre */
            --accent-color: #007bff;
            --sidebar-width: 280px;
            --header-height: 60px;

            --method-get-bg: #e7f5ff;
            --method-get-text: #007bff;
            --method-post-bg: #fff3e0;
            --method-post-text: #ff9800;
            --method-delete-bg: #ffebee;
            --method-delete-text: #f44336;

            /* Syntax Highlighting Colors (adjusted for better contrast/clarity) */
            --syntax-string: #032f62; /* Dark blue for strings */
            --syntax-number: #d73a49; /* Reddish for numbers */
            --syntax-boolean: #d73a49; /* Same as number */
            --syntax-null: #6f42c1;   /* Purple for null */
            --syntax-key: #e36209;    /* Orange for keys/attributes */
            --syntax-punctuation: #586069; /* Grey for punctuation */
            --syntax-operator: #d73a49;    /* Reddish for operators */
            --syntax-comment: #6a737d;    /* Grey italic for comments */
            --syntax-function: #6f42c1;   /* Purple for functions */
            --syntax-keyword: #d73a49;    /* Reddish for keywords */
            --syntax-variable: #e36209;   /* Orange for variables */
            --syntax-parameter: #24292e;  /* Dark grey for parameters */
            --syntax-url: #005cc5;      /* Blue for URLs */
            --syntax-tag: #22863a;      /* Green for tags */
            --syntax-classname: #6f42c1; /* Purple for class names */
        }

        /* Typography */
        h1, h2, h3, h4, h5, h6 {
            color: var(--heading-color);
            margin-top: 1.5em;
            margin-bottom: 0.8em;
            line-height: 1.3;
            font-weight: 600;
        }
        h1 { font-size: 2.25rem; margin-top: 0; border-bottom: 1px solid var(--border-color); padding-bottom: 0.5em; }
        h2 { font-size: 1.75rem; border-bottom: 1px solid var(--border-color); padding-bottom: 0.4em; margin-top: 2.5em;}
        h3 { font-size: 1.4rem; margin-top: 2em; }
        h4 { font-size: 1.1rem; font-weight: 600; margin-top: 1.8em; }
        p { margin-bottom: 1em; }
        a { color: var(--link-color); text-decoration: none; transition: color 0.2s ease; }
        a:hover { color: var(--link-hover-color); text-decoration: underline; }
        strong { font-weight: 600; }
        ul, ol { margin-left: 1.5em; margin-bottom: 1em; }
        li { margin-bottom: 0.5em; }

        /* Layout: Sidebar + Main Content */
        .sidebar {
            width: var(--sidebar-width);
            height: 100vh;
            position: fixed;
            top: 0;
            left: 0;
            background-color: var(--sidebar-bg);
            border-right: 1px solid var(--border-color);
            padding: 20px;
            overflow-y: auto;
            z-index: 1000;
            transition: transform 0.3s ease;
        }

        .main-content {
            margin-left: var(--sidebar-width);
            padding: 30px 40px;
            flex-grow: 1;
            max-width: calc(100% - var(--sidebar-width));
        }

        /* Sidebar Styles */
        .sidebar-header {
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 1px solid var(--border-color);
        }
        .sidebar-header h1 {
             font-size: 1.5rem;
             margin: 0;
             border: none;
             padding: 0;
        }
        .sidebar-search {
            width: 100%;
            padding: 10px;
            margin-bottom: 20px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 0.9rem;
        }
        .sidebar nav ul {
            list-style: none;
            margin-left: 0;
            padding-left: 0; /* Reset padding */
        }
        .sidebar nav > ul > li {
             margin-bottom: 15px; /* Space between categories */
        }
        .sidebar nav > ul > li > span { /* Category Title */
            font-weight: 600;
            color: var(--heading-color);
            font-size: 1rem;
            display: block;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            cursor: default;
        }
         .sidebar nav ul ul { /* Endpoint list */
            list-style: none;
            margin-left: 10px; /* Indent endpoints */
            padding-left: 0;
            margin-bottom: 0;
        }
        .sidebar nav ul ul li {
             margin-bottom: 5px;
        }
        .sidebar nav a {
            display: block;
            padding: 5px 10px;
            border-radius: 4px;
            color: var(--text-color);
            font-size: 0.9rem;
            transition: background-color 0.2s ease, color 0.2s ease;
        }
        .sidebar nav a:hover {
            background-color: #e9ecef;
            text-decoration: none;
            color: var(--heading-color);
        }
        .sidebar nav a.active {
            background-color: var(--accent-color);
            color: #fff;
            font-weight: 500;
        }
        .sidebar .hidden { /* Used by search JS */
            display: none !important; /* Ensure override */
        }


        /* Main Content Sections */
        .endpoint {
            margin-bottom: 3em;
            padding-bottom: 2em;
            border-bottom: 1px solid var(--border-color);
        }
        .endpoint:last-child {
            border-bottom: none;
        }
        .endpoint-header {
            display: flex;
            align-items: center;
            margin-bottom: 1em;
            flex-wrap: wrap; /* Wrap on small screens */
        }
        .endpoint-header h3 {
            margin: 0;
            font-size: 1.5rem; /* Make endpoint title stand out */
        }
        .method-badge {
            display: inline-block;
            padding: 4px 10px;
            border-radius: 4px;
            font-size: 0.85rem;
            font-weight: 700;
            text-transform: uppercase;
            margin-right: 12px;
            line-height: 1;
        }
        .method-badge.get { background-color: var(--method-get-bg); color: var(--method-get-text); border: 1px solid var(--method-get-text);}
        .method-badge.post { background-color: var(--method-post-bg); color: var(--method-post-text); border: 1px solid var(--method-post-text);}
        .method-badge.delete { background-color: var(--method-delete-bg); color: var(--method-delete-text); border: 1px solid var(--method-delete-text);}
        /* Add more colors for PUT, PATCH, etc. if needed */

        .endpoint-url {
            font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace;
            font-size: 0.95rem;
            background-color: var(--code-bg);
            padding: 3px 8px;
            border-radius: 4px;
            color: var(--code-color);
            word-break: break-all; /* Break long URLs */
            margin-left: auto; /* Push URL to the right on larger screens */
        }

        /* Parameters Table */
        .parameters-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 1.5em;
            font-size: 0.95rem;
        }
        .parameters-table th, .parameters-table td {
            border: 1px solid var(--border-color);
            padding: 10px 12px;
            text-align: left;
            vertical-align: top;
        }
        .parameters-table th {
            background-color: var(--sidebar-bg);
            font-weight: 600;
        }
        .parameters-table td code {
            font-size: 0.9em;
            background-color: var(--code-bg);
            padding: 2px 5px;
            border-radius: 3px;
        }
        .param-required {
            font-weight: bold;
            color: #dc3545; /* Bootstrap danger color */
        }
        .param-type {
            font-style: italic;
            color: #6c757d; /* Bootstrap secondary color */
        }
        .param-location {
             display: block;
             font-size: 0.85em;
             color: #6c757d;
             margin-top: 3px;
        }

        /* Code Blocks and Highlighting */
        .code-samples {
            margin-top: 1.5em;
        }
        .code-tabs {
            display: flex;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: -1px; /* Overlap border */
        }
        .code-tab {
            padding: 10px 15px;
            cursor: pointer;
            border: 1px solid transparent;
            border-bottom: none;
            margin-bottom: -1px;
            position: relative;
            background-color: var(--sidebar-bg);
             border-top-left-radius: 4px;
             border-top-right-radius: 4px;
             color: var(--text-color);
             margin-right: 5px;
             font-size: 0.9rem;
        }
        .code-tab.active {
            background-color: var(--content-bg);
            border-color: var(--border-color);
            border-bottom-color: var(--content-bg); /* Hide bottom border */
            font-weight: 600;
            color: var(--heading-color);
        }
         .code-content {
            border: 1px solid var(--border-color);
            border-top-right-radius: 4px; /* Match tab radius if first tab isnt active */
            border-bottom-left-radius: 4px;
            border-bottom-right-radius: 4px;
            overflow: hidden; /* Contain the pre element */
         }
        .code-content pre {
            background-color: var(--pre-bg);
            color: var(--code-color); /* Base code color */
            padding: 15px;
            margin: 0; /* Remove default margin */
            overflow-x: auto;
            font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace;
            font-size: 0.9rem;
            line-height: 1.5;
            border-radius: 0; /* Remove radius from pre */
             white-space: pre; /* Ensure whitespace is preserved */
        }
        .code-content pre code {
            background-color: transparent; /* Inherit from pre */
            padding: 0;
            border-radius: 0;
            color: inherit; /* Inherit base color */
            font-size: inherit; /* Ensure consistent font size */
            white-space: inherit; /* Inherit whitespace handling */
            display: block; /* Ensure code takes block space */
        }
        .code-block {
            display: none; /* Hide all code blocks initially */
        }
        .code-block.active {
            display: block; /* Show active code block */
        }

        /* Basic Syntax Highlighting Styles */
        .token.string { color: var(--syntax-string); }
        .token.number { color: var(--syntax-number); }
        .token.boolean { color: var(--syntax-boolean); }
        .token.null { color: var(--syntax-null); }
        .token.key { color: var(--syntax-key); font-weight: bold; } /* Make keys stand out */
        .token.punctuation { color: var(--syntax-punctuation); }
        .token.operator { color: var(--syntax-operator); }
        .token.comment { color: var(--syntax-comment); font-style: italic; }
        .token.function { color: var(--syntax-function); }
        .token.keyword { color: var(--syntax-keyword); font-weight: bold; }
        .token.variable { color: var(--syntax-variable); }
        .token.parameter { color: var(--syntax-parameter); font-weight: bold;} /* e.g., curl flags */
        .token.url { color: var(--syntax-url); }
        .token.tag { color: var(--syntax-tag); }
        .token.class-name { color: var(--syntax-classname); }


        /* Data Models & Other Sections */
        .data-model pre {
            background-color: var(--pre-bg);
            color: var(--code-color);
            padding: 15px;
            margin-bottom: 1.5em;
            overflow-x: auto;
            font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace;
            font-size: 0.9rem;
            line-height: 1.5;
            border: 1px solid var(--border-color);
            border-radius: 4px;
        }

        /* Hamburger Menu for Mobile */
        .mobile-menu-button {
            display: none; /* Hidden by default */
            position: fixed;
            top: 15px;
            left: 15px;
            z-index: 1001;
            background-color: var(--accent-color);
            color: white;
            border: none;
            padding: 10px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1.2rem;
        }

        /* Responsive Design */
        @media (max-width: 992px) {
            .sidebar {
                transform: translateX(-100%);
                width: var(--sidebar-width); /* Keep width consistent */
                z-index: 1100; /* Above content */
            }
            .sidebar.open {
                transform: translateX(0);
            }
            .main-content {
                margin-left: 0;
                max-width: 100%;
                padding: 20px;
            }
            .mobile-menu-button {
                display: block;
            }
            .endpoint-header {
                 flex-direction: column;
                 align-items: flex-start;
            }
            .endpoint-url {
                margin-left: 0; /* Reset margin */
                margin-top: 10px; /* Add space below title/badge */
                font-size: 0.9rem;
            }
        }

        @media (max-width: 768px) {
             html { font-size: 15px; }
            .main-content { padding: 15px; }
            h1 { font-size: 1.8rem; }
            h2 { font-size: 1.5rem; }
            h3 { font-size: 1.25rem; }
            .endpoint-header h3 { font-size: 1.3rem; }
            .code-tab { padding: 8px 12px; font-size: 0.85rem; }
            .code-content pre { font-size: 0.85rem; padding: 12px; }
        }

        @media (max-width: 480px) {
             :root { --sidebar-width: 250px; } /* Slightly smaller sidebar on mobile */
             html { font-size: 14px; }
             .parameters-table { font-size: 0.9rem; }
             .parameters-table th, .parameters-table td { padding: 8px 10px; }
        }

    </style>
</head>
<body>

    <!-- Hamburger button for mobile -->
    <button class="mobile-menu-button" id="mobile-menu-toggle" aria-label="Toggle Menu">
        ☰
    </button>

    <!-- Sticky Sidebar -->
    <aside class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h1>Admin Panel API</h1>
        </div>
        <input type="text" id="sidebar-search" class="sidebar-search" placeholder="Search endpoints...">
        <nav id="sidebar-nav">
             <ul>
                <!-- Navigation items will be dynamically populated or hardcoded here -->
                <!-- Example Structure: -->
                <li data-category="Overview">
                    <span>Overview</span>
                    <ul>
                        <li><a href="#overview">Introduction</a></li>
                        <li><a href="#authentication-notes">Authentication</a></li>
                        <li><a href="#error-handling">Error Handling</a></li>
                        <li><a href="#data-models">Data Models</a></li>
                    </ul>
                </li>
                <li data-category="Authentication">
                    <span>Authentication</span>
                    <ul>
                        <li><a href="#endpoint-login">Login</a></li>
                        <li><a href="#endpoint-logout">Logout</a></li>
                    </ul>
                </li>
                 <li data-category="Dashboard">
                    <span>Dashboard</span>
                    <ul>
                        <li><a href="#endpoint-dashboard-home">Dashboard Home</a></li>
                        <li><a href="#endpoint-admin-profile">Admin Profile</a></li>
                        <li><a href="#endpoint-change-password">Change Password</a></li>
                        <li><a href="#endpoint-dashboard-search">Search</a></li>
                    </ul>
                </li>
                 <li data-category="User Management">
                    <span>User Management</span>
                    <ul>
                         <li><a href="#endpoint-list-users">List Users</a></li>
                         <li><a href="#endpoint-export-users-csv">Export Users CSV</a></li>
                         <li><a href="#endpoint-user-detail">User Detail</a></li>
                         <li><a href="#endpoint-send-invite">Send Telegram Invite</a></li>
                         <li><a href="#endpoint-kick-user">Kick User</a></li>
                         <li><a href="#endpoint-unban-user">Unban User</a></li>
                    </ul>
                </li>
                 <li data-category="Access Code Management">
                    <span>Access Codes</span>
                    <ul>
                         <li><a href="#endpoint-list-access-codes">List Access Codes</a></li>
                         <li><a href="#endpoint-add-access-code">Add Access Code</a></li>
                         <li><a href="#endpoint-upload-access-codes">Upload Access Codes</a></li>
                         <li><a href="#endpoint-edit-access-code">Edit Access Code</a></li>
                         <li><a href="#endpoint-delete-access-code">Delete Access Code</a></li>
                         <li><a href="#endpoint-bulk-delete-access-codes">Bulk Delete Access Codes</a></li>
                         <li><a href="#endpoint-access-code-details">Access Code Details</a></li>
                         <li><a href="#endpoint-check-code-in-use">Check If Code In Use</a></li>
                    </ul>
                </li>
                 <li data-category="Request Management">
                    <span>Requests</span>
                    <ul>
                         <li><a href="#endpoint-list-verification-requests">List Verification Requests</a></li>
                         <li><a href="#endpoint-list-support-requests">List Support Requests</a></li>
                         <li><a href="#endpoint-verification-request-details">Verification Request Details</a></li>
                         <li><a href="#endpoint-support-request-details">Support Request Details</a></li>
                         <li><a href="#endpoint-resolve-support-request">Resolve Support Request</a></li>
                         <li><a href="#endpoint-approve-verification-request">Approve Verification Request</a></li>
                         <li><a href="#endpoint-deny-verification-request">Deny Verification Request</a></li>
                         <li><a href="#endpoint-add-verification-note">Add Verification Request Note</a></li>
                         <li><a href="#endpoint-send-support-notification">Send Support Notification</a></li>
                    </ul>
                </li>
                 <li data-category="Telegram Bot Management">
                    <span>Telegram Bots</span>
                    <ul>
                         <li><a href="#endpoint-list-telegram-bots">List Telegram Bots</a></li>
                         <li><a href="#endpoint-add-telegram-bot">Add Telegram Bot</a></li>
                         <li><a href="#endpoint-edit-telegram-bot">Edit Telegram Bot</a></li>
                         <li><a href="#endpoint-delete-telegram-bot">Delete Telegram Bot</a></li>
                         <li><a href="#endpoint-test-telegram-bot">Test Telegram Bot</a></li>
                         <li><a href="#endpoint-list-telegram-bot-users">List Telegram Bot Users</a></li>
                    </ul>
                </li>
                <!-- Add more categories and endpoints as needed -->
            </ul>
        </nav>
    </aside>

    <!-- Main Content Area -->
    <main class="main-content" id="main-content">
        <h1>Admin Panel API Documentation</h1>

        <!-- Overview Section -->
        <section id="overview" data-searchable-text="overview introduction flask telegram admin panel">
            <h2>Overview</h2>
            <p>This document provides details for the REST APIs used by the Flask-based admin panel for managing a Telegram bot system. These APIs are primarily designed for internal use by the admin panel's web interface.</p>
            <p>The server runs on port <code>5001</code> by default (e.g., <code>http://localhost:5001</code>).</p>
        </section>

        <!-- Authentication Notes Section -->
        <section id="authentication-notes" data-searchable-text="authentication session cookie login logout security">
            <h2>Authentication</h2>
            <p>All API endpoints, except for <code>/login</code>, require authentication using a session-based system. A session cookie is obtained upon successful login and must be included in subsequent requests.</p>
            <ul>
                <li>Login at <code>/login</code> to establish a session.</li>
                <li>Use the session cookie automatically managed by the browser or manually include it in tools like cURL (using <code>-b</code>/<code>-c</code> flags).</li>
                <li>Logout at <code>/logout</code> to invalidate the session.</li>
                <li>Sessions typically expire when the browser is closed or after an explicit logout.</li>
                <li>The session stores the admin ID to authorize actions.</li>
                <li>Requests without a valid session will likely result in a redirect to the login page or a <code>401 Unauthorized</code> status (depending on the endpoint).</li>
            </ul>
             <p><strong>Note:</strong> The cURL examples use <code>-b cookies.txt</code> (read cookies) and <code>-c cookies.txt</code> (write cookies) to simulate session handling. Fetch examples rely on the browser's automatic cookie management when run in the same origin context, or require the <code>credentials: 'include'</code> option if making cross-origin requests (ensure CORS is configured correctly on the server).</p>
        </section>


        <!-- ======================= -->
        <!-- Authentication Endpoints -->
        <!-- ======================= -->
        <h2 id="category-authentication">Authentication</h2>

        <section class="endpoint" id="endpoint-login" data-searchable-text="login post authentication username password session cookie">
            <div class="endpoint-header">
                <span class="method-badge post">POST</span>
                <h3>Login</h3>
                <code class="endpoint-url">/login</code>
            </div>
            <p>Authenticates the admin user and establishes a session.</p>

            <h4>Authentication</h4>
            <p>None required for this endpoint.</p>

            <h4>Request Body (Form Data)</h4>
            <table class="parameters-table">
                <thead>
                    <tr>
                        <th>Parameter</th>
                        <th>Type</th>
                        <th>Description</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><code>username</code></td>
                        <td><span class="param-type">string</span></td>
                        <td><span class="param-required">Required.</span> Admin username.</td>
                    </tr>
                    <tr>
                        <td><code>password</code></td>
                        <td><span class="param-type">string</span></td>
                        <td><span class="param-required">Required.</span> Admin password.</td>
                    </tr>
                </tbody>
            </table>

            <h4>Response</h4>
            <p>Redirects to <code>/dashboard/</code> on successful login. Returns the login page HTML with an error message on failure.</p>

            <div class="code-samples">
                <div class="code-tabs">
                    <button class="code-tab active" data-target="login-curl">cURL</button>
                    <button class="code-tab" data-target="login-fetch">Fetch</button>
                </div>
                <div class="code-content">
                    <div class="code-block active" id="login-curl">
                        <pre><code class="language-bash">curl -X POST http://localhost:5001/login \
 -d "username=admin&password=yourpassword" \
 -c cookies.txt # Saves the session cookie</code></pre>
                    </div>
                     <div class="code-block" id="login-fetch">
                        <pre><code class="language-javascript">const formData = new URLSearchParams();
formData.append('username', 'admin');
formData.append('password', 'yourpassword');

fetch('/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/x-www-form-urlencoded',
  },
  body: formData,
  // redirect: 'manual' // Use if you want to handle the redirect manually
})
.then(response => {
  // Response will likely be a redirect (status 302)
  // The browser will typically follow the redirect automatically
  // and the session cookie will be set.
  if (response.ok || response.status === 302) {
    console.log('Login likely successful, redirecting...');
    // window.location.href = '/dashboard/'; // Or let the browser handle it
  } else {
    console.error('Login failed:', response.status);
    // Handle error, maybe display message from response body if available
    return response.text(); // Or response.json() if error is JSON
  }
})
.then(errorHtml => {
  if(errorHtml) console.log('Error page content:', errorHtml);
})
.catch(error => console.error('Error:', error));</code></pre>
                    </div>
                </div>
            </div>
        </section>

        <section class="endpoint" id="endpoint-logout" data-searchable-text="logout get authentication session cookie invalidate">
            <div class="endpoint-header">
                <span class="method-badge get">GET</span>
                <h3>Logout</h3>
                <code class="endpoint-url">/logout</code>
            </div>
            <p>Invalidates the current admin session.</p>

            <h4>Authentication</h4>
            <p>Requires a valid session cookie.</p>

            <h4>Request Parameters</h4>
            <p>None.</p>

            <h4>Response</h4>
            <p>Redirects to the <code>/login</code> page.</p>

            <div class="code-samples">
                <div class="code-tabs">
                    <button class="code-tab active" data-target="logout-curl">cURL</button>
                    <button class="code-tab" data-target="logout-fetch">Fetch</button>
                </div>
                <div class="code-content">
                     <div class="code-block active" id="logout-curl">
                        <pre><code class="language-bash">curl -X GET http://localhost:5001/logout \
 -b cookies.txt # Sends the session cookie</code></pre>
                    </div>
                    <div class="code-block" id="logout-fetch">
                        <pre><code class="language-javascript">fetch('/logout', {
  method: 'GET',
  credentials: 'include' // Important to send the session cookie
  // redirect: 'manual' // Optional: handle redirect manually
})
.then(response => {
  // Expecting a redirect to /login
  console.log('Logout successful, redirecting...');
  // The browser usually handles the redirect and clears the relevant cookie
   window.location.href = '/login'; // Force redirect if needed
})
.catch(error => console.error('Error:', error));</code></pre>
                    </div>
                </div>
            </div>
        </section>


        <!-- ======================= -->
        <!-- Dashboard APIs          -->
        <!-- ======================= -->
        <h2 id="category-dashboard">Dashboard APIs</h2>

        <section class="endpoint" id="endpoint-dashboard-home" data-searchable-text="dashboard home get list subscriptions pagination filter search">
            <div class="endpoint-header">
                <span class="method-badge get">GET</span>
                <h3>Dashboard Home</h3>
                <code class="endpoint-url">/dashboard/</code>
            </div>
            <p>Retrieves the main dashboard page, displaying active subscriptions and potentially other key information.</p>

            <h4>Authentication</h4>
            <p>Requires a valid session cookie.</p>

            <h4>Query Parameters</h4>
            <table class="parameters-table">
                 <thead><tr><th>Parameter</th><th>Type</th><th>Description</th></tr></thead>
                <tbody>
                    <tr>
                        <td><code>page</code></td>
                        <td><span class="param-type">integer</span></td>
                        <td>Optional. Page number for paginating subscriptions (default: 1).</td>
                    </tr>
                    <tr>
                        <td><code>per_page</code></td>
                        <td><span class="param-type">integer</span></td>
                        <td>Optional. Number of subscriptions per page (default: 6).</td>
                    </tr>
                     <tr>
                        <td><code>filter</code></td>
                        <td><span class="param-type">string</span></td>
                        <td>Optional. A search term to filter subscriptions.</td>
                    </tr>
                </tbody>
            </table>

            <h4>Response</h4>
            <p>Returns the HTML content of the main dashboard page.</p>

            <div class="code-samples">
                <div class="code-tabs">
                    <button class="code-tab active" data-target="dash-home-curl">cURL</button>
                    <button class="code-tab" data-target="dash-home-fetch">Fetch</button>
                </div>
                <div class="code-content">
                     <div class="code-block active" id="dash-home-curl">
                        <pre><code class="language-bash"># Get first page with default per_page, filtering for 'search_term'
curl -X GET "http://localhost:5001/dashboard/?page=1&amp;filter=search_term" \
 -b cookies.txt</code></pre>
                    </div>
                     <div class="code-block" id="dash-home-fetch">
                        <pre><code class="language-javascript">fetch('/dashboard/?page=1&filter=search_term', {
  method: 'GET',
  credentials: 'include' // Send cookies
})
.then(response => {
  if (response.ok) {
    return response.text(); // Get HTML content
  } else {
    console.error('Failed to load dashboard:', response.status);
    // Handle error (e.g., redirect to login if unauthorized)
  }
})
.then(html => {
  if (html) {
    console.log('Dashboard HTML received');
    // document.body.innerHTML = html; // Example: Replace page content
  }
})
.catch(error => console.error('Error:', error));</code></pre>
                    </div>
                </div>
            </div>
        </section>

        <section class="endpoint" id="endpoint-admin-profile" data-searchable-text="admin profile get settings details activity log">
            <div class="endpoint-header">
                <span class="method-badge get">GET</span>
                <h3>Admin Profile</h3>
                <code class="endpoint-url">/dashboard/profile</code>
            </div>
            <p>Retrieves the admin profile page, showing details and activity logs.</p>

            <h4>Authentication</h4>
            <p>Requires a valid session cookie.</p>

            <h4>Response</h4>
            <p>Returns the HTML content of the admin profile page.</p>

            <div class="code-samples">
                 <div class="code-tabs">
                    <button class="code-tab active" data-target="profile-get-curl">cURL</button>
                    <button class="code-tab" data-target="profile-get-fetch">Fetch</button>
                </div>
                <div class="code-content">
                     <div class="code-block active" id="profile-get-curl">
                        <pre><code class="language-bash">curl -X GET http://localhost:5001/dashboard/profile \
 -b cookies.txt</code></pre>
                    </div>
                     <div class="code-block" id="profile-get-fetch">
                        <pre><code class="language-javascript">fetch('/dashboard/profile', {
  method: 'GET',
  credentials: 'include'
})
.then(response => response.ok ? response.text() : Promise.reject('Failed to load profile'))
.then(html => {
  console.log('Profile HTML loaded.');
  // Update UI with HTML
})
.catch(error => console.error('Error:', error));</code></pre>
                    </div>
                </div>
            </div>
        </section>

        <section class="endpoint" id="endpoint-change-password" data-searchable-text="admin profile change password post security update">
            <div class="endpoint-header">
                <span class="method-badge post">POST</span>
                <h3>Change Password</h3>
                <code class="endpoint-url">/dashboard/profile/change-password</code>
            </div>
            <p>Updates the logged-in admin's password.</p>

            <h4>Authentication</h4>
            <p>Requires a valid session cookie.</p>

            <h4>Request Body (Form Data)</h4>
            <table class="parameters-table">
                 <thead><tr><th>Parameter</th><th>Type</th><th>Description</th></tr></thead>
                <tbody>
                    <tr>
                        <td><code>current_password</code></td>
                        <td><span class="param-type">string</span></td>
                        <td><span class="param-required">Required.</span> The admin's current password.</td>
                    </tr>
                     <tr>
                        <td><code>new_password</code></td>
                        <td><span class="param-type">string</span></td>
                        <td><span class="param-required">Required.</span> The desired new password.</td>
                    </tr>
                     <tr>
                        <td><code>confirm_password</code></td>
                        <td><span class="param-type">string</span></td>
                        <td><span class="param-required">Required.</span> Confirmation of the new password (must match <code>new_password</code>).</td>
                    </tr>
                </tbody>
            </table>

            <h4>Response</h4>
            <p>Redirects back to the <code>/dashboard/profile</code> page with a success or error message (usually via flash messages).</p>

            <div class="code-samples">
                <div class="code-tabs">
                    <button class="code-tab active" data-target="change-pw-curl">cURL</button>
                    <button class="code-tab" data-target="change-pw-fetch">Fetch</button>
                </div>
                <div class="code-content">
                    <div class="code-block active" id="change-pw-curl">
                        <pre><code class="language-bash">curl -X POST http://localhost:5001/dashboard/profile/change-password \
 -b cookies.txt \
 -d "current_password=oldpass&amp;new_password=newpass&amp;confirm_password=newpass"</code></pre>
                    </div>
                     <div class="code-block" id="change-pw-fetch">
                        <pre><code class="language-javascript">const formData = new URLSearchParams();
formData.append('current_password', 'oldpass');
formData.append('new_password', 'newpass');
formData.append('confirm_password', 'newpass');

fetch('/dashboard/profile/change-password', {
  method: 'POST',
  credentials: 'include',
  headers: {
    'Content-Type': 'application/x-www-form-urlencoded',
  },
  body: formData,
  redirect: 'follow' // Browser usually handles redirects automatically
})
.then(response => {
  // After redirect, the response URL should be the profile page
  if (response.ok && response.url.includes('/dashboard/profile')) {
     console.log('Password change successful, redirected to profile.');
     // You might need to reload the page or check for flash messages
     // depending on how the server implements feedback.
  } else {
     console.error('Password change failed or unexpected redirect.');
     // Handle error - maybe the response contains error details if not redirected
  }
})
.catch(error => console.error('Error:', error));</code></pre>
                    </div>
                </div>
            </div>
        </section>

        <section class="endpoint" id="endpoint-dashboard-search" data-searchable-text="dashboard search get query find results">
            <div class="endpoint-header">
                <span class="method-badge get">GET</span>
                <h3>Search</h3>
                <code class="endpoint-url">/dashboard/search</code>
            </div>
            <p>Performs a general search across the dashboard data (e.g., users, codes) and displays results.</p>

            <h4>Authentication</h4>
            <p>Requires a valid session cookie.</p>

            <h4>Query Parameters</h4>
             <table class="parameters-table">
                <thead><tr><th>Parameter</th><th>Type</th><th>Description</th></tr></thead>
                <tbody>
                    <tr>
                        <td><code>q</code></td>
                        <td><span class="param-type">string</span></td>
                        <td><span class="param-required">Required.</span> The search query term.</td>
                    </tr>
                </tbody>
            </table>

            <h4>Response</h4>
            <p>Returns an HTML page displaying the search results.</p>

            <div class="code-samples">
                 <div class="code-tabs">
                    <button class="code-tab active" data-target="search-get-curl">cURL</button>
                    <button class="code-tab" data-target="search-get-fetch">Fetch</button>
                </div>
                <div class="code-content">
                     <div class="code-block active" id="search-get-curl">
                        <pre><code class="language-bash">curl -X GET "http://localhost:5001/dashboard/search?q=search_term" \
 -b cookies.txt</code></pre>
                    </div>
                     <div class="code-block" id="search-get-fetch">
                        <pre><code class="language-javascript">const searchTerm = 'search_term';
fetch(`/dashboard/search?q=${encodeURIComponent(searchTerm)}`, {
  method: 'GET',
  credentials: 'include'
})
.then(response => response.ok ? response.text() : Promise.reject('Search failed'))
.then(html => {
  console.log('Search results HTML loaded.');
  // Update UI with results HTML
})
.catch(error => console.error('Error:', error));</code></pre>
                    </div>
                </div>
            </div>
        </section>


        <!-- ======================= -->
        <!-- User Management APIs    -->
        <!-- ======================= -->
        <h2 id="category-user-management">User Management</h2>

        <section class="endpoint" id="endpoint-list-users" data-searchable-text="users list get user management pagination filter sort status">
             <div class="endpoint-header">
                <span class="method-badge get">GET</span>
                <h3>List Users</h3>
                <code class="endpoint-url">/dashboard/users</code>
            </div>
            <p>Retrieves a paginated and filterable list of users.</p>

            <h4>Authentication</h4>
            <p>Requires a valid session cookie.</p>

            <h4>Query Parameters</h4>
            <table class="parameters-table">
                 <thead><tr><th>Parameter</th><th>Type</th><th>Description</th></tr></thead>
                <tbody>
                    <tr><td><code>page</code></td><td><span class="param-type">integer</span></td><td>Optional. Page number (default: 1).</td></tr>
                    <tr><td><code>per_page</code></td><td><span class="param-type">integer</span></td><td>Optional. Items per page (default: 10).</td></tr>
                    <tr><td><code>sort</code></td><td><span class="param-type">string</span></td><td>Optional. Field to sort by (e.g., <code>user_id</code>, <code>last_updated</code>) (default: <code>last_updated</code>).</td></tr>
                    <tr><td><code>order</code></td><td><span class="param-type">string</span></td><td>Optional. Sort order: 'asc' or 'desc' (default: <code>desc</code>).</td></tr>
                    <tr><td><code>filter</code></td><td><span class="param-type">string</span></td><td>Optional. Search term to filter users.</td></tr>
                    <tr><td><code>status</code></td><td><span class="param-type">string</span></td><td>Optional. Filter by user status (e.g., 'active', 'inactive', 'all') (default: <code>all</code>).</td></tr>
                </tbody>
            </table>

            <h4>Response</h4>
            <p>Returns an HTML page displaying the list of users.</p>

            <div class="code-samples">
                <div class="code-tabs">
                    <button class="code-tab active" data-target="list-users-curl">cURL</button>
                    <button class="code-tab" data-target="list-users-fetch">Fetch</button>
                </div>
                <div class="code-content">
                    <div class="code-block active" id="list-users-curl">
                        <pre><code class="language-bash"># Get page 1, 20 users per page, sorted by user_id ascending, status active
curl -X GET "http://localhost:5001/dashboard/users?page=1&amp;per_page=20&amp;sort=user_id&amp;order=asc&amp;status=active" \
 -b cookies.txt</code></pre>
                    </div>
                     <div class="code-block" id="list-users-fetch">
                        <pre><code class="language-javascript">const params = new URLSearchParams({
  page: 1,
  per_page: 20,
  sort: 'user_id',
  order: 'asc',
  status: 'active'
});

fetch(`/dashboard/users?${params.toString()}`, {
  method: 'GET',
  credentials: 'include'
})
.then(response => response.ok ? response.text() : Promise.reject('Failed to list users'))
.then(html => {
  console.log('User list HTML loaded.');
  // Update UI with HTML
})
.catch(error => console.error('Error:', error));</code></pre>
                    </div>
                </div>
            </div>
        </section>

        <section class="endpoint" id="endpoint-export-users-csv" data-searchable-text="users export csv download get report">
             <div class="endpoint-header">
                <span class="method-badge get">GET</span>
                <h3>Export Users CSV</h3>
                <code class="endpoint-url">/dashboard/export-users-csv</code>
            </div>
            <p>Downloads a CSV file containing data for all users.</p>

            <h4>Authentication</h4>
            <p>Requires a valid session cookie.</p>

            <h4>Response</h4>
            <p>Returns a CSV file download (<code>Content-Type: text/csv</code>).</p>

            <div class="code-samples">
                 <div class="code-tabs">
                    <button class="code-tab active" data-target="export-csv-curl">cURL</button>
                    <button class="code-tab" data-target="export-csv-fetch">Fetch</button>
                </div>
                <div class="code-content">
                    <div class="code-block active" id="export-csv-curl">
                        <pre><code class="language-bash">curl -X GET http://localhost:5001/dashboard/export-users-csv \
 -b cookies.txt \
 -o users.csv # Save output to users.csv</code></pre>
                    </div>
                     <div class="code-block" id="export-csv-fetch">
                        <pre><code class="language-javascript">fetch('/dashboard/export-users-csv', {
  method: 'GET',
  credentials: 'include'
})
.then(response => {
  if (response.ok) {
    // Get filename from Content-Disposition header if available
    const disposition = response.headers.get('Content-Disposition');
    let filename = 'users.csv';
    if (disposition && disposition.indexOf('attachment') !== -1) {
      const filenameRegex = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/;
      const matches = filenameRegex.exec(disposition);
      if (matches != null && matches[1]) {
        filename = matches[1].replace(/['"]/g, '');
      }
    }
    return response.blob().then(blob => ({ blob, filename }));
  } else {
    return Promise.reject('Failed to download CSV');
  }
})
.then(({ blob, filename }) => {
  // Create a link and trigger download
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.style.display = 'none';
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  window.URL.revokeObjectURL(url);
  a.remove();
  console.log('CSV download initiated.');
})
.catch(error => console.error('Error:', error));</code></pre>
                    </div>
                </div>
            </div>
        </section>

        <section class="endpoint" id="endpoint-user-detail" data-searchable-text="users detail get user management view specific user_id">
             <div class="endpoint-header">
                <span class="method-badge get">GET</span>
                <h3>User Detail</h3>
                <code class="endpoint-url">/dashboard/user/&lt;user_id&gt;</code>
            </div>
            <p>Retrieves the details page for a specific user.</p>

            <h4>Authentication</h4>
            <p>Requires a valid session cookie.</p>

             <h4>Path Parameters</h4>
            <table class="parameters-table">
                 <thead><tr><th>Parameter</th><th>Type</th><th>Description</th></tr></thead>
                <tbody>
                    <tr><td><code>user_id</code></td><td><span class="param-type">integer/string</span></td><td><span class="param-required">Required.</span> The unique identifier of the user.</td></tr>
                </tbody>
            </table>

            <h4>Response</h4>
            <p>Returns an HTML page displaying the details of the specified user.</p>

            <div class="code-samples">
                 <div class="code-tabs">
                    <button class="code-tab active" data-target="user-detail-curl">cURL</button>
                    <button class="code-tab" data-target="user-detail-fetch">Fetch</button>
                </div>
                <div class="code-content">
                    <div class="code-block active" id="user-detail-curl">
                        <pre><code class="language-bash">curl -X GET http://localhost:5001/dashboard/user/123456789 \
 -b cookies.txt</code></pre>
                    </div>
                     <div class="code-block" id="user-detail-fetch">
                        <pre><code class="language-javascript">const userId = '123456789';
fetch(`/dashboard/user/${userId}`, {
  method: 'GET',
  credentials: 'include'
})
.then(response => response.ok ? response.text() : Promise.reject('Failed to get user details'))
.then(html => {
  console.log('User detail HTML loaded.');
  // Update UI with HTML
})
.catch(error => console.error('Error:', error));</code></pre>
                    </div>
                </div>
            </div>
        </section>

        <section class="endpoint" id="endpoint-send-invite" data-searchable-text="users send invite telegram post user_id channel">
             <div class="endpoint-header">
                <span class="method-badge post">POST</span>
                <h3>Send Telegram Invite</h3>
                <code class="endpoint-url">/dashboard/user/&lt;user_id&gt;/send-invite</code>
            </div>
            <p>Sends a Telegram channel invite link to the specified user via the bot.</p>

            <h4>Authentication</h4>
            <p>Requires a valid session cookie.</p>

            <h4>Path Parameters</h4>
            <table class="parameters-table">
                 <thead><tr><th>Parameter</th><th>Type</th><th>Description</th></tr></thead>
                <tbody>
                    <tr><td><code>user_id</code></td><td><span class="param-type">integer/string</span></td><td><span class="param-required">Required.</span> The Telegram User ID of the user to invite.</td></tr>
                </tbody>
            </table>

            <h4>Response</h4>
            <p>Returns a JSON response indicating success or failure.</p>
             <pre><code class="language-json">{
  "success": true,
  "message": "Invite sent successfully."
}
// Or on error:
{
  "success": false,
  "error": "Failed to send invite. Reason..."
}</code></pre>

            <div class="code-samples">
                <div class="code-tabs">
                    <button class="code-tab active" data-target="send-invite-curl">cURL</button>
                    <button class="code-tab" data-target="send-invite-fetch">Fetch</button>
                </div>
                <div class="code-content">
                    <div class="code-block active" id="send-invite-curl">
                        <pre><code class="language-bash">curl -X POST http://localhost:5001/dashboard/user/123456789/send-invite \
 -b cookies.txt \
 -H "Accept: application/json" # Ensure JSON response is requested if needed</code></pre>
                    </div>
                     <div class="code-block" id="send-invite-fetch">
                        <pre><code class="language-javascript">const userId = '123456789';
fetch(`/dashboard/user/${userId}/send-invite`, {
  method: 'POST',
  credentials: 'include',
  headers: {
    'Accept': 'application/json' // Request JSON response
  }
})
.then(response => response.json())
.then(data => {
  if (data.success) {
    console.log('Invite sent:', data.message);
    // Update UI (e.g., show success message)
  } else {
    console.error('Failed to send invite:', data.error);
    // Update UI (e.g., show error message)
  }
})
.catch(error => console.error('Error:', error));</code></pre>
                    </div>
                </div>
            </div>
        </section>

        <section class="endpoint" id="endpoint-kick-user" data-searchable-text="users kick remove ban post user_id telegram channel">
             <div class="endpoint-header">
                <span class="method-badge post">POST</span>
                <h3>Kick User</h3>
                <code class="endpoint-url">/dashboard/user/&lt;user_id&gt;/kick</code>
            </div>
            <p>Kicks (removes and bans) the specified user from the associated Telegram channel.</p>

            <h4>Authentication</h4>
            <p>Requires a valid session cookie.</p>

             <h4>Path Parameters</h4>
            <table class="parameters-table">
                 <thead><tr><th>Parameter</th><th>Type</th><th>Description</th></tr></thead>
                <tbody>
                    <tr><td><code>user_id</code></td><td><span class="param-type">integer/string</span></td><td><span class="param-required">Required.</span> The Telegram User ID of the user to kick.</td></tr>
                </tbody>
            </table>

            <h4>Response</h4>
            <p>Redirects back to the user detail page (<code>/dashboard/user/&lt;user_id&gt;</code>) with a success or error message (usually via flash messages).</p>

            <div class="code-samples">
                 <div class="code-tabs">
                    <button class="code-tab active" data-target="kick-user-curl">cURL</button>
                    <button class="code-tab" data-target="kick-user-fetch">Fetch</button>
                </div>
                <div class="code-content">
                    <div class="code-block active" id="kick-user-curl">
                        <pre><code class="language-bash">curl -X POST http://localhost:5001/dashboard/user/123456789/kick \
 -b cookies.txt</code></pre>
                    </div>
                     <div class="code-block" id="kick-user-fetch">
                        <pre><code class="language-javascript">const userId = '123456789';
fetch(`/dashboard/user/${userId}/kick`, {
  method: 'POST',
  credentials: 'include',
  redirect: 'follow' // Follow the redirect back to the user detail page
})
.then(response => {
  if (response.ok && response.url.includes(`/dashboard/user/${userId}`)) {
     console.log('User kick successful, redirected to user page.');
     // Reload page or check for flash messages
  } else {
     console.error('User kick failed or unexpected redirect.');
  }
})
.catch(error => console.error('Error:', error));</code></pre>
                    </div>
                </div>
            </div>
        </section>

        <section class="endpoint" id="endpoint-unban-user" data-searchable-text="users unban remove ban post user_id telegram channel restore">
             <div class="endpoint-header">
                <span class="method-badge post">POST</span>
                <h3>Unban User</h3>
                <code class="endpoint-url">/dashboard/user/&lt;user_id&gt;/unban</code>
            </div>
            <p>Unbans the specified user from the associated Telegram channel (allowing them to rejoin if they have a link).</p>

            <h4>Authentication</h4>
            <p>Requires a valid session cookie.</p>

             <h4>Path Parameters</h4>
            <table class="parameters-table">
                 <thead><tr><th>Parameter</th><th>Type</th><th>Description</th></tr></thead>
                <tbody>
                    <tr><td><code>user_id</code></td><td><span class="param-type">integer/string</span></td><td><span class="param-required">Required.</span> The Telegram User ID of the user to unban.</td></tr>
                </tbody>
            </table>

            <h4>Response</h4>
            <p>Redirects back to the user detail page (<code>/dashboard/user/&lt;user_id&gt;</code>) with a success or error message (usually via flash messages).</p>

            <div class="code-samples">
                 <div class="code-tabs">
                    <button class="code-tab active" data-target="unban-user-curl">cURL</button>
                    <button class="code-tab" data-target="unban-user-fetch">Fetch</button>
                </div>
                <div class="code-content">
                    <div class="code-block active" id="unban-user-curl">
                        <pre><code class="language-bash">curl -X POST http://localhost:5001/dashboard/user/123456789/unban \
 -b cookies.txt</code></pre>
                    </div>
                     <div class="code-block" id="unban-user-fetch">
                        <pre><code class="language-javascript">const userId = '123456789';
fetch(`/dashboard/user/${userId}/unban`, {
  method: 'POST',
  credentials: 'include',
  redirect: 'follow'
})
.then(response => {
  if (response.ok && response.url.includes(`/dashboard/user/${userId}`)) {
     console.log('User unban successful, redirected to user page.');
     // Reload page or check for flash messages
  } else {
     console.error('User unban failed or unexpected redirect.');
  }
})
.catch(error => console.error('Error:', error));</code></pre>
                    </div>
                </div>
            </div>
        </section>


        <!-- ========================== -->
        <!-- Access Code Management APIs -->
        <!-- ========================== -->
        <h2 id="category-access-codes">Access Code Management</h2>

         <section class="endpoint" id="endpoint-list-access-codes" data-searchable-text="access codes list get pagination filter sort">
             <div class="endpoint-header">
                <span class="method-badge get">GET</span>
                <h3>List Access Codes</h3>
                <code class="endpoint-url">/dashboard/access-codes</code>
            </div>
            <p>Retrieves a paginated and filterable list of access codes.</p>

            <h4>Authentication</h4>
            <p>Requires a valid session cookie.</p>

            <h4>Query Parameters</h4>
            <table class="parameters-table">
                 <thead><tr><th>Parameter</th><th>Type</th><th>Description</th></tr></thead>
                <tbody>
                    <tr><td><code>page</code></td><td><span class="param-type">integer</span></td><td>Optional. Page number for pagination.</td></tr>
                    <tr><td><code>per_page</code></td><td><span class="param-type">integer</span></td><td>Optional. Items per page.</td></tr>
                    <tr><td><code>sort</code></td><td><span class="param-type">string</span></td><td>Optional. Field to sort by (e.g., <code>code</code>, <code>added_at</code>, <code>expiration_date</code>).</td></tr>
                    <tr><td><code>order</code></td><td><span class="param-type">string</span></td><td>Optional. Sort order: 'asc' or 'desc'.</td></tr>
                    <tr><td><code>filter</code></td><td><span class="param-type">string</span></td><td>Optional. Search term to filter codes.</td></tr>
                </tbody>
            </table>

            <h4>Response</h4>
            <p>Returns an HTML page displaying the list of access codes.</p>

            <div class="code-samples">
                 <div class="code-tabs">
                    <button class="code-tab active" data-target="list-codes-curl">cURL</button>
                    <button class="code-tab" data-target="list-codes-fetch">Fetch</button>
                </div>
                <div class="code-content">
                    <div class="code-block active" id="list-codes-curl">
                        <pre><code class="language-bash"># Get first page, 20 codes per page
curl -X GET "http://localhost:5001/dashboard/access-codes?page=1&amp;per_page=20" \
 -b cookies.txt</code></pre>
                    </div>
                     <div class="code-block" id="list-codes-fetch">
                        <pre><code class="language-javascript">const params = new URLSearchParams({ page: 1, per_page: 20 });
fetch(`/dashboard/access-codes?${params.toString()}`, {
  method: 'GET',
  credentials: 'include'
})
.then(response => response.ok ? response.text() : Promise.reject('Failed to list codes'))
.then(html => {
  console.log('Access code list HTML loaded.');
  // Update UI with HTML
})
.catch(error => console.error('Error:', error));</code></pre>
                    </div>
                </div>
            </div>
        </section>

         <section class="endpoint" id="endpoint-add-access-code" data-searchable-text="access codes add create post generate expiration">
             <div class="endpoint-header">
                <span class="method-badge post">POST</span>
                <h3>Add Access Code</h3>
                <code class="endpoint-url">/dashboard/add-access-code</code>
            </div>
            <p>Adds a new access code to the system.</p>

            <h4>Authentication</h4>
            <p>Requires a valid session cookie.</p>

            <h4>Request Body (Form Data)</h4>
            <table class="parameters-table">
                 <thead><tr><th>Parameter</th><th>Type</th><th>Description</th></tr></thead>
                <tbody>
                    <tr><td><code>code</code></td><td><span class="param-type">string</span></td><td><span class="param-required">Required.</span> The access code string to add.</td></tr>
                    <tr><td><code>expiration_days</code></td><td><span class="param-type">integer</span></td><td>Optional. Number of days from now until the code expires. If omitted, might default to a system setting or never expire.</td></tr>
                </tbody>
            </table>

            <h4>Response</h4>
            <p>Redirects to the access codes list page (<code>/dashboard/access-codes</code>) with a success or error message.</p>

            <div class="code-samples">
                 <div class="code-tabs">
                    <button class="code-tab active" data-target="add-code-curl">cURL</button>
                    <button class="code-tab" data-target="add-code-fetch">Fetch</button>
                </div>
                <div class="code-content">
                    <div class="code-block active" id="add-code-curl">
                        <pre><code class="language-bash">curl -X POST http://localhost:5001/dashboard/add-access-code \
 -b cookies.txt \
 -d "code=ABC123&amp;expiration_days=30"</code></pre>
                    </div>
                     <div class="code-block" id="add-code-fetch">
                        <pre><code class="language-javascript">const formData = new URLSearchParams();
formData.append('code', 'ABC123');
formData.append('expiration_days', '30');

fetch('/dashboard/add-access-code', {
  method: 'POST',
  credentials: 'include',
  headers: {
    'Content-Type': 'application/x-www-form-urlencoded',
  },
  body: formData,
  redirect: 'follow'
})
.then(response => {
  if (response.ok && response.url.includes('/dashboard/access-codes')) {
     console.log('Access code added, redirected.');
     // Reload page or check flash messages
  } else {
     console.error('Failed to add access code.');
  }
})
.catch(error => console.error('Error:', error));</code></pre>
                    </div>
                </div>
            </div>
        </section>

         <section class="endpoint" id="endpoint-upload-access-codes" data-searchable-text="access codes upload bulk import post file csv txt expiration">
             <div class="endpoint-header">
                <span class="method-badge post">POST</span>
                <h3>Upload Access Codes</h3>
                <code class="endpoint-url">/dashboard/upload-access-codes</code>
            </div>
            <p>Uploads a file (CSV or TXT) containing multiple access codes to add in bulk.</p>

            <h4>Authentication</h4>
            <p>Requires a valid session cookie.</p>

            <h4>Request Body (Form Data / multipart)</h4>
            <table class="parameters-table">
                 <thead><tr><th>Parameter</th><th>Type</th><th>Description</th></tr></thead>
                <tbody>
                    <tr><td><code>file</code></td><td><span class="param-type">file</span></td><td><span class="param-required">Required.</span> The CSV or TXT file containing access codes (typically one per line).</td></tr>
                    <tr><td><code>expiration_days</code></td><td><span class="param-type">integer</span></td><td>Optional. Number of days until the uploaded codes expire. Applies to all codes in the file.</td></tr>
                </tbody>
            </table>

            <h4>Response</h4>
            <p>Redirects to the access codes list page (<code>/dashboard/access-codes</code>) with a success or error message indicating how many codes were processed.</p>

            <div class="code-samples">
                 <div class="code-tabs">
                    <button class="code-tab active" data-target="upload-codes-curl">cURL</button>
                    <button class="code-tab" data-target="upload-codes-fetch">Fetch</button>
                </div>
                <div class="code-content">
                    <div class="code-block active" id="upload-codes-curl">
                        <pre><code class="language-bash"># Ensure you have a file named codes.csv in the current directory
curl -X POST http://localhost:5001/dashboard/upload-access-codes \
 -b cookies.txt \
 -F "file=@codes.csv" \
 -F "expiration_days=30"</code></pre>
                    </div>
                     <div class="code-block" id="upload-codes-fetch">
                        <pre><code class="language-javascript">// Assuming 'fileInput' is an &lt;input type="file"&gt; element
const fileInput = document.getElementById('yourFileInputId');
const file = fileInput.files[0];
const expirationDays = '30'; // Or get from another input

const formData = new FormData();
formData.append('file', file);
formData.append('expiration_days', expirationDays);

fetch('/dashboard/upload-access-codes', {
  method: 'POST',
  credentials: 'include',
  body: formData, // No 'Content-Type' header needed, browser sets it for FormData
  redirect: 'follow'
})
.then(response => {
  if (response.ok && response.url.includes('/dashboard/access-codes')) {
     console.log('Access code file uploaded, redirected.');
     // Reload page or check flash messages
  } else {
     console.error('Failed to upload access codes.');
     // May need to handle error response body if available
  }
})
.catch(error => console.error('Error:', error));</code></pre>
                    </div>
                </div>
            </div>
        </section>

         <section class="endpoint" id="endpoint-edit-access-code" data-searchable-text="access codes edit update post expiration">
             <div class="endpoint-header">
                <span class="method-badge post">POST</span>
                <h3>Edit Access Code</h3>
                <code class="endpoint-url">/dashboard/edit-access-code</code>
            </div>
            <p>Updates the properties (e.g., expiration date) of an existing access code.</p>

            <h4>Authentication</h4>
            <p>Requires a valid session cookie.</p>

            <h4>Request Body (Form Data)</h4>
             <table class="parameters-table">
                 <thead><tr><th>Parameter</th><th>Type</th><th>Description</th></tr></thead>
                <tbody>
                    <tr><td><code>code</code></td><td><span class="param-type">string</span></td><td><span class="param-required">Required.</span> The access code string to edit.</td></tr>
                    <tr><td><code>expiration_days</code></td><td><span class="param-type">integer</span></td><td>Optional. New number of days from now until the code expires. Can be used to extend or shorten the validity.</td></tr>
                    <!-- Add other editable fields if applicable -->
                </tbody>
            </table>

            <h4>Response</h4>
            <p>Redirects to the access codes list page (<code>/dashboard/access-codes</code>) with a success or error message.</p>

            <div class="code-samples">
                 <div class="code-tabs">
                    <button class="code-tab active" data-target="edit-code-curl">cURL</button>
                    <button class="code-tab" data-target="edit-code-fetch">Fetch</button>
                </div>
                <div class="code-content">
                    <div class="code-block active" id="edit-code-curl">
                        <pre><code class="language-bash">curl -X POST http://localhost:5001/dashboard/edit-access-code \
 -b cookies.txt \
 -d "code=ABC123&amp;expiration_days=60"</code></pre>
                    </div>
                     <div class="code-block" id="edit-code-fetch">
                        <pre><code class="language-javascript">const formData = new URLSearchParams();
formData.append('code', 'ABC123');
formData.append('expiration_days', '60');

fetch('/dashboard/edit-access-code', {
  method: 'POST',
  credentials: 'include',
  headers: {
    'Content-Type': 'application/x-www-form-urlencoded',
  },
  body: formData,
  redirect: 'follow'
})
.then(response => {
  if (response.ok && response.url.includes('/dashboard/access-codes')) {
     console.log('Access code edited, redirected.');
  } else {
     console.error('Failed to edit access code.');
  }
})
.catch(error => console.error('Error:', error));</code></pre>
                    </div>
                </div>
            </div>
        </section>

         <section class="endpoint" id="endpoint-delete-access-code" data-searchable-text="access codes delete remove post json">
             <div class="endpoint-header">
                <span class="method-badge post">POST</span> <!-- Consider DELETE method if applicable -->
                <h3>Delete Access Code</h3>
                <code class="endpoint-url">/dashboard/delete-access-code</code>
            </div>
            <p>Deletes a single access code.</p>

            <h4>Authentication</h4>
            <p>Requires a valid session cookie.</p>

            <h4>Request Body (JSON)</h4>
            <pre><code class="language-json">{
  "code": "ABC123"
}</code></pre>
            <table class="parameters-table">
                 <thead><tr><th>Parameter</th><th>Type</th><th>Description</th></tr></thead>
                <tbody>
                    <tr><td><code>code</code></td><td><span class="param-type">string</span></td><td><span class="param-required">Required.</span> The access code to delete.</td></tr>
                </tbody>
            </table>

            <h4>Response</h4>
            <p>Returns a JSON response indicating success or failure.</p>
             <pre><code class="language-json">{
  "success": true,
  "message": "Access code deleted successfully."
}
// Or on error:
{
  "success": false,
  "error": "Failed to delete access code. Reason..."
}</code></pre>


            <div class="code-samples">
                 <div class="code-tabs">
                    <button class="code-tab active" data-target="delete-code-curl">cURL</button>
                    <button class="code-tab" data-target="delete-code-fetch">Fetch</button>
                </div>
                <div class="code-content">
                    <div class="code-block active" id="delete-code-curl">
                        <pre><code class="language-bash">curl -X POST http://localhost:5001/dashboard/delete-access-code \
 -b cookies.txt \
 -H "Content-Type: application/json" \
 -d '{"code":"ABC123"}'</code></pre>
                    </div>
                     <div class="code-block" id="delete-code-fetch">
                        <pre><code class="language-javascript">const requestBody = {
  code: 'ABC123'
};

fetch('/dashboard/delete-access-code', {
  method: 'POST',
  credentials: 'include',
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  body: JSON.stringify(requestBody)
})
.then(response => response.json())
.then(data => {
  if (data.success) {
    console.log('Access code deleted:', data.message);
    // Update UI (e.g., remove code from list)
  } else {
    console.error('Failed to delete code:', data.error);
    // Show error message
  }
})
.catch(error => console.error('Error:', error));</code></pre>
                    </div>
                </div>
            </div>
        </section>

         <section class="endpoint" id="endpoint-bulk-delete-access-codes" data-searchable-text="access codes delete remove bulk post json array">
             <div class="endpoint-header">
                <span class="method-badge post">POST</span> <!-- Consider DELETE method if applicable -->
                <h3>Bulk Delete Access Codes</h3>
                <code class="endpoint-url">/dashboard/bulk-delete-access-codes</code>
            </div>
            <p>Deletes multiple access codes in a single request.</p>

            <h4>Authentication</h4>
            <p>Requires a valid session cookie.</p>

            <h4>Request Body (JSON)</h4>
            <pre><code class="language-json">{
  "codes": ["ABC123", "DEF456"]
}</code></pre>
             <table class="parameters-table">
                 <thead><tr><th>Parameter</th><th>Type</th><th>Description</th></tr></thead>
                <tbody>
                    <tr><td><code>codes</code></td><td><span class="param-type">array</span></td><td><span class="param-required">Required.</span> An array of access code strings to delete.</td></tr>
                </tbody>
            </table>

            <h4>Response</h4>
            <p>Returns a JSON response indicating success or failure, potentially with details on how many were deleted.</p>
              <pre><code class="language-json">{
  "success": true,
  "message": "Successfully deleted 2 codes."
  // "deleted_count": 2 // Optionally include count
}
// Or on error:
{
  "success": false,
  "error": "Failed to delete some codes. Reason..."
  // "deleted_count": 0 // Optionally include count
}</code></pre>

            <div class="code-samples">
                <div class="code-tabs">
                    <button class="code-tab active" data-target="bulk-delete-curl">cURL</button>
                    <button class="code-tab" data-target="bulk-delete-fetch">Fetch</button>
                </div>
                <div class="code-content">
                    <div class="code-block active" id="bulk-delete-curl">
                        <pre><code class="language-bash">curl -X POST http://localhost:5001/dashboard/bulk-delete-access-codes \
 -b cookies.txt \
 -H "Content-Type: application/json" \
 -d '{"codes":["ABC123","DEF456"]}'</code></pre>
                    </div>
                    <div class="code-block" id="bulk-delete-fetch">
                        <pre><code class="language-javascript">const requestBody = {
  codes: ["ABC123", "DEF456"]
};

fetch('/dashboard/bulk-delete-access-codes', {
  method: 'POST',
  credentials: 'include',
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  body: JSON.stringify(requestBody)
})
.then(response => response.json())
.then(data => {
  if (data.success) {
    console.log('Bulk delete successful:', data.message);
    // Update UI
  } else {
    console.error('Bulk delete failed:', data.error);
    // Show error
  }
})
.catch(error => console.error('Error:', error));</code></pre>
                    </div>
                </div>
            </div>
        </section>

         <section class="endpoint" id="endpoint-access-code-details" data-searchable-text="access codes details get view code info json">
             <div class="endpoint-header">
                <span class="method-badge get">GET</span>
                <h3>Access Code Details</h3>
                <code class="endpoint-url">/dashboard/access-code-details/&lt;code&gt;</code>
            </div>
            <p>Retrieves detailed information about a specific access code.</p>

            <h4>Authentication</h4>
            <p>Requires a valid session cookie.</p>

             <h4>Path Parameters</h4>
            <table class="parameters-table">
                 <thead><tr><th>Parameter</th><th>Type</th><th>Description</th></tr></thead>
                <tbody>
                    <tr><td><code>code</code></td><td><span class="param-type">string</span></td><td><span class="param-required">Required.</span> The access code string to get details for.</td></tr>
                </tbody>
            </table>

            <h4>Response</h4>
            <p>Returns a JSON object containing the details of the access code.</p>
            <pre><code class="language-json">{
  "code": "ABC123",
  "source": "manually_added",
  "added_by": "admin_id_example",
  "added_at": "2023-01-01T00:00:00Z", // ISODate format
  "expiration_date": "2023-02-01T00:00:00Z", // ISODate format
  "used_by_user_id": null, // or user_id if used
  "used_at": null, // or ISODate if used
  // ... other relevant fields from the Access Code Model
  "success": true
}
// Or if not found:
{
  "success": false,
  "error": "Access code not found."
}</code></pre>

            <div class="code-samples">
                 <div class="code-tabs">
                    <button class="code-tab active" data-target="code-details-curl">cURL</button>
                    <button class="code-tab" data-target="code-details-fetch">Fetch</button>
                </div>
                <div class="code-content">
                    <div class="code-block active" id="code-details-curl">
                        <pre><code class="language-bash">curl -X GET http://localhost:5001/dashboard/access-code-details/ABC123 \
 -b cookies.txt \
 -H "Accept: application/json"</code></pre>
                    </div>
                     <div class="code-block" id="code-details-fetch">
                        <pre><code class="language-javascript">const accessCode = 'ABC123';
fetch(`/dashboard/access-code-details/${accessCode}`, {
  method: 'GET',
  credentials: 'include',
  headers: {
    'Accept': 'application/json'
  }
})
.then(response => response.json())
.then(data => {
  if (data.success) {
    console.log('Access Code Details:', data);
    // Use the details (e.g., display in a modal)
  } else {
    console.error('Failed to get code details:', data.error);
    // Handle error (e.g., code not found)
  }
})
.catch(error => console.error('Error:', error));</code></pre>
                    </div>
                </div>
            </div>
        </section>

         <section class="endpoint" id="endpoint-check-code-in-use" data-searchable-text="access codes check usage status get json code">
             <div class="endpoint-header">
                <span class="method-badge get">GET</span>
                <h3>Check If Code In Use</h3>
                <code class="endpoint-url">/dashboard/check-code-in-use/&lt;code&gt;</code>
            </div>
            <p>Checks if a specific access code has already been used (registered by a user).</p>

            <h4>Authentication</h4>
            <p>Requires a valid session cookie.</p>

             <h4>Path Parameters</h4>
            <table class="parameters-table">
                 <thead><tr><th>Parameter</th><th>Type</th><th>Description</th></tr></thead>
                <tbody>
                    <tr><td><code>code</code></td><td><span class="param-type">string</span></td><td><span class="param-required">Required.</span> The access code string to check.</td></tr>
                </tbody>
            </table>

            <h4>Response</h4>
            <p>Returns a JSON response indicating whether the code is in use.</p>
            <pre><code class="language-json">{
  "in_use": true, // or false
  "user_id": 123456789, // ID of user if in_use is true, null otherwise
  "success": true
}
// Or if code doesn't exist or other error:
{
  "success": false,
  "error": "Access code not found." // Or other error message
}</code></pre>

            <div class="code-samples">
                <div class="code-tabs">
                    <button class="code-tab active" data-target="check-code-curl">cURL</button>
                    <button class="code-tab" data-target="check-code-fetch">Fetch</button>
                </div>
                <div class="code-content">
                    <div class="code-block active" id="check-code-curl">
                        <pre><code class="language-bash">curl -X GET http://localhost:5001/dashboard/check-code-in-use/ABC123 \
 -b cookies.txt \
 -H "Accept: application/json"</code></pre>
                    </div>
                     <div class="code-block" id="check-code-fetch">
                        <pre><code class="language-javascript">const accessCode = 'ABC123';
fetch(`/dashboard/check-code-in-use/${accessCode}`, {
  method: 'GET',
  credentials: 'include',
  headers: {
    'Accept': 'application/json'
  }
})
.then(response => response.json())
.then(data => {
  if (data.success) {
    console.log(`Code in use: ${data.in_use}`);
    if (data.in_use) {
        console.log(`Used by User ID: ${data.user_id}`);
    }
    // Update UI based on usage status
  } else {
    console.error('Failed to check code usage:', data.error);
  }
})
.catch(error => console.error('Error:', error));</code></pre>
                    </div>
                </div>
            </div>
        </section>


        <!-- ========================== -->
        <!-- Request Management APIs    -->
        <!-- ========================== -->
        <h2 id="category-request-management">Request Management</h2>

        <section class="endpoint" id="endpoint-list-verification-requests" data-searchable-text="requests verification list get pending review">
             <div class="endpoint-header">
                <span class="method-badge get">GET</span>
                <h3>List Verification Requests</h3>
                <code class="endpoint-url">/dashboard/verification-requests</code>
            </div>
            <p>Retrieves the page listing pending user verification requests.</p>

            <h4>Authentication</h4>
            <p>Requires a valid session cookie.</p>

            <h4>Response</h4>
            <p>Returns an HTML page displaying the list of verification requests.</p>

            <div class="code-samples">
                <div class="code-tabs">
                    <button class="code-tab active" data-target="list-verify-req-curl">cURL</button>
                    <button class="code-tab" data-target="list-verify-req-fetch">Fetch</button>
                </div>
                <div class="code-content">
                    <div class="code-block active" id="list-verify-req-curl">
                        <pre><code class="language-bash">curl -X GET http://localhost:5001/dashboard/verification-requests \
 -b cookies.txt</code></pre>
                    </div>
                    <div class="code-block" id="list-verify-req-fetch">
                        <pre><code class="language-javascript">fetch('/dashboard/verification-requests', {
  method: 'GET',
  credentials: 'include'
})
.then(response => response.ok ? response.text() : Promise.reject('Failed to load verification requests'))
.then(html => {
  console.log('Verification requests HTML loaded.');
  // Update UI
})
.catch(error => console.error('Error:', error));</code></pre>
                    </div>
                </div>
            </div>
        </section>

        <section class="endpoint" id="endpoint-list-support-requests" data-searchable-text="requests support list get helpdesk ticket query">
             <div class="endpoint-header">
                <span class="method-badge get">GET</span>
                <h3>List Support Requests</h3>
                <code class="endpoint-url">/dashboard/support-requests</code>
            </div>
            <p>Retrieves the page listing pending user support requests.</p>

            <h4>Authentication</h4>
            <p>Requires a valid session cookie.</p>

            <h4>Response</h4>
            <p>Returns an HTML page displaying the list of support requests.</p>

            <div class="code-samples">
                 <div class="code-tabs">
                    <button class="code-tab active" data-target="list-support-req-curl">cURL</button>
                    <button class="code-tab" data-target="list-support-req-fetch">Fetch</button>
                </div>
                <div class="code-content">
                    <div class="code-block active" id="list-support-req-curl">
                        <pre><code class="language-bash">curl -X GET http://localhost:5001/dashboard/support-requests \
 -b cookies.txt</code></pre>
                    </div>
                     <div class="code-block" id="list-support-req-fetch">
                        <pre><code class="language-javascript">fetch('/dashboard/support-requests', {
  method: 'GET',
  credentials: 'include'
})
.then(response => response.ok ? response.text() : Promise.reject('Failed to load support requests'))
.then(html => {
  console.log('Support requests HTML loaded.');
  // Update UI
})
.catch(error => console.error('Error:', error));</code></pre>
                    </div>
                </div>
            </div>
        </section>

        <section class="endpoint" id="endpoint-verification-request-details" data-searchable-text="requests verification details get json request_id view">
             <div class="endpoint-header">
                <span class="method-badge get">GET</span>
                <h3>Verification Request Details</h3>
                <code class="endpoint-url">/dashboard/verification-request-details/&lt;request_id&gt;</code>
            </div>
            <p>Retrieves detailed information about a specific verification request.</p>

            <h4>Authentication</h4>
            <p>Requires a valid session cookie.</p>

             <h4>Path Parameters</h4>
            <table class="parameters-table">
                 <thead><tr><th>Parameter</th><th>Type</th><th>Description</th></tr></thead>
                <tbody>
                    <tr><td><code>request_id</code></td><td><span class="param-type">string</span></td><td><span class="param-required">Required.</span> The unique ID of the verification request.</td></tr>
                </tbody>
            </table>

            <h4>Response</h4>
            <p>Returns a JSON object containing the details of the verification request.</p>
             <pre><code class="language-json">{
  "request_id": "REQ123",
  "user_id": 7657876053,
  "access_code": "ABC123",
  "created_at": "2023-01-01T00:00:00Z",
  "status": "pending",
  "admin_notes": [
    {
      "admin_id": "admin_user",
      "admin_name": "Admin Name",
      "note": "This is an admin note",
      "timestamp": "2023-01-02T00:00:00Z" // ISODate
    }
    // ... more notes
  ],
  // ... other fields from Verification Request Model
  "success": true
}
// Or if not found:
{
  "success": false,
  "error": "Verification request not found."
}</code></pre>

            <div class="code-samples">
                <div class="code-tabs">
                    <button class="code-tab active" data-target="verify-details-curl">cURL</button>
                    <button class="code-tab" data-target="verify-details-fetch">Fetch</button>
                </div>
                <div class="code-content">
                    <div class="code-block active" id="verify-details-curl">
                        <pre><code class="language-bash">curl -X GET http://localhost:5001/dashboard/verification-request-details/REQ123 \
 -b cookies.txt \
 -H "Accept: application/json"</code></pre>
                    </div>
                     <div class="code-block" id="verify-details-fetch">
                        <pre><code class="language-javascript">const requestId = 'REQ123';
fetch(`/dashboard/verification-request-details/${requestId}`, {
  method: 'GET',
  credentials: 'include',
  headers: {
    'Accept': 'application/json'
  }
})
.then(response => response.json())
.then(data => {
  if (data.success) {
    console.log('Verification Request Details:', data);
    // Display details
  } else {
    console.error('Failed to get details:', data.error);
  }
})
.catch(error => console.error('Error:', error));</code></pre>
                    </div>
                </div>
            </div>
        </section>

        <section class="endpoint" id="endpoint-support-request-details" data-searchable-text="requests support details get json request_id view ticket">
             <div class="endpoint-header">
                <span class="method-badge get">GET</span>
                <h3>Support Request Details</h3>
                <code class="endpoint-url">/dashboard/support-request-details/&lt;request_id&gt;</code>
            </div>
            <p>Retrieves detailed information about a specific support request.</p>

            <h4>Authentication</h4>
            <p>Requires a valid session cookie.</p>

             <h4>Path Parameters</h4>
            <table class="parameters-table">
                 <thead><tr><th>Parameter</th><th>Type</th><th>Description</th></tr></thead>
                <tbody>
                    <tr><td><code>request_id</code></td><td><span class="param-type">string</span></td><td><span class="param-required">Required.</span> The unique ID of the support request.</td></tr>
                </tbody>
            </table>

            <h4>Response</h4>
            <p>Returns a JSON object containing the details of the support request.</p>
             <pre><code class="language-json">{
  "request_id": "SUP123",
  "user_id": 7657876053,
  "user_details": {
    "name": "User Name",
    "email": "<EMAIL>",
    "whatsapp": "+1234567890"
   },
  "query": "I need help with login",
  "created_at": "2023-01-01T00:00:00Z",
  "status": "pending", // or "resolved"
  "resolved_at": null, // or ISODate if resolved
  "resolved_by": null, // or admin_id if resolved
  // ... other fields from Support Request Model
  "success": true
}
// Or if not found:
{
  "success": false,
  "error": "Support request not found."
}</code></pre>

            <div class="code-samples">
                <div class="code-tabs">
                    <button class="code-tab active" data-target="support-details-curl">cURL</button>
                    <button class="code-tab" data-target="support-details-fetch">Fetch</button>
                </div>
                <div class="code-content">
                    <div class="code-block active" id="support-details-curl">
                        <pre><code class="language-bash">curl -X GET http://localhost:5001/dashboard/support-request-details/SUP123 \
 -b cookies.txt \
 -H "Accept: application/json"</code></pre>
                    </div>
                    <div class="code-block" id="support-details-fetch">
                        <pre><code class="language-javascript">const requestId = 'SUP123';
fetch(`/dashboard/support-request-details/${requestId}`, {
  method: 'GET',
  credentials: 'include',
  headers: {
    'Accept': 'application/json'
  }
})
.then(response => response.json())
.then(data => {
  if (data.success) {
    console.log('Support Request Details:', data);
    // Display details
  } else {
    console.error('Failed to get details:', data.error);
  }
})
.catch(error => console.error('Error:', error));</code></pre>
                    </div>
                </div>
            </div>
        </section>

        <section class="endpoint" id="endpoint-resolve-support-request" data-searchable-text="requests support resolve post close ticket request_id">
             <div class="endpoint-header">
                <span class="method-badge post">POST</span>
                <h3>Resolve Support Request</h3>
                <code class="endpoint-url">/dashboard/resolve-support-request/&lt;request_id&gt;</code>
            </div>
            <p>Marks a specific support request as resolved.</p>

            <h4>Authentication</h4>
            <p>Requires a valid session cookie.</p>

             <h4>Path Parameters</h4>
            <table class="parameters-table">
                 <thead><tr><th>Parameter</th><th>Type</th><th>Description</th></tr></thead>
                <tbody>
                    <tr><td><code>request_id</code></td><td><span class="param-type">string</span></td><td><span class="param-required">Required.</span> The unique ID of the support request to resolve.</td></tr>
                </tbody>
            </table>

            <h4>Response</h4>
            <p>Redirects to the support requests list page (<code>/dashboard/support-requests</code>) with a success or error message.</p>

            <div class="code-samples">
                 <div class="code-tabs">
                    <button class="code-tab active" data-target="resolve-support-curl">cURL</button>
                    <button class="code-tab" data-target="resolve-support-fetch">Fetch</button>
                </div>
                <div class="code-content">
                    <div class="code-block active" id="resolve-support-curl">
                        <pre><code class="language-bash">curl -X POST http://localhost:5001/dashboard/resolve-support-request/SUP123 \
 -b cookies.txt</code></pre>
                    </div>
                     <div class="code-block" id="resolve-support-fetch">
                        <pre><code class="language-javascript">const requestId = 'SUP123';
fetch(`/dashboard/resolve-support-request/${requestId}`, {
  method: 'POST',
  credentials: 'include',
  redirect: 'follow'
})
.then(response => {
  if (response.ok && response.url.includes('/dashboard/support-requests')) {
     console.log('Support request resolved, redirected.');
     // Reload page or check flash messages
  } else {
     console.error('Failed to resolve support request.');
  }
})
.catch(error => console.error('Error:', error));</code></pre>
                    </div>
                </div>
            </div>
        </section>

        <section class="endpoint" id="endpoint-approve-verification-request" data-searchable-text="requests verification approve post confirm request_id">
             <div class="endpoint-header">
                <span class="method-badge post">POST</span>
                <h3>Approve Verification Request</h3>
                <code class="endpoint-url">/dashboard/verification-requests/approve/&lt;request_id&gt;</code>
            </div>
            <p>Approves a pending user verification request.</p>

            <h4>Authentication</h4>
            <p>Requires a valid session cookie.</p>

             <h4>Path Parameters</h4>
            <table class="parameters-table">
                 <thead><tr><th>Parameter</th><th>Type</th><th>Description</th></tr></thead>
                <tbody>
                    <tr><td><code>request_id</code></td><td><span class="param-type">string</span></td><td><span class="param-required">Required.</span> The unique ID of the verification request to approve.</td></tr>
                </tbody>
            </table>

            <h4>Response</h4>
            <p>Redirects to the verification requests list page (<code>/dashboard/verification-requests</code>) with a success or error message.</p>

            <div class="code-samples">
                 <div class="code-tabs">
                    <button class="code-tab active" data-target="approve-verify-curl">cURL</button>
                    <button class="code-tab" data-target="approve-verify-fetch">Fetch</button>
                </div>
                <div class="code-content">
                    <div class="code-block active" id="approve-verify-curl">
                        <pre><code class="language-bash">curl -X POST http://localhost:5001/dashboard/verification-requests/approve/REQ123 \
 -b cookies.txt</code></pre>
                    </div>
                     <div class="code-block" id="approve-verify-fetch">
                        <pre><code class="language-javascript">const requestId = 'REQ123';
fetch(`/dashboard/verification-requests/approve/${requestId}`, {
  method: 'POST',
  credentials: 'include',
  redirect: 'follow'
})
.then(response => {
  if (response.ok && response.url.includes('/dashboard/verification-requests')) {
     console.log('Verification request approved, redirected.');
  } else {
     console.error('Failed to approve verification request.');
  }
})
.catch(error => console.error('Error:', error));</code></pre>
                    </div>
                </div>
            </div>
        </section>

        <section class="endpoint" id="endpoint-deny-verification-request" data-searchable-text="requests verification deny post reject request_id">
             <div class="endpoint-header">
                <span class="method-badge post">POST</span>
                <h3>Deny Verification Request</h3>
                <code class="endpoint-url">/dashboard/verification-requests/deny/&lt;request_id&gt;</code>
            </div>
            <p>Denies a pending user verification request.</p>

            <h4>Authentication</h4>
            <p>Requires a valid session cookie.</p>

             <h4>Path Parameters</h4>
            <table class="parameters-table">
                 <thead><tr><th>Parameter</th><th>Type</th><th>Description</th></tr></thead>
                <tbody>
                    <tr><td><code>request_id</code></td><td><span class="param-type">string</span></td><td><span class="param-required">Required.</span> The unique ID of the verification request to deny.</td></tr>
                </tbody>
            </table>

            <h4>Response</h4>
            <p>Redirects to the verification requests list page (<code>/dashboard/verification-requests</code>) with a success or error message.</p>

            <div class="code-samples">
                <div class="code-tabs">
                    <button class="code-tab active" data-target="deny-verify-curl">cURL</button>
                    <button class="code-tab" data-target="deny-verify-fetch">Fetch</button>
                </div>
                <div class="code-content">
                    <div class="code-block active" id="deny-verify-curl">
                        <pre><code class="language-bash">curl -X POST http://localhost:5001/dashboard/verification-requests/deny/REQ123 \
 -b cookies.txt</code></pre>
                    </div>
                    <div class="code-block" id="deny-verify-fetch">
                        <pre><code class="language-javascript">const requestId = 'REQ123';
fetch(`/dashboard/verification-requests/deny/${requestId}`, {
  method: 'POST',
  credentials: 'include',
  redirect: 'follow'
})
.then(response => {
  if (response.ok && response.url.includes('/dashboard/verification-requests')) {
     console.log('Verification request denied, redirected.');
  } else {
     console.error('Failed to deny verification request.');
  }
})
.catch(error => console.error('Error:', error));</code></pre>
                    </div>
                </div>
            </div>
        </section>

        <section class="endpoint" id="endpoint-add-verification-note" data-searchable-text="requests verification note add comment post json request_id">
             <div class="endpoint-header">
                <span class="method-badge post">POST</span>
                <h3>Add Verification Request Note</h3>
                <code class="endpoint-url">/dashboard/verification-request/add-note/&lt;request_id&gt;</code>
            </div>
            <p>Adds an administrative note to a specific verification request.</p>

            <h4>Authentication</h4>
            <p>Requires a valid session cookie.</p>

             <h4>Path Parameters</h4>
            <table class="parameters-table">
                 <thead><tr><th>Parameter</th><th>Type</th><th>Description</th></tr></thead>
                <tbody>
                    <tr><td><code>request_id</code></td><td><span class="param-type">string</span></td><td><span class="param-required">Required.</span> The unique ID of the verification request to add a note to.</td></tr>
                </tbody>
            </table>

            <h4>Request Body (JSON)</h4>
            <pre><code class="language-json">{
  "note": "This is an admin note"
}</code></pre>
             <table class="parameters-table">
                 <thead><tr><th>Parameter</th><th>Type</th><th>Description</th></tr></thead>
                <tbody>
                    <tr><td><code>note</code></td><td><span class="param-type">string</span></td><td><span class="param-required">Required.</span> The text content of the note to add.</td></tr>
                </tbody>
            </table>

            <h4>Response</h4>
            <p>Returns a JSON response indicating success or failure.</p>
             <pre><code class="language-json">{
  "success": true,
  "message": "Note added successfully."
}
// Or on error:
{
  "success": false,
  "error": "Failed to add note. Reason..."
}</code></pre>

            <div class="code-samples">
                <div class="code-tabs">
                    <button class="code-tab active" data-target="add-note-curl">cURL</button>
                    <button class="code-tab" data-target="add-note-fetch">Fetch</button>
                </div>
                <div class="code-content">
                    <div class="code-block active" id="add-note-curl">
                        <pre><code class="language-bash">curl -X POST http://localhost:5001/dashboard/verification-request/add-note/REQ123 \
 -b cookies.txt \
 -H "Content-Type: application/json" \
 -d '{"note":"This is an admin note"}'</code></pre>
                    </div>
                     <div class="code-block" id="add-note-fetch">
                        <pre><code class="language-javascript">const requestId = 'REQ123';
const requestBody = {
  note: 'This is an admin note'
};

fetch(`/dashboard/verification-request/add-note/${requestId}`, {
  method: 'POST',
  credentials: 'include',
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  body: JSON.stringify(requestBody)
})
.then(response => response.json())
.then(data => {
  if (data.success) {
    console.log('Note added:', data.message);
    // Refresh notes section in UI or show success message
  } else {
    console.error('Failed to add note:', data.error);
  }
})
.catch(error => console.error('Error:', error));</code></pre>
                    </div>
                </div>
            </div>
        </section>

        <section class="endpoint" id="endpoint-send-support-notification" data-searchable-text="requests support notification send message telegram post json user_id request_id status">
             <div class="endpoint-header">
                <span class="method-badge post">POST</span>
                <h3>Send Support Notification</h3>
                <code class="endpoint-url">/dashboard/send-support-notification</code>
            </div>
            <p>Sends a notification message to a user via Telegram regarding their support request.</p>

            <h4>Authentication</h4>
            <p>Requires a valid session cookie.</p>

            <h4>Request Body (JSON)</h4>
             <pre><code class="language-json">{
  "user_id": 123456789,
  "request_id": "SUP123",
  "status": "Resolved",
  "message": "Your issue has been resolved.",
  "query_summary": "Login problem"
}</code></pre>
            <table class="parameters-table">
                 <thead><tr><th>Parameter</th><th>Type</th><th>Description</th></tr></thead>
                <tbody>
                    <tr><td><code>user_id</code></td><td><span class="param-type">integer/string</span></td><td><span class="param-required">Required.</span> The Telegram User ID of the user to notify.</td></tr>
                    <tr><td><code>request_id</code></td><td><span class="param-type">string</span></td><td><span class="param-required">Required.</span> The ID of the related support request.</td></tr>
                    <tr><td><code>status</code></td><td><span class="param-type">string</span></td><td><span class="param-required">Required.</span> The current status of the request (e.g., "Resolved", "Pending Info", "Closed").</td></tr>
                    <tr><td><code>message</code></td><td><span class="param-type">string</span></td><td><span class="param-required">Required.</span> The message content to send to the user.</td></tr>
                    <tr><td><code>query_summary</code></td><td><span class="param-type">string</span></td><td>Optional. A brief summary of the user's original query for context in the message.</td></tr>
                </tbody>
            </table>

            <h4>Response</h4>
            <p>Returns a JSON response indicating success or failure of sending the message.</p>
             <pre><code class="language-json">{
  "success": true,
  "message": "Notification sent successfully."
}
// Or on error:
{
  "success": false,
  "error": "Failed to send notification. Reason..."
}</code></pre>

            <div class="code-samples">
                 <div class="code-tabs">
                    <button class="code-tab active" data-target="send-notify-curl">cURL</button>
                    <button class="code-tab" data-target="send-notify-fetch">Fetch</button>
                </div>
                <div class="code-content">
                    <div class="code-block active" id="send-notify-curl">
                        <pre><code class="language-bash">curl -X POST http://localhost:5001/dashboard/send-support-notification \
 -b cookies.txt \
 -H "Content-Type: application/json" \
 -d '{
   "user_id": 123456789,
   "request_id": "SUP123",
   "status": "Resolved",
   "message": "Your issue has been resolved.",
   "query_summary": "Login problem"
 }'</code></pre>
                    </div>
                    <div class="code-block" id="send-notify-fetch">
                        <pre><code class="language-javascript">const requestBody = {
  user_id: 123456789,
  request_id: "SUP123",
  status: "Resolved",
  message: "Your issue has been resolved.",
  query_summary: "Login problem"
};

fetch('/dashboard/send-support-notification', {
  method: 'POST',
  credentials: 'include',
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  body: JSON.stringify(requestBody)
})
.then(response => response.json())
.then(data => {
  if (data.success) {
    console.log('Support notification sent:', data.message);
    // Show success feedback
  } else {
    console.error('Failed to send notification:', data.error);
    // Show error feedback
  }
})
.catch(error => console.error('Error:', error));</code></pre>
                    </div>
                </div>
            </div>
        </section>

        <!-- ========================== -->
        <!-- Telegram Bot Management    -->
        <!-- ========================== -->
        <h2 id="category-telegram-bots">Telegram Bot Management</h2>

         <section class="endpoint" id="endpoint-list-telegram-bots" data-searchable-text="telegram bots list get configuration settings">
             <div class="endpoint-header">
                <span class="method-badge get">GET</span>
                <h3>List Telegram Bots</h3>
                <code class="endpoint-url">/dashboard/telegram-bots</code>
            </div>
            <p>Retrieves the page listing all configured Telegram bots.</p>

            <h4>Authentication</h4>
            <p>Requires a valid session cookie.</p>

            <h4>Response</h4>
            <p>Returns an HTML page displaying the list of configured Telegram bots.</p>

            <div class="code-samples">
                <div class="code-tabs">
                    <button class="code-tab active" data-target="list-bots-curl">cURL</button>
                    <button class="code-tab" data-target="list-bots-fetch">Fetch</button>
                </div>
                <div class="code-content">
                    <div class="code-block active" id="list-bots-curl">
                        <pre><code class="language-bash">curl -X GET http://localhost:5001/dashboard/telegram-bots \
 -b cookies.txt</code></pre>
                    </div>
                    <div class="code-block" id="list-bots-fetch">
                        <pre><code class="language-javascript">fetch('/dashboard/telegram-bots', {
  method: 'GET',
  credentials: 'include'
})
.then(response => response.ok ? response.text() : Promise.reject('Failed to load bot list'))
.then(html => {
  console.log('Telegram bots list HTML loaded.');
  // Update UI
})
.catch(error => console.error('Error:', error));</code></pre>
                    </div>
                </div>
            </div>
        </section>

         <section class="endpoint" id="endpoint-add-telegram-bot" data-searchable-text="telegram bots add create post name username token channel_id">
             <div class="endpoint-header">
                <span class="method-badge post">POST</span>
                <h3>Add Telegram Bot</h3>
                <code class="endpoint-url">/dashboard/add-telegram-bot</code>
            </div>
            <p>Adds a new Telegram bot configuration to the system.</p>

            <h4>Authentication</h4>
            <p>Requires a valid session cookie.</p>

             <h4>Request Body (Form Data)</h4>
            <table class="parameters-table">
                 <thead><tr><th>Parameter</th><th>Type</th><th>Description</th></tr></thead>
                <tbody>
                    <tr><td><code>name</code></td><td><span class="param-type">string</span></td><td><span class="param-required">Required.</span> A friendly name for the bot.</td></tr>
                    <tr><td><code>username</code></td><td><span class="param-type">string</span></td><td><span class="param-required">Required.</span> The bot's Telegram username (without @).</td></tr>
                    <tr><td><code>token</code></td><td><span class="param-type">string</span></td><td><span class="param-required">Required.</span> The API token obtained from BotFather.</td></tr>
                    <tr><td><code>channel_id</code></td><td><span class="param-type">string/integer</span></td><td><span class="param-required">Required.</span> The ID of the Telegram channel associated with this bot (often starts with -100...).</td></tr>
                </tbody>
            </table>

            <h4>Response</h4>
            <p>Redirects to the Telegram bots list page (<code>/dashboard/telegram-bots</code>) with a success or error message.</p>

            <div class="code-samples">
                <div class="code-tabs">
                    <button class="code-tab active" data-target="add-bot-curl">cURL</button>
                    <button class="code-tab" data-target="add-bot-fetch">Fetch</button>
                </div>
                <div class="code-content">
                    <div class="code-block active" id="add-bot-curl">
                        <pre><code class="language-bash">curl -X POST http://localhost:5001/dashboard/add-telegram-bot \
 -b cookies.txt \
 -d "name=MyBot&amp;username=my_bot&amp;token=123456:ABC-DEF&amp;channel_id=-1001234567890"</code></pre>
                    </div>
                    <div class="code-block" id="add-bot-fetch">
                        <pre><code class="language-javascript">const formData = new URLSearchParams();
formData.append('name', 'MyBot');
formData.append('username', 'my_bot');
formData.append('token', '123456:ABC-DEF');
formData.append('channel_id', '-1001234567890');

fetch('/dashboard/add-telegram-bot', {
  method: 'POST',
  credentials: 'include',
  headers: {
    'Content-Type': 'application/x-www-form-urlencoded',
  },
  body: formData,
  redirect: 'follow'
})
.then(response => {
  if (response.ok && response.url.includes('/dashboard/telegram-bots')) {
     console.log('Telegram bot added, redirected.');
  } else {
     console.error('Failed to add Telegram bot.');
  }
})
.catch(error => console.error('Error:', error));</code></pre>
                    </div>
                </div>
            </div>
        </section>

         <section class="endpoint" id="endpoint-edit-telegram-bot" data-searchable-text="telegram bots edit update post name username token channel_id bot_id">
             <div class="endpoint-header">
                <span class="method-badge post">POST</span>
                <h3>Edit Telegram Bot</h3>
                <code class="endpoint-url">/dashboard/edit-telegram-bot/&lt;bot_id&gt;</code>
            </div>
            <p>Updates the configuration of an existing Telegram bot.</p>

            <h4>Authentication</h4>
            <p>Requires a valid session cookie.</p>

             <h4>Path Parameters</h4>
            <table class="parameters-table">
                 <thead><tr><th>Parameter</th><th>Type</th><th>Description</th></tr></thead>
                <tbody>
                    <tr><td><code>bot_id</code></td><td><span class="param-type">string</span></td><td><span class="param-required">Required.</span> The MongoDB ObjectId of the bot configuration to edit.</td></tr>
                </tbody>
            </table>

            <h4>Request Body (Form Data)</h4>
             <table class="parameters-table">
                 <thead><tr><th>Parameter</th><th>Type</th><th>Description</th></tr></thead>
                <tbody>
                    <tr><td><code>name</code></td><td><span class="param-type">string</span></td><td><span class="param-required">Required.</span> Updated friendly name for the bot.</td></tr>
                    <tr><td><code>username</code></td><td><span class="param-type">string</span></td><td><span class="param-required">Required.</span> Updated bot's Telegram username.</td></tr>
                    <tr><td><code>token</code></td><td><span class="param-type">string</span></td><td><span class="param-required">Required.</span> Updated API token.</td></tr>
                    <tr><td><code>channel_id</code></td><td><span class="param-type">string/integer</span></td><td><span class="param-required">Required.</span> Updated associated Telegram channel ID.</td></tr>
                     <!-- Add other editable fields if applicable -->
                </tbody>
            </table>

            <h4>Response</h4>
            <p>Redirects to the Telegram bots list page (<code>/dashboard/telegram-bots</code>) with a success or error message.</p>

            <div class="code-samples">
                 <div class="code-tabs">
                    <button class="code-tab active" data-target="edit-bot-curl">cURL</button>
                    <button class="code-tab" data-target="edit-bot-fetch">Fetch</button>
                </div>
                <div class="code-content">
                    <div class="code-block active" id="edit-bot-curl">
                        <pre><code class="language-bash">curl -X POST http://localhost:5001/dashboard/edit-telegram-bot/60a1b2c3d4e5f6g7h8i9j0k \
 -b cookies.txt \
 -d "name=UpdatedBot&amp;username=updated_bot&amp;token=123456:ABC-DEF&amp;channel_id=-1001234567890"</code></pre>
                    </div>
                    <div class="code-block" id="edit-bot-fetch">
                        <pre><code class="language-javascript">const botId = '60a1b2c3d4e5f6g7h8i9j0k';
const formData = new URLSearchParams();
formData.append('name', 'UpdatedBot');
formData.append('username', 'updated_bot');
formData.append('token', '123456:ABC-DEF');
formData.append('channel_id', '-1001234567890');

fetch(`/dashboard/edit-telegram-bot/${botId}`, {
  method: 'POST',
  credentials: 'include',
  headers: {
    'Content-Type': 'application/x-www-form-urlencoded',
  },
  body: formData,
  redirect: 'follow'
})
.then(response => {
  if (response.ok && response.url.includes('/dashboard/telegram-bots')) {
     console.log('Telegram bot updated, redirected.');
  } else {
     console.error('Failed to update Telegram bot.');
  }
})
.catch(error => console.error('Error:', error));</code></pre>
                    </div>
                </div>
            </div>
        </section>

         <section class="endpoint" id="endpoint-delete-telegram-bot" data-searchable-text="telegram bots delete remove post bot_id">
             <div class="endpoint-header">
                <span class="method-badge post">POST</span> <!-- Consider DELETE method -->
                <h3>Delete Telegram Bot</h3>
                <code class="endpoint-url">/dashboard/delete-telegram-bot/&lt;bot_id&gt;</code>
            </div>
            <p>Deletes a Telegram bot configuration from the system.</p>

            <h4>Authentication</h4>
            <p>Requires a valid session cookie.</p>

            <h4>Path Parameters</h4>
            <table class="parameters-table">
                 <thead><tr><th>Parameter</th><th>Type</th><th>Description</th></tr></thead>
                <tbody>
                    <tr><td><code>bot_id</code></td><td><span class="param-type">string</span></td><td><span class="param-required">Required.</span> The MongoDB ObjectId of the bot configuration to delete.</td></tr>
                </tbody>
            </table>

            <h4>Response</h4>
            <p>Redirects to the Telegram bots list page (<code>/dashboard/telegram-bots</code>) with a success or error message.</p>

            <div class="code-samples">
                 <div class="code-tabs">
                    <button class="code-tab active" data-target="delete-bot-curl">cURL</button>
                    <button class="code-tab" data-target="delete-bot-fetch">Fetch</button>
                </div>
                <div class="code-content">
                    <div class="code-block active" id="delete-bot-curl">
                        <pre><code class="language-bash">curl -X POST http://localhost:5001/dashboard/delete-telegram-bot/60a1b2c3d4e5f6g7h8i9j0k \
 -b cookies.txt</code></pre>
                    </div>
                     <div class="code-block" id="delete-bot-fetch">
                        <pre><code class="language-javascript">const botId = '60a1b2c3d4e5f6g7h8i9j0k';
fetch(`/dashboard/delete-telegram-bot/${botId}`, {
  method: 'POST', // Or 'DELETE' if the API supports it
  credentials: 'include',
  redirect: 'follow'
})
.then(response => {
  if (response.ok && response.url.includes('/dashboard/telegram-bots')) {
     console.log('Telegram bot deleted, redirected.');
  } else {
     console.error('Failed to delete Telegram bot.');
  }
})
.catch(error => console.error('Error:', error));</code></pre>
                    </div>
                </div>
            </div>
        </section>

         <section class="endpoint" id="endpoint-test-telegram-bot" data-searchable-text="telegram bots test check verify post bot_id token api">
             <div class="endpoint-header">
                <span class="method-badge post">POST</span>
                <h3>Test Telegram Bot</h3>
                <code class="endpoint-url">/dashboard/test-telegram-bot/&lt;bot_id&gt;</code>
            </div>
            <p>Tests the API token of a configured Telegram bot by making a call to the Telegram API (e.g., `getMe`).</p>

            <h4>Authentication</h4>
            <p>Requires a valid session cookie.</p>

             <h4>Path Parameters</h4>
            <table class="parameters-table">
                 <thead><tr><th>Parameter</th><th>Type</th><th>Description</th></tr></thead>
                <tbody>
                    <tr><td><code>bot_id</code></td><td><span class="param-type">string</span></td><td><span class="param-required">Required.</span> The MongoDB ObjectId of the bot configuration to test.</td></tr>
                </tbody>
            </table>

            <h4>Response</h4>
            <p>Returns a JSON response indicating the result of the test.</p>
             <pre><code class="language-json">{
  "success": true,
  "message": "Bot token is valid. Bot name: [Bot Name]",
  "bot_info": { /* Response from Telegram's getMe */ }
}
// Or on failure:
{
  "success": false,
  "error": "Failed to verify bot token. Invalid token or Telegram API error.",
  "details": { /* Error details from Telegram API if available */ }
}</code></pre>

            <div class="code-samples">
                 <div class="code-tabs">
                    <button class="code-tab active" data-target="test-bot-curl">cURL</button>
                    <button class="code-tab" data-target="test-bot-fetch">Fetch</button>
                </div>
                <div class="code-content">
                    <div class="code-block active" id="test-bot-curl">
                        <pre><code class="language-bash">curl -X POST http://localhost:5001/dashboard/test-telegram-bot/60a1b2c3d4e5f6g7h8i9j0k \
 -b cookies.txt \
 -H "Accept: application/json"</code></pre>
                    </div>
                     <div class="code-block" id="test-bot-fetch">
                        <pre><code class="language-javascript">const botId = '60a1b2c3d4e5f6g7h8i9j0k';
fetch(`/dashboard/test-telegram-bot/${botId}`, {
  method: 'POST',
  credentials: 'include',
  headers: {
    'Accept': 'application/json'
  }
})
.then(response => response.json())
.then(data => {
  if (data.success) {
    console.log('Bot test successful:', data.message, data.bot_info);
    // Update UI with success status
  } else {
    console.error('Bot test failed:', data.error, data.details);
    // Update UI with failure status
  }
})
.catch(error => console.error('Error:', error));</code></pre>
                    </div>
                </div>
            </div>
        </section>

        <section class="endpoint" id="endpoint-list-telegram-bot-users" data-searchable-text="telegram bots users list get members channel bot_telegram_username">
             <div class="endpoint-header">
                <span class="method-badge get">GET</span>
                <h3>List Telegram Bot Users</h3>
                <code class="endpoint-url">/dashboard/telegram-bot-users/&lt;bot_telegram_username&gt;</code>
            </div>
            <p>Retrieves the page listing users associated with a specific Telegram bot (likely users registered via that bot).</p>

            <h4>Authentication</h4>
            <p>Requires a valid session cookie.</p>

             <h4>Path Parameters</h4>
            <table class="parameters-table">
                 <thead><tr><th>Parameter</th><th>Type</th><th>Description</th></tr></thead>
                <tbody>
                    <tr><td><code>bot_telegram_username</code></td><td><span class="param-type">string</span></td><td><span class="param-required">Required.</span> The Telegram username (without @) of the bot whose users are to be listed.</td></tr>
                </tbody>
            </table>

            <h4>Response</h4>
            <p>Returns an HTML page displaying the list of users associated with the specified bot.</p>

            <div class="code-samples">
                 <div class="code-tabs">
                    <button class="code-tab active" data-target="list-bot-users-curl">cURL</button>
                    <button class="code-tab" data-target="list-bot-users-fetch">Fetch</button>
                </div>
                <div class="code-content">
                    <div class="code-block active" id="list-bot-users-curl">
                        <pre><code class="language-bash">curl -X GET http://localhost:5001/dashboard/telegram-bot-users/my_bot \
 -b cookies.txt</code></pre>
                    </div>
                     <div class="code-block" id="list-bot-users-fetch">
                        <pre><code class="language-javascript">const botUsername = 'my_bot';
fetch(`/dashboard/telegram-bot-users/${botUsername}`, {
  method: 'GET',
  credentials: 'include'
})
.then(response => response.ok ? response.text() : Promise.reject('Failed to load bot users list'))
.then(html => {
  console.log(`User list HTML for bot ${botUsername} loaded.`);
  // Update UI
})
.catch(error => console.error('Error:', error));</code></pre>
                    </div>
                </div>
            </div>
        </section>

        <!-- ======================= -->
        <!-- Data Models Section     -->
        <!-- ======================= -->
        <section id="data-models" data-searchable-text="data models schema user access code verification support telegram bot mongodb collection structure">
            <h2>Data Models</h2>
            <p>The following are simplified representations of the main data structures stored in MongoDB collections.</p>

            <h3 id="model-user">User Data Model (<code>master_user_data</code> collection)</h3>
            <div class="data-model">
                <pre><code class="language-json">{
  "user_id": 7657876053, // NumberLong (Telegram User ID)
  "access_code": "ABC123",
  "broker_reg": true,
  "email": "<EMAIL>",
  "name": "User Name",
  "registration_time": "2023-01-01T00:00:00Z", // ISODate
  "subscription": "active",
  "telegram_bot_id": 123456789, // ID of the bot used for registration
  "trading_experience": "intermediate",
  "user_status": "active", // e.g., active, inactive, banned
  "user_verify": true, // Whether verification was completed
  "whatsapp": "+1234567890",
  // potentially other fields like last_updated, etc.
}</code></pre>
            </div>

             <h3 id="model-access-code">Access Code Model (<code>access_code</code> collection)</h3>
             <div class="data-model">
                <pre><code class="language-json">{
  "code": "ABC123",
  "source": "manually_added", // or "uploaded", "generated"
  "added_by": "admin_id", // Reference to the admin who added it
  "added_at": "2023-01-01T00:00:00Z", // ISODate
  "expiration_date": "2023-02-01T00:00:00Z", // ISODate (can be null)
  "used_by_user_id": null, // Populated with user_id when used
  "used_at": null, // ISODate when used
  "reg_date": "2023-01-01", // String date field (purpose might need clarification)
  "volume_mln_usd": 1.5, // Example custom field
  "trade_fn": "Trading Function", // Example custom field
}</code></pre>
            </div>

             <h3 id="model-verification-request">Verification Request Model (<code>verification_request</code> collection)</h3>
             <div class="data-model">
                <pre><code class="language-json">{
  "request_id": "REQ123", // Unique identifier
  "user_id": 7657876053, // NumberLong
  "access_code": "ABC123", // Code used for verification
  "created_at": "2023-01-01T00:00:00Z", // ISODate
  "status": "pending", // or "approved", "denied"
  "admin_notes": [
    {
      "admin_id": "admin_user",
      "admin_name": "Admin Name",
      "note": "This is an admin note",
      "timestamp": "2023-01-02T00:00:00Z" // ISODate
    }
    // ... more notes
  ],
  "reviewed_by": null, // admin_id who approved/denied
  "reviewed_at": null // ISODate of review
}</code></pre>
            </div>

             <h3 id="model-support-request">Support Request Model (<code>support_request</code> collection)</h3>
             <div class="data-model">
                 <pre><code class="language-json">{
  "request_id": "SUP123", // Unique identifier
  "user_id": 7657876053, // NumberLong
  "user_details": { // Snapshot of user details at time of request
    "name": "User Name",
    "email": "<EMAIL>",
    "whatsapp": "+1234567890"
  },
  "query": "I need help with login", // The user's message
  "created_at": "2023-01-01T00:00:00Z", // ISODate
  "status": "pending", // or "resolved", "closed"
  "resolved_at": null, // ISODate when resolved
  "resolved_by": null, // admin_id who resolved it
  "admin_notes": [ /* Optional: Similar structure to verification notes */ ]
}</code></pre>
            </div>

             <h3 id="model-telegram-bot">Telegram Bot Model (<code>telegram_bots</code> collection)</h3>
             <div class="data-model">
                 <pre><code class="language-json">{
  "_id": "60a1b2c3d4e5f6g7h8i9j0k", // ObjectId (Primary Key)
  "name": "MyBot", // Friendly name
  "username": "my_bot", // Telegram username (without @)
  "token": "123456:ABC-DEF", // Bot API Token (should be stored securely)
  "channel_id": "-1001234567890", // Associated channel ID
  "telegram_id": 123456789, // Bot's own Telegram ID (from getMe)
  "telegram_first_name": "My Bot", // Bot's first name (from getMe)
  "is_active": true, // Whether the bot is considered active/enabled
  "added_at": "2023-01-01T00:00:00Z", // ISODate
  "updated_at": "2023-01-01T00:00:00Z" // ISODate
}</code></pre>
            </div>
        </section>


         <!-- ======================= -->
         <!-- Error Handling Section  -->
         <!-- ======================= -->
        <section id="error-handling" data-searchable-text="errors http status codes json response format 400 401 404 500 bad request unauthorized not found server error">
            <h2>Error Handling</h2>
            <p>The API uses standard HTTP status codes to indicate the success or failure of a request.</p>
            <ul>
                <li><code>200 OK</code>: Request successful.</li>
                 <li><code>302 Found</code>: Used for redirects, typically after successful POST operations (like login, logout, form submissions) or when authentication is required and the user is sent to the login page.</li>
                <li><code>400 Bad Request</code>: The request was malformed, contained invalid parameters, or failed validation. The response body (often JSON) may contain details.</li>
                <li><code>401 Unauthorized</code>: Authentication is required, but was missing or invalid. Often results in a redirect to login for browser requests, but API clients might receive this status directly.</li>
                <li><code>403 Forbidden</code>: The authenticated user does not have permission to perform the requested action.</li>
                <li><code>404 Not Found</code>: The requested resource (e.g., a specific user, code, or endpoint path) could not be found.</li>
                <li><code>500 Internal Server Error</code>: An unexpected error occurred on the server.</li>
            </ul>

            <h4>JSON Error Response Format</h4>
            <p>When endpoints return JSON responses for errors, they typically follow this format:</p>
            <pre><code class="language-json">{
  "success": false,
  "error": "A descriptive error message explaining what went wrong."
  // "details": { ... } // Optional: May include more specific error details
}</code></pre>

            <h4>JSON Success Response Format</h4>
            <p>Successful JSON responses generally include a success flag and often a message:</p>
             <pre><code class="language-json">{
  "success": true,
  "message": "Operation completed successfully."
  // "data": { ... } // Optional: May include data payload if the operation returns data
}</code></pre>
             <p>Note that many endpoints in this API are designed for the web interface and return HTML or redirects rather than JSON.</p>
        </section>


    </main>

    <!-- Embedded JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // --- Configuration ---
            const scrollOffset = 80; // Offset for active link highlighting (px)
            const searchDebounceTime = 300; // Debounce time for search input (ms)

            // --- Elements ---
            const sidebar = document.getElementById('sidebar');
            const sidebarNav = document.getElementById('sidebar-nav');
            const navLinks = sidebarNav.querySelectorAll('a');
            const mainContent = document.getElementById('main-content');
            const contentSections = mainContent.querySelectorAll('section[id], h2[id], h3[id]'); // Elements that nav links point to
            const searchInput = document.getElementById('sidebar-search');
            const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
            const codeTabs = document.querySelectorAll('.code-tab');

            // --- State ---
            let searchDebounceTimer;

            // --- Functions ---

             /**
             * Basic Syntax Highlighting for Code Blocks (Corrected Version)
             * Applies simple regex-based highlighting for JSON, Bash (curl), and JS (fetch).
             * Escapes original code first, then applies spans.
             */
            function highlightCodeBlocks() {
                const codeBlocks = document.querySelectorAll('pre code');
                codeBlocks.forEach(block => {
                    const language = block.className.replace('language-', '');
                    let code = block.textContent; // Get raw text content

                    // --- Step 1: Escape HTML entities in the original code ---
                    // This prevents accidental HTML interpretation of code content like '<' or '>'
                    const escapeHtml = (unsafe) => {
                        if (!unsafe) return "";
                        return unsafe
                             .replace(/&/g, "&amp;")
                             .replace(/</g, "&lt;")
                             .replace(/>/g, "&gt;")
                             .replace(/"/g, "&quot;")
                             .replace(/'/g, "&#039;");
                    }
                    code = escapeHtml(code);

                    // --- Step 2: Apply highlighting spans (these spans are intentional HTML) ---
                    // JSON Highlighting
                    if (language === 'json') {
                        // Strings (handle escaped quotes within strings)
                        code = code.replace(/(&quot;)(.*?)(&quot;)/g, '<span class="token punctuation">$1</span><span class="token string">$2</span><span class="token punctuation">$3</span>');
                        // Keys (quoted strings before a colon)
                        code = code.replace(/(&quot;[^"]+&quot;)(\s*):/g, '<span class="token key">$1</span>$2:');
                        // Booleans
                        code = code.replace(/\b(true|false)\b/g, '<span class="token boolean">$1</span>');
                        // Null
                        code = code.replace(/\b(null)\b/g, '<span class="token null">$1</span>');
                        // Numbers
                        code = code.replace(/\b(-?\d+(\.\d+)?([eE][+-]?\d+)?)\b/g, '<span class="token number">$1</span>');
                         // Punctuation (avoid highlighting inside potential future HTML tags, though unlikely here)
                         code = code.replace(/([{}[\]:,])(?!([^&]|&amp;)*;)/g, '<span class="token punctuation">$1</span>');
                    }
                    // Bash/cURL Highlighting
                    else if (language === 'bash') {
                         code = code.replace(/#[^\n]*/g, '<span class="token comment">$&</span>'); // Comments
                         code = code.replace(/^curl\b/gm, '<span class="token keyword">curl</span>'); // curl command at line start
                         // Options/Flags (like -X, -H, -d, -b, -c, -F, --request, etc.)
                         // Need to be careful not to match parts of URLs or data
                         code = code.replace(/(\s)(-X|-H|-d|-b|-c|-F|--request|--header|--data|--cookie|--cookie-jar|--form)\b/g, '$1<span class="token parameter">$2</span>');
                         // Common methods after -X
                         code = code.replace(/(-X)\s+(GET|POST|PUT|DELETE|PATCH|HEAD|OPTIONS)\b/g, '$1 <span class="token keyword">$2</span>');
                         // Strings (handle escaped quotes)
                         code = code.replace(/(&quot;)(.*?)(&quot;)/g, '<span class="token punctuation">$1</span><span class="token string">$2</span><span class="token punctuation">$3</span>');
                         code = code.replace(/(')(.*?)(')/g, '<span class="token punctuation">$1</span><span class="token string">$2</span><span class="token punctuation">$3</span>');
                         // URLs (basic detection)
                         code = code.replace(/(https?:\/\/[^\s'"\\]+)/g, '<span class="token url">$1</span>');
                    }
                    // JavaScript/Fetch Highlighting
                    else if (language === 'javascript') {
                        code = code.replace(/(\/\/.*)/g, '<span class="token comment">$1</span>'); // Single-line comments
                        code = code.replace(/(\/\*[\s\S]*?\*\/)/g, '<span class="token comment">$1</span>'); // Multi-line comments
                        // Strings (handle escaped quotes/backticks)
                        code = code.replace(/(&quot;)(.*?)(&quot;)/g, '<span class="token punctuation">$1</span><span class="token string">$2</span><span class="token punctuation">$3</span>');
                        code = code.replace(/(')(.*?)(')/g, '<span class="token punctuation">$1</span><span class="token string">$2</span><span class="token punctuation">$3</span>');
                        code = code.replace(/(`)([\s\S]*?)(`)/g, '<span class="token punctuation">$1</span><span class="token string">$2</span><span class="token punctuation">$3</span>'); // Template literals
                         // Keywords
                        code = code.replace(/\b(const|let|var|function|fetch|then|catch|new|return|if|else|await|async|class|import|export|from|document|window|console)\b/g, '<span class="token keyword">$1</span>');
                        // Booleans / Null
                         code = code.replace(/\b(true|false)\b/g, '<span class="token boolean">$1</span>');
                         code = code.replace(/\b(null|undefined)\b/g, '<span class="token null">$1</span>');
                         // Numbers
                        code = code.replace(/\b(-?\d+(\.\d+)?([eE][+-]?\d+)?)\b/g, '<span class="token number">$1</span>');
                        // Function names (simple detection: word followed by parenthesis)
                        code = code.replace(/([a-zA-Z_\$][\w\$]*)\s*\(/g, '<span class="token function">$1</span>(');
                         // Class names (simple detection: UpperCaseWord often after 'new' or 'class') - Less reliable
                         code = code.replace(/\b([A-Z][A-Za-z0-9_]*)\b/g, '<span class="token class-name">$1</span>');
                        // Punctuation
                        code = code.replace(/([{}[\]();:,=+\-*/&|!]|&lt;|&gt;)(?!([^&]|&amp;)*;)/g, '<span class="token punctuation">$1</span>');
                    }

                    // --- Step 3: Set the innerHTML ---
                    // The browser should now correctly interpret the spans as HTML
                    // and the escaped original content as text.
                    block.innerHTML = code;
                });
            }


            /**
             * Updates the active link in the sidebar based on scroll position.
             */
            function updateActiveNavLink() {
                let currentSectionId = null;
                const scrollPosition = window.scrollY + scrollOffset;

                contentSections.forEach(section => {
                    // Check if section exists and has an offsetTop
                    if (section && typeof section.offsetTop !== 'undefined') {
                         if (section.offsetTop <= scrollPosition) {
                             // Check if the section is actually visible (not hidden by search)
                            const sectionStyle = window.getComputedStyle(section);
                            if (sectionStyle.display !== 'none') {
                                currentSectionId = section.id;
                            }
                        }
                    }
                });

                 // Fallback if no section is found high enough (e.g., at the very top)
                 if (!currentSectionId && contentSections.length > 0 && contentSections[0].id) {
                     // Check if first section is visible
                      const firstSectionStyle = window.getComputedStyle(contentSections[0]);
                       if (firstSectionStyle.display !== 'none' && window.scrollY < contentSections[0].offsetTop) {
                          // Maybe highlight the first visible link or none
                          currentSectionId = null; // Or potentially highlight the first nav link's target if appropriate
                       }
                 }


                navLinks.forEach(link => {
                    link.classList.remove('active');
                    const href = link.getAttribute('href');
                     // Ensure href exists and corresponds to a section before adding 'active'
                    if (href && href.startsWith('#') && document.getElementById(href.substring(1))) {
                         if (href.substring(1) === currentSectionId) {
                            link.classList.add('active');
                            // Optional: Scroll sidebar to keep active link visible (can be jumpy)
                            // link.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                        }
                    }
                });
            }


            /**
             * Filters sidebar links and main content sections based on search input.
             */
            function filterContent() {
                const query = searchInput.value.toLowerCase().trim();
                 // Select all potential content blocks to filter (sections and h2/h3 with IDs)
                const allContentBlocks = mainContent.querySelectorAll('.endpoint, section[id], h2[id], h3[id]');
                const allNavItems = sidebarNav.querySelectorAll('nav > ul > li li'); // All endpoint/sub-item nav items
                const allCategories = sidebarNav.querySelectorAll('nav > ul > li[data-category]'); // Category list items

                allContentBlocks.forEach(block => {
                     // Use searchable text if available, otherwise try matching ID or title text
                    const searchableText = (block.dataset.searchableText || '').toLowerCase();
                    const titleText = (block.querySelector('h3') ? block.querySelector('h3').textContent : block.textContent).toLowerCase();
                    const blockId = (block.id || '').toLowerCase();
                    const matches = searchableText.includes(query) || titleText.includes(query) || blockId.includes(query);
                    block.style.display = matches ? '' : 'none';
                });

                allNavItems.forEach(item => {
                     const link = item.querySelector('a');
                     if (link) {
                         const linkText = link.textContent.toLowerCase();
                         const linkHref = link.getAttribute('href').substring(1).toLowerCase();
                         // Also check the searchable text of the target section if it exists
                         const targetSection = document.getElementById(linkHref);
                         const targetSearchableText = targetSection ? (targetSection.dataset.searchableText || '').toLowerCase() : '';
                         const targetTitleText = targetSection ? (targetSection.querySelector('h3') ? targetSection.querySelector('h3').textContent : targetSection.textContent).toLowerCase() : '';

                         const matches = linkText.includes(query) || linkHref.includes(query) || targetSearchableText.includes(query) || targetTitleText.includes(query) || query === '';
                         item.classList.toggle('hidden', !matches);
                     } else {
                         // Hide list items that aren't links if they don't match (though shouldn't be many)
                         item.classList.toggle('hidden', query !== '');
                     }
                });

                 // Show/hide categories based on whether they have visible items
                 allCategories.forEach(category => {
                    const categoryItemsList = category.querySelector('ul');
                    let hasVisibleItems = false;
                    if (categoryItemsList) {
                         hasVisibleItems = Array.from(categoryItemsList.querySelectorAll('li')).some(li => !li.classList.contains('hidden'));
                    }
                    category.classList.toggle('hidden', !hasVisibleItems && query !== '');
                    // Also hide/show the category title span itself if needed
                    const categorySpan = category.querySelector('span');
                    if (categorySpan) {
                        categorySpan.classList.toggle('hidden', !hasVisibleItems && query !== '');
                    }
                 });
                 // Update active link after filtering might have hidden the current one
                 updateActiveNavLink();
            }

            /**
             * Toggles the mobile sidebar visibility.
             */
            function toggleMobileMenu() {
                sidebar.classList.toggle('open');
                 mobileMenuToggle.setAttribute('aria-expanded', sidebar.classList.contains('open'));
            }

            /**
             * Handles clicks on code tabs to switch visible code block.
             */
            function handleCodeTabClick(event) {
                 if (!event.target.classList.contains('code-tab')) return;

                 const clickedTab = event.target;
                 const targetId = clickedTab.dataset.target;
                  // Find the closest '.code-samples' parent, then the '.code-content' child
                 const codeContentContainer = clickedTab.closest('.code-tabs').nextElementSibling; // Assumes .code-content is next sibling

                 if (!codeContentContainer) {
                      console.error("Could not find code content container for tab:", clickedTab);
                      return;
                 }
                 const targetBlock = codeContentContainer.querySelector(`#${targetId}`);

                 if (!targetBlock || clickedTab.classList.contains('active')) return;

                 // Deactivate other tabs and hide blocks within the same container
                 const tabs = clickedTab.parentElement.querySelectorAll('.code-tab');
                 const blocks = codeContentContainer.querySelectorAll('.code-block');

                 tabs.forEach(tab => tab.classList.remove('active'));
                 blocks.forEach(block => block.classList.remove('active')); // Use class instead of display:none

                 // Activate clicked tab and show target block
                 clickedTab.classList.add('active');
                 targetBlock.classList.add('active');
            }

            // --- Event Listeners ---
            window.addEventListener('scroll', updateActiveNavLink);
            searchInput.addEventListener('input', () => {
                clearTimeout(searchDebounceTimer);
                searchDebounceTimer = setTimeout(filterContent, searchDebounceTime);
            });
            mobileMenuToggle.addEventListener('click', toggleMobileMenu);
            mobileMenuToggle.setAttribute('aria-expanded', 'false'); // Initial state


            // Close sidebar if clicking outside on mobile
            document.addEventListener('click', (event) => {
                 if (window.innerWidth < 992 && sidebar.classList.contains('open')) {
                    if (!sidebar.contains(event.target) && event.target !== mobileMenuToggle) {
                        toggleMobileMenu();
                    }
                 }
                 // Close sidebar when a nav link is clicked on mobile
                 if (window.innerWidth < 992 && navLinks) {
                     // Check if the click target is one of the nav links
                     if (Array.from(navLinks).includes(event.target)) {
                          toggleMobileMenu();
                     }
                 }
            });

            // Add listeners to all code sample containers (specifically the tabs container)
             const codeTabContainers = document.querySelectorAll('.code-tabs');
             codeTabContainers.forEach(container => {
                container.addEventListener('click', handleCodeTabClick);
             });


            // --- Initialisation ---
            highlightCodeBlocks(); // Apply syntax highlighting
            updateActiveNavLink(); // Initial check for active link
            filterContent(); // Initial filter check (e.g., if search has default value)

        });
    </script>

</body>
</html>