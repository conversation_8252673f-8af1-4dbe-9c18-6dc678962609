#!/usr/bin/env python3
"""
Script to reset admin password for a customer database.
Usage: python reset_admin_password.py --password <new_password> --cn <customer_name>
"""
import argparse
import logging
import sys
from pymongo import MongoClient
from werkzeug.security import generate_password_hash
from bson.objectid import ObjectId

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def parse_arguments():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description='Reset admin password for a customer database.')
    parser.add_argument('--password', required=True, help='New admin password')
    parser.add_argument('--cn', required=True, help='Customer name (without _custdb suffix)')
    return parser.parse_args()

def reset_admin_password(customer_name, new_password):
    """
    Reset admin password for a customer database.

    Args:
        customer_name (str): Customer name (without _custdb suffix)
        new_password (str): New admin password

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Construct database name
        db_name = f"{customer_name}_custdb"
        logger.info(f"Attempting to reset admin password for database: {db_name}")

        # Connect to MongoDB
        # Using the same connection string as in the application
        mongo_uri = "mongodb://localhost:27017/"
        client = MongoClient(mongo_uri)

        # Check if database exists
        if db_name not in client.list_database_names():
            logger.error(f"Database '{db_name}' does not exist")
            return False

        # Get database
        db = client[db_name]

        # Check if admins collection exists
        if "admins" not in db.list_collection_names():
            logger.error(f"Collection 'admins' does not exist in database '{db_name}'")
            return False

        # Find admin user
        admin = db.admins.find_one({"username": "admin"})
        if not admin:
            logger.error(f"Admin user not found in database '{db_name}'")
            return False

        # Generate new password hash with default method for Werkzeug 2.2.3 (pbkdf2:sha256)
        password_hash = generate_password_hash(new_password)

        # Update admin password
        result = db.admins.update_one(
            {"_id": admin["_id"]},
            {"$set": {"password_hash": password_hash}}
        )

        if result.modified_count > 0:
            logger.info(f"Admin password reset successfully for database '{db_name}'")
            logger.info(f"Admin ID: {admin['_id']}")
            logger.info(f"New password hash: {password_hash}")
            return True
        else:
            logger.error(f"Failed to update admin password for database '{db_name}'")
            return False

    except Exception as e:
        logger.error(f"Error resetting admin password: {e}")
        return False

def main():
    """Main function."""
    args = parse_arguments()

    # Validate password
    if len(args.password) < 8:
        logger.error("Password must be at least 8 characters long")
        sys.exit(1)

    # Reset admin password
    success = reset_admin_password(args.cn, args.password)

    if success:
        logger.info("Password reset completed successfully")
        sys.exit(0)
    else:
        logger.error("Password reset failed")
        sys.exit(1)

if __name__ == "__main__":
    main()
