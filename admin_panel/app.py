from flask import Flask, request, redirect, render_template, g, session
from config import Config
import logging
import re
from werkzeug.middleware.dispatcher import DispatcherMiddleware
from werkzeug.exceptions import NotFound

# Get logger for this module
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.config.from_object(Config)

# Force DEBUG mode for development
app.config['DEBUG'] = True
app.config['TEMPLATES_AUTO_RELOAD'] = True

logger.info(f"Flask application configured, DEBUG mode: {app.config['DEBUG']}")

# Import tenant middleware and session interface
from utils.tenant_middleware import TenantMiddleware
from utils.tenant_session import TenantSessionInterface

# Apply tenant middleware
app.wsgi_app = TenantMiddleware(app.wsgi_app)

# Use tenant-specific session interface
app.session_interface = TenantSessionInterface()
logger.info("Using tenant-specific session interface")

# Register blueprints
from controllers.auth import auth_bp
from controllers.dashboard_telegram import dashboard_bp  # Import from the correct file
from controllers.api import api_bp
from controllers.api_manager import api_manager_bp
from controllers.broker_api import broker_api_bp
from controllers.logs_api import logs_api_bp

# Create a route for the welcome page
@app.route('/welcome')
def welcome():
    """Simple welcome page for when a tenant database doesn't exist"""
    tenant_name = request.args.get('tenant', 'Unknown')
    return render_template('welcome.html', tenant_name=tenant_name)

# Middleware to handle tenant resolution
@app.before_request
def resolve_tenant():
    """
    Middleware to handle tenant resolution.
    Gets tenant from environ (set by TenantMiddleware), checks if tenant database exists,
    and redirects to welcome page if it doesn't.
    """
    # Log request information
    logger.debug(f"Request path: {request.path}")
    logger.debug(f"Request cookies: {request.cookies}")

    # Skip for static files and the welcome page itself
    if request.path.startswith('/static') or request.path == '/welcome':
        # Clear tenant context for static files and welcome page
        if 'tenant' in session:
            session.pop('tenant')
        if hasattr(g, 'tenant'):
            delattr(g, 'tenant')
        logger.debug("Skipping tenant resolution for static file or welcome page")
        return None

    # Get tenant from environ (set by TenantMiddleware)
    tenant_name = request.environ.get('tenant')
    logger.debug(f"Tenant from environ: {tenant_name}")

    # If we have a tenant
    if tenant_name:
        # Always store tenant in session and g
        # Note: The session cookie name is already tenant-specific due to TenantSessionInterface
        session['tenant'] = tenant_name
        g.tenant = tenant_name
        logger.debug(f"Set tenant in session and g: {tenant_name}")
        logger.debug(f"Session after setting tenant: {dict(session)}")

        # Import MongoDB class
        from utils.mongo_db import MongoDB
        mongo_db = MongoDB()

        # Check if tenant database exists
        tenant_db_name = f"{tenant_name}_custdb"

        # Check if we need to switch databases
        if mongo_db.db.name != tenant_db_name:
            # Force database switch
            logger.info(f"Switching database from {mongo_db.db.name} to {tenant_db_name} for tenant {tenant_name}")
            mongo_db.current_tenant = None  # Reset to force switch

            # Check if tenant database exists
            if not mongo_db._tenant_db_exists(tenant_db_name):
                # Redirect to welcome page if tenant database doesn't exist
                logger.warning(f"Tenant database '{tenant_db_name}' does not exist")
                return redirect(f"/welcome?tenant={tenant_name}")

            # Switch to tenant database
            mongo_db.switch_database(tenant_db_name)

        # Store original path and tenant path in g
        g.original_path = request.environ.get('ORIGINAL_PATH_INFO', request.path)
        g.tenant_path = request.path

        # Log the path rewriting
        logger.debug(f"Tenant: {tenant_name}, Original path: {g.original_path}, Tenant path: {g.tenant_path}")

        # Check if user is logged in for this tenant
        if 'admin_id' in session:
            logger.debug(f"User is logged in for tenant {tenant_name}, admin_id: {session['admin_id']}")
        else:
            logger.debug(f"User is not logged in for tenant {tenant_name}")
    else:
        # No tenant in URL, clear tenant context
        if 'tenant' in session:
            session.pop('tenant')
        if hasattr(g, 'tenant'):
            delattr(g, 'tenant')
        logger.debug("No tenant in URL, cleared tenant context")

        # Switch to default database if needed
        from utils.mongo_db import MongoDB
        mongo_db = MongoDB()
        if mongo_db.db.name != mongo_db.default_db_name:
            logger.info(f"Switching to default database {mongo_db.default_db_name}")
            mongo_db.switch_database(mongo_db.default_db_name)

    # Force session save
    session.modified = True

    return None

# Create a context processor to make tenant information available to templates
@app.context_processor
def inject_tenant():
    """
    Make tenant information available to all templates.
    """
    from utils.url_helpers import get_tenant_prefix

    tenant = None
    try:
        # Get tenant from request.environ first (most reliable), then g or session
        if hasattr(request, 'environ'):
            tenant = request.environ.get('tenant')

        if not tenant:
            tenant = getattr(g, 'tenant', None) or session.get('tenant')

        logger.debug(f"Injecting tenant into template context: {tenant}")
    except RuntimeError:
        # Handle case where app context is not available
        pass

    # Get tenant prefix based on mode
    tenant_prefix = get_tenant_prefix()

    return {
        'tenant': tenant,
        'tenant_prefix': tenant_prefix,
        'session_name': f"session_{tenant}" if tenant else "session"
    }

# Create a URL converter for tenant-aware URL generation
from utils.url_helpers import tenant_url_for
app.jinja_env.globals['tenant_url_for'] = tenant_url_for

# Register blueprints
app.register_blueprint(auth_bp)
app.register_blueprint(dashboard_bp)
app.register_blueprint(api_bp)
app.register_blueprint(api_manager_bp)
app.register_blueprint(broker_api_bp)
app.register_blueprint(logs_api_bp)
logger.info("Blueprints registered")

if __name__ == '__main__':
    logger.info("Starting Flask application directly")
    app.run(debug=Config.DEBUG)