#!/usr/bin/env python3
"""
Admin Tools Script for managing admin users in customer databases.
This script provides various admin management functions:
- Reset admin password
- Create new admin user
- List admin users
- Delete admin user

Usage: python admin_tools.py [command] [options]
"""
import argparse
import logging
import sys
from datetime import datetime, timezone
from pymongo import MongoClient
from werkzeug.security import generate_password_hash
from bson.objectid import ObjectId

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def parse_arguments():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description='Admin tools for customer databases.')
    subparsers = parser.add_subparsers(dest='command', help='Command to execute')
    
    # Reset password command
    reset_parser = subparsers.add_parser('reset-password', help='Reset admin password')
    reset_parser.add_argument('--password', required=True, help='New admin password')
    reset_parser.add_argument('--cn', required=True, help='Customer name (without _custdb suffix)')
    reset_parser.add_argument('--username', default='admin', help='Admin username (default: admin)')
    
    # Create admin command
    create_parser = subparsers.add_parser('create-admin', help='Create new admin user')
    create_parser.add_argument('--username', required=True, help='Admin username')
    create_parser.add_argument('--password', required=True, help='Admin password')
    create_parser.add_argument('--cn', required=True, help='Customer name (without _custdb suffix)')
    
    # List admins command
    list_parser = subparsers.add_parser('list-admins', help='List admin users')
    list_parser.add_argument('--cn', required=True, help='Customer name (without _custdb suffix)')
    
    # Delete admin command
    delete_parser = subparsers.add_parser('delete-admin', help='Delete admin user')
    delete_parser.add_argument('--username', required=True, help='Admin username')
    delete_parser.add_argument('--cn', required=True, help='Customer name (without _custdb suffix)')
    
    # List databases command
    list_db_parser = subparsers.add_parser('list-databases', help='List all customer databases')
    
    return parser.parse_args()

def get_mongodb_client():
    """Get MongoDB client."""
    try:
        # Using the same connection string as in the application
        mongo_uri = "mongodb://mongodb:27017/"
        return MongoClient(mongo_uri)
    except Exception as e:
        logger.error(f"Error connecting to MongoDB: {e}")
        return None

def check_database_exists(client, db_name):
    """Check if database exists."""
    if db_name not in client.list_database_names():
        logger.error(f"Database '{db_name}' does not exist")
        return False
    return True

def check_collection_exists(db, collection_name):
    """Check if collection exists."""
    if collection_name not in db.list_collection_names():
        logger.error(f"Collection '{collection_name}' does not exist")
        return False
    return True

def reset_admin_password(customer_name, username, new_password):
    """
    Reset admin password for a customer database.
    
    Args:
        customer_name (str): Customer name (without _custdb suffix)
        username (str): Admin username
        new_password (str): New admin password
    
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Construct database name
        db_name = f"{customer_name}_custdb"
        logger.info(f"Attempting to reset password for admin '{username}' in database: {db_name}")
        
        # Connect to MongoDB
        client = get_mongodb_client()
        if not client:
            return False
        
        # Check if database exists
        if not check_database_exists(client, db_name):
            return False
        
        # Get database
        db = client[db_name]
        
        # Check if admins collection exists
        if not check_collection_exists(db, "admins"):
            return False
        
        # Find admin user
        admin = db.admins.find_one({"username": username})
        if not admin:
            logger.error(f"Admin user '{username}' not found in database '{db_name}'")
            return False
        
        # Generate new password hash
        password_hash = generate_password_hash(new_password)
        
        # Update admin password
        result = db.admins.update_one(
            {"_id": admin["_id"]},
            {"$set": {"password_hash": password_hash}}
        )
        
        if result.modified_count > 0:
            logger.info(f"Admin password reset successfully for '{username}' in database '{db_name}'")
            logger.info(f"Admin ID: {admin['_id']}")
            logger.info(f"New password hash: {password_hash}")
            return True
        else:
            logger.error(f"Failed to update password for admin '{username}' in database '{db_name}'")
            return False
    
    except Exception as e:
        logger.error(f"Error resetting admin password: {e}")
        return False

def create_admin_user(customer_name, username, password):
    """
    Create new admin user for a customer database.
    
    Args:
        customer_name (str): Customer name (without _custdb suffix)
        username (str): Admin username
        password (str): Admin password
    
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Construct database name
        db_name = f"{customer_name}_custdb"
        logger.info(f"Attempting to create admin user '{username}' in database: {db_name}")
        
        # Connect to MongoDB
        client = get_mongodb_client()
        if not client:
            return False
        
        # Check if database exists
        if not check_database_exists(client, db_name):
            return False
        
        # Get database
        db = client[db_name]
        
        # Check if admins collection exists
        if not check_collection_exists(db, "admins"):
            logger.info(f"Creating 'admins' collection in database '{db_name}'")
            db.create_collection("admins")
        
        # Check if admin user already exists
        existing_admin = db.admins.find_one({"username": username})
        if existing_admin:
            logger.error(f"Admin user '{username}' already exists in database '{db_name}'")
            return False
        
        # Generate password hash
        password_hash = generate_password_hash(password)
        
        # Create admin user
        result = db.admins.insert_one({
            "username": username,
            "password_hash": password_hash,
            "created_at": datetime.now(timezone.utc)
        })
        
        if result.inserted_id:
            logger.info(f"Admin user '{username}' created successfully in database '{db_name}'")
            logger.info(f"Admin ID: {result.inserted_id}")
            return True
        else:
            logger.error(f"Failed to create admin user '{username}' in database '{db_name}'")
            return False
    
    except Exception as e:
        logger.error(f"Error creating admin user: {e}")
        return False

def list_admin_users(customer_name):
    """
    List admin users for a customer database.
    
    Args:
        customer_name (str): Customer name (without _custdb suffix)
    
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Construct database name
        db_name = f"{customer_name}_custdb"
        logger.info(f"Listing admin users for database: {db_name}")
        
        # Connect to MongoDB
        client = get_mongodb_client()
        if not client:
            return False
        
        # Check if database exists
        if not check_database_exists(client, db_name):
            return False
        
        # Get database
        db = client[db_name]
        
        # Check if admins collection exists
        if not check_collection_exists(db, "admins"):
            return False
        
        # Find admin users
        admins = list(db.admins.find())
        
        if admins:
            logger.info(f"Found {len(admins)} admin users in database '{db_name}':")
            for admin in admins:
                created_at = admin.get("created_at", "Unknown")
                logger.info(f"ID: {admin['_id']}, Username: {admin['username']}, Created: {created_at}")
            return True
        else:
            logger.info(f"No admin users found in database '{db_name}'")
            return True
    
    except Exception as e:
        logger.error(f"Error listing admin users: {e}")
        return False

def delete_admin_user(customer_name, username):
    """
    Delete admin user from a customer database.
    
    Args:
        customer_name (str): Customer name (without _custdb suffix)
        username (str): Admin username
    
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Construct database name
        db_name = f"{customer_name}_custdb"
        logger.info(f"Attempting to delete admin user '{username}' from database: {db_name}")
        
        # Connect to MongoDB
        client = get_mongodb_client()
        if not client:
            return False
        
        # Check if database exists
        if not check_database_exists(client, db_name):
            return False
        
        # Get database
        db = client[db_name]
        
        # Check if admins collection exists
        if not check_collection_exists(db, "admins"):
            return False
        
        # Count admin users
        admin_count = db.admins.count_documents({})
        if admin_count <= 1:
            logger.error(f"Cannot delete the only admin user in database '{db_name}'")
            return False
        
        # Find admin user
        admin = db.admins.find_one({"username": username})
        if not admin:
            logger.error(f"Admin user '{username}' not found in database '{db_name}'")
            return False
        
        # Delete admin user
        result = db.admins.delete_one({"_id": admin["_id"]})
        
        if result.deleted_count > 0:
            logger.info(f"Admin user '{username}' deleted successfully from database '{db_name}'")
            return True
        else:
            logger.error(f"Failed to delete admin user '{username}' from database '{db_name}'")
            return False
    
    except Exception as e:
        logger.error(f"Error deleting admin user: {e}")
        return False

def list_customer_databases():
    """
    List all customer databases.
    
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Connect to MongoDB
        client = get_mongodb_client()
        if not client:
            return False
        
        # Get all databases
        all_dbs = client.list_database_names()
        
        # Filter customer databases
        customer_dbs = [db for db in all_dbs if db.endswith('_custdb')]
        
        if customer_dbs:
            logger.info(f"Found {len(customer_dbs)} customer databases:")
            for db in customer_dbs:
                customer_name = db[:-7]  # Remove '_custdb' suffix
                logger.info(f"Database: {db}, Customer: {customer_name}")
            return True
        else:
            logger.info("No customer databases found")
            return True
    
    except Exception as e:
        logger.error(f"Error listing customer databases: {e}")
        return False

def main():
    """Main function."""
    args = parse_arguments()
    
    if not args.command:
        logger.error("No command specified. Use --help for usage information.")
        sys.exit(1)
    
    # Execute command
    if args.command == 'reset-password':
        # Validate password
        if len(args.password) < 8:
            logger.error("Password must be at least 8 characters long")
            sys.exit(1)
        
        # Reset admin password
        success = reset_admin_password(args.cn, args.username, args.password)
    
    elif args.command == 'create-admin':
        # Validate password
        if len(args.password) < 8:
            logger.error("Password must be at least 8 characters long")
            sys.exit(1)
        
        # Create admin user
        success = create_admin_user(args.cn, args.username, args.password)
    
    elif args.command == 'list-admins':
        # List admin users
        success = list_admin_users(args.cn)
    
    elif args.command == 'delete-admin':
        # Delete admin user
        success = delete_admin_user(args.cn, args.username)
    
    elif args.command == 'list-databases':
        # List customer databases
        success = list_customer_databases()
    
    else:
        logger.error(f"Unknown command: {args.command}")
        sys.exit(1)
    
    if success:
        logger.info(f"Command '{args.command}' completed successfully")
        sys.exit(0)
    else:
        logger.error(f"Command '{args.command}' failed")
        sys.exit(1)

if __name__ == "__main__":
    main()
