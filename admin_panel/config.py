import os
import json
import secrets

# Define base directories
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
LOG_DIR = os.path.join(BASE_DIR, 'logs')  # Changed to be inside admin_panel

# Ensure directories exist
os.makedirs(LOG_DIR, exist_ok=True)

# Load configuration from JSON file
config_path = os.path.join(BASE_DIR, 'config.json')

try:
    with open(config_path, 'r') as config_file:
        config_data = json.load(config_file)
except FileNotFoundError:
    # Default configuration if file doesn't exist
    config_data = {
        "admin_panel": {
            "database": {
                "type": "mongodb",
                "uri": "mongodb://localhost:27017/",
                "database": "telegram_bot",  # Changed to telegram_bot
                "collections": {
                    "subscriptions": "subscriptions",
                    "activity_logs": "activity_logs",
                    "admins": "admins"
                }
            },
            "security": {
                "secret_key": "generate_new_key_on_first_run"
            },
            "debug": True
        },
        "telegram_bot": {
            "token": "your_telegram_bot_token_here",
            "admin_chat_ids": [],
            "notification_settings": {
                "new_user_added": True,
                "user_removed": True,
                "admin_login": True
            }
        },
        "general": {
            "timezone": "UTC",
            "log_level": "INFO",
            "log_file": os.path.join(LOG_DIR, "admin_panel.log")
        }
    }

    # Create the config file with default settings
    with open(config_path, 'w') as config_file:
        json.dump(config_data, config_file, indent=2)

# Generate a secret key if one doesn't exist
if config_data["admin_panel"]["security"]["secret_key"] == "generate_new_key_on_first_run":
    config_data["admin_panel"]["security"]["secret_key"] = secrets.token_hex(16)
    # Save the generated key back to the config file
    with open(config_path, 'w') as config_file:
        json.dump(config_data, config_file, indent=2)

class Config:
    # MongoDB configuration
    MONGO_URI = config_data["admin_panel"]["database"].get("uri", "mongodb://mongodb:27017/")
    MONGO_DB = "telegram_bot"  # Hardcoded to telegram_bot
    MONGO_COLLECTIONS = config_data["admin_panel"]["database"].get("collections", {
        "subscriptions": "subscriptions",
        "activity_logs": "activity_logs",
        "admins": "admins"
    })

    # Secret key for session management
    SECRET_KEY = config_data["admin_panel"]["security"]["secret_key"] or secrets.token_hex(16)

    # Other configuration settings
    DEBUG = config_data["admin_panel"]["debug"]

    # Logging configuration
    LOG_LEVEL = config_data["general"]["log_level"]
    LOG_FILE = config_data["general"].get("log_file", os.path.join(LOG_DIR, "admin_panel.log"))

    # Application mode (test or prod)
    MODE = config_data.get("mode", "test")

    # Helper method to get telegram config when needed
    @staticmethod
    def get_telegram_config():
        if "telegram_bot" in config_data:
            return config_data["telegram_bot"]
        return None

    @staticmethod
    def get_mode():
        """Get the application mode (test or prod)"""
        return config_data.get("mode", "test")