#!/usr/bin/env python3
"""
Test script for tenant-specific cookies.
This script tests the tenant-specific cookie functionality by:
1. Creating a simple Flask app with tenant-specific sessions
2. Creating routes to test cookie isolation between tenants
3. Running the app and testing the routes
"""
import sys
import os
import logging
from flask import Flask, session, g, request, jsonify, redirect, url_for, render_template_string

# Add parent directory to path to import modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import tenant session interface
from utils.tenant_session import TenantSessionInterface
from utils.tenant_middleware import TenantMiddleware

# Configure logging
logging.basicConfig(level=logging.DEBUG, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_test_app():
    """Create a test Flask app with tenant-specific sessions"""
    app = Flask(__name__)
    app.config['SECRET_KEY'] = 'test_secret_key'
    app.config['DEBUG'] = True
    
    # Use tenant-specific session interface
    app.session_interface = TenantSessionInterface()
    logger.info("Using tenant-specific session interface")
    
    # Apply tenant middleware
    app.wsgi_app = TenantMiddleware(app.wsgi_app)
    
    # Middleware to handle tenant resolution
    @app.before_request
    def resolve_tenant():
        # Skip for static files
        if request.path.startswith('/static'):
            return None
        
        # Get tenant from environ (set by TenantMiddleware)
        tenant_name = request.environ.get('tenant')
        
        # If we have a tenant
        if tenant_name:
            # Store tenant in session and g
            session['tenant'] = tenant_name
            g.tenant = tenant_name
            logger.debug(f"Resolved tenant: {tenant_name}")
            logger.debug(f"Session: {dict(session)}")
            logger.debug(f"Cookies: {request.cookies}")
        else:
            # No tenant in URL, clear tenant context
            if 'tenant' in session:
                session.pop('tenant')
            if hasattr(g, 'tenant'):
                delattr(g, 'tenant')
        
        return None
    
    # Template for all pages
    page_template = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>{{ title }}</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
            .debug { background: #ffe; padding: 10px; border: 1px solid #ddd; margin-top: 20px; }
        </style>
    </head>
    <body>
        <h1>{{ title }}</h1>
        
        <div>
            <h2>Session Data</h2>
            <pre>{{ session_data }}</pre>
        </div>
        
        <div>
            <h2>Cookies</h2>
            <pre>{{ cookies }}</pre>
        </div>
        
        <div>
            <h2>Actions</h2>
            <ul>
                <li><a href="/tenant1/set-value?key=color&value=red">Set color=red for tenant1</a></li>
                <li><a href="/tenant2/set-value?key=color&value=blue">Set color=blue for tenant2</a></li>
                <li><a href="/tenant1/clear">Clear tenant1 session</a></li>
                <li><a href="/tenant2/clear">Clear tenant2 session</a></li>
            </ul>
        </div>
        
        <div>
            <h2>Navigation</h2>
            <ul>
                <li><a href="/">Home</a></li>
                <li><a href="/tenant1/">Tenant 1 Home</a></li>
                <li><a href="/tenant2/">Tenant 2 Home</a></li>
            </ul>
        </div>
        
        <div class="debug">
            <h2>Debug Info</h2>
            <p><strong>Tenant:</strong> {{ tenant }}</p>
            <p><strong>Session Cookie Name:</strong> {{ session_cookie_name }}</p>
            <p><strong>Request Path:</strong> {{ request_path }}</p>
        </div>
    </body>
    </html>
    """
    
    # Routes for testing
    @app.route('/')
    def index():
        tenant = getattr(g, 'tenant', None)
        session_cookie_name = f"session_{tenant}" if tenant else "session"
        
        return render_template_string(
            page_template,
            title="Cookie Test Home",
            session_data=dict(session),
            cookies=dict(request.cookies),
            tenant=tenant,
            session_cookie_name=session_cookie_name,
            request_path=request.path
        )
    
    @app.route('/set-value')
    def set_value():
        tenant = getattr(g, 'tenant', None)
        key = request.args.get('key', 'test')
        value = request.args.get('value', 'value')
        
        session[key] = value
        session.modified = True
        
        logger.info(f"Set {key}={value} for tenant: {tenant}")
        logger.debug(f"Session after set: {dict(session)}")
        
        return redirect(f"/{tenant}/" if tenant else "/")
    
    @app.route('/clear')
    def clear_session():
        tenant = getattr(g, 'tenant', None)
        
        # Keep tenant in session
        tenant_value = session.get('tenant')
        
        # Clear session
        session.clear()
        
        # Restore tenant
        if tenant_value:
            session['tenant'] = tenant_value
            
        session.modified = True
        
        logger.info(f"Cleared session for tenant: {tenant}")
        logger.debug(f"Session after clear: {dict(session)}")
        
        return redirect(f"/{tenant}/" if tenant else "/")
    
    return app

if __name__ == '__main__':
    app = create_test_app()
    logger.info("Starting test app for tenant-specific cookies")
    logger.info("Test URLs:")
    logger.info("  http://localhost:5000/ - Default route")
    logger.info("  http://localhost:5000/tenant1/ - Tenant 1 route")
    logger.info("  http://localhost:5000/tenant2/ - Tenant 2 route")
    logger.info("  http://localhost:5000/tenant1/set-value?key=color&value=red - Set color=red for tenant1")
    logger.info("  http://localhost:5000/tenant2/set-value?key=color&value=blue - Set color=blue for tenant2")
    app.run(host='0.0.0.0', port=5000)
