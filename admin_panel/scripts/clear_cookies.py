#!/usr/bin/env python3
"""
Scrip<PERSON> to clear all cookies for a tenant.
This script creates a simple Flask app that clears all cookies for a tenant.
"""
import sys
import os
import logging
from flask import Flask, session, g, request, jsonify, redirect, url_for, render_template_string, make_response

# Add parent directory to path to import modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Configure logging
logging.basicConfig(level=logging.DEBUG, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_clear_cookies_app():
    """Create a Flask app that clears all cookies"""
    app = Flask(__name__)
    app.config['SECRET_KEY'] = 'test_secret_key'
    app.config['DEBUG'] = True
    
    # Template for all pages
    page_template = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>{{ title }}</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
            .debug { background: #ffe; padding: 10px; border: 1px solid #ddd; margin-top: 20px; }
        </style>
    </head>
    <body>
        <h1>{{ title }}</h1>
        
        <div>
            <h2>Cookies</h2>
            <pre>{{ cookies }}</pre>
        </div>
        
        <div>
            <h2>Actions</h2>
            <ul>
                <li><a href="/clear-all-cookies">Clear All Cookies</a></li>
                <li><a href="/clear-tenant-cookies?tenant=indx">Clear indx Cookies</a></li>
                <li><a href="/clear-tenant-cookies?tenant=newcustomer">Clear newcustomer Cookies</a></li>
            </ul>
        </div>
        
        <div>
            <h2>Navigation</h2>
            <ul>
                <li><a href="/">Home</a></li>
                <li><a href="/indx/">Go to indx</a></li>
                <li><a href="/newcustomer/">Go to newcustomer</a></li>
            </ul>
        </div>
        
        <div class="debug">
            <h2>Debug Info</h2>
            <p><strong>Request Path:</strong> {{ request_path }}</p>
            <p><strong>Message:</strong> {{ message }}</p>
        </div>
    </body>
    </html>
    """
    
    # Routes for testing
    @app.route('/')
    def index():
        return render_template_string(
            page_template,
            title="Cookie Clearer",
            cookies=dict(request.cookies),
            request_path=request.path,
            message="Use the actions above to clear cookies"
        )
    
    @app.route('/clear-all-cookies')
    def clear_all_cookies():
        # Get all cookies
        cookies = dict(request.cookies)
        
        # Create response
        response = make_response(render_template_string(
            page_template,
            title="All Cookies Cleared",
            cookies=cookies,
            request_path=request.path,
            message=f"Cleared {len(cookies)} cookies"
        ))
        
        # Clear all cookies
        for cookie_name in cookies:
            response.delete_cookie(cookie_name)
            logger.info(f"Deleted cookie: {cookie_name}")
        
        return response
    
    @app.route('/clear-tenant-cookies')
    def clear_tenant_cookies():
        # Get tenant from query parameter
        tenant = request.args.get('tenant')
        
        if not tenant:
            return redirect('/')
        
        # Get all cookies
        cookies = dict(request.cookies)
        
        # Create response
        response = make_response(render_template_string(
            page_template,
            title=f"Cookies Cleared for {tenant}",
            cookies=cookies,
            request_path=request.path,
            message=f"Cleared cookies for tenant: {tenant}"
        ))
        
        # Clear tenant-specific cookies
        tenant_cookies = [name for name in cookies if name.startswith(f"session_{tenant}") or name.startswith(f"debug_{tenant}")]
        for cookie_name in tenant_cookies:
            response.delete_cookie(cookie_name, path=f"/{tenant}")
            logger.info(f"Deleted cookie: {cookie_name} with path: /{tenant}")
        
        return response
    
    return app

if __name__ == '__main__':
    app = create_clear_cookies_app()
    logger.info("Starting cookie clearer app")
    logger.info("Test URLs:")
    logger.info("  http://localhost:5000/ - Home")
    logger.info("  http://localhost:5000/clear-all-cookies - Clear all cookies")
    logger.info("  http://localhost:5000/clear-tenant-cookies?tenant=indx - Clear indx cookies")
    logger.info("  http://localhost:5000/clear-tenant-cookies?tenant=newcustomer - Clear newcustomer cookies")
    app.run(host='0.0.0.0', port=5000)
