#!/usr/bin/env python3
"""
Test script for tenant-specific sessions.
This script tests the tenant-specific session functionality by:
1. Creating a simple Flask app with tenant-specific sessions
2. Creating routes to test session isolation between tenants
3. Running the app and testing the routes
"""
import sys
import os
import logging
from flask import Flask, session, g, request, jsonify, redirect, url_for

# Add parent directory to path to import modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import tenant session interface
from utils.tenant_session import TenantSessionInterface
from utils.tenant_middleware import TenantMiddleware

# Configure logging
logging.basicConfig(level=logging.DEBUG, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_test_app():
    """Create a test Flask app with tenant-specific sessions"""
    app = Flask(__name__)
    app.config['SECRET_KEY'] = 'test_secret_key'
    app.config['DEBUG'] = True
    
    # Use tenant-specific session interface
    app.session_interface = TenantSessionInterface()
    logger.info("Using tenant-specific session interface")
    
    # Apply tenant middleware
    app.wsgi_app = TenantMiddleware(app.wsgi_app)
    
    # Middleware to handle tenant resolution
    @app.before_request
    def resolve_tenant():
        # Skip for static files
        if request.path.startswith('/static'):
            return None
        
        # Get tenant from environ (set by TenantMiddleware)
        tenant_name = request.environ.get('tenant')
        
        # If we have a tenant
        if tenant_name:
            # Store tenant in session and g
            session['tenant'] = tenant_name
            g.tenant = tenant_name
            logger.debug(f"Resolved tenant: {tenant_name}")
        else:
            # No tenant in URL, clear tenant context
            if 'tenant' in session:
                session.pop('tenant')
            if hasattr(g, 'tenant'):
                delattr(g, 'tenant')
        
        return None
    
    # Routes for testing
    @app.route('/')
    def index():
        return jsonify({
            'message': 'Test app for tenant-specific sessions',
            'tenant': getattr(g, 'tenant', None),
            'session_data': dict(session)
        })
    
    @app.route('/login')
    def login():
        # Get tenant from environ or g
        tenant = request.environ.get('tenant') or getattr(g, 'tenant', None)
        
        # Store test data in session
        session['user_id'] = f"user_for_{tenant}" if tenant else "default_user"
        session['logged_in'] = True
        
        return jsonify({
            'message': 'Logged in',
            'tenant': tenant,
            'session_data': dict(session)
        })
    
    @app.route('/logout')
    def logout():
        # Get tenant from environ or g
        tenant = request.environ.get('tenant') or getattr(g, 'tenant', None)
        
        # Clear session data
        session.pop('user_id', None)
        session.pop('logged_in', None)
        
        return jsonify({
            'message': 'Logged out',
            'tenant': tenant,
            'session_data': dict(session)
        })
    
    @app.route('/session')
    def session_info():
        # Get tenant from environ or g
        tenant = request.environ.get('tenant') or getattr(g, 'tenant', None)
        
        # Get session cookie name
        session_cookie_name = f"session_{tenant}" if tenant else "session"
        
        return jsonify({
            'tenant': tenant,
            'session_cookie_name': session_cookie_name,
            'session_data': dict(session)
        })
    
    return app

if __name__ == '__main__':
    app = create_test_app()
    logger.info("Starting test app for tenant-specific sessions")
    logger.info("Test URLs:")
    logger.info("  http://localhost:5000/ - Default route")
    logger.info("  http://localhost:5000/tenant1/ - Tenant 1 route")
    logger.info("  http://localhost:5000/tenant2/ - Tenant 2 route")
    logger.info("  http://localhost:5000/tenant1/login - Login for Tenant 1")
    logger.info("  http://localhost:5000/tenant2/login - Login for Tenant 2")
    logger.info("  http://localhost:5000/tenant1/session - Session info for Tenant 1")
    logger.info("  http://localhost:5000/tenant2/session - Session info for Tenant 2")
    app.run(host='0.0.0.0', port=5000)
