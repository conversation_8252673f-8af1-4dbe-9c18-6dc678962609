"""
Script to test tenant database switching.
This script simulates multiple requests to different tenant URLs and verifies
that the database switching works correctly.
"""
import sys
import os
import logging
from flask import Flask, request, session, g
from werkzeug.test import Client
from werkzeug.wrappers import Response

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add parent directory to path so we can import from admin_panel
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_tenant_db_switching():
    """
    Test tenant database switching by simulating multiple requests.
    """
    # Import MongoDB class
    from utils.mongo_db import MongoDB
    
    # Create a test Flask app
    app = Flask(__name__)
    app.config['TESTING'] = True
    app.config['SECRET_KEY'] = 'test_key'
    
    # Create routes for testing
    @app.route('/<tenant>/test')
    def test_route(tenant):
        # Store tenant in session and g
        session['tenant'] = tenant
        g.tenant = tenant
        
        # Get MongoDB instance
        mongo_db = MongoDB()
        
        # Return current database name
        return f"Current database: {mongo_db.db.name}"
    
    # Create a test client
    client = Client(app)
    
    # Test multiple requests
    logger.info("Testing tenant database switching...")
    
    # First request to tenant1
    logger.info("Request 1: /tenant1/test")
    with app.test_request_context('/tenant1/test'):
        session['tenant'] = 'tenant1'
        g.tenant = 'tenant1'
        mongo_db = MongoDB()
        logger.info(f"Database 1: {mongo_db.db.name}")
    
    # Second request to tenant1 (should use the same database)
    logger.info("Request 2: /tenant1/test")
    with app.test_request_context('/tenant1/test'):
        session['tenant'] = 'tenant1'
        g.tenant = 'tenant1'
        mongo_db = MongoDB()
        logger.info(f"Database 2: {mongo_db.db.name}")
    
    # Third request to tenant2 (should switch database)
    logger.info("Request 3: /tenant2/test")
    with app.test_request_context('/tenant2/test'):
        session['tenant'] = 'tenant2'
        g.tenant = 'tenant2'
        mongo_db = MongoDB()
        logger.info(f"Database 3: {mongo_db.db.name}")
    
    # Fourth request to tenant2 (should use the same database)
    logger.info("Request 4: /tenant2/test")
    with app.test_request_context('/tenant2/test'):
        session['tenant'] = 'tenant2'
        g.tenant = 'tenant2'
        mongo_db = MongoDB()
        logger.info(f"Database 4: {mongo_db.db.name}")
    
    # Fifth request to tenant1 (should switch back to tenant1 database)
    logger.info("Request 5: /tenant1/test")
    with app.test_request_context('/tenant1/test'):
        session['tenant'] = 'tenant1'
        g.tenant = 'tenant1'
        mongo_db = MongoDB()
        logger.info(f"Database 5: {mongo_db.db.name}")
    
    logger.info("Test completed.")

if __name__ == "__main__":
    test_tenant_db_switching()
