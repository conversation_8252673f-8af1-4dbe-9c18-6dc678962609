"""
Script to test tenant-specific URLs.
This script prints out examples of tenant-specific URLs for testing.
"""
import sys
import os

# Add parent directory to path so we can import from admin_panel
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def print_tenant_urls(tenant_name):
    """
    Print examples of tenant-specific URLs for testing.
    
    Args:
        tenant_name (str): The name of the tenant
    """
    print(f"Testing URLs for tenant: {tenant_name}")
    print("-" * 50)
    
    # Base URL
    base_url = f"http://localhost:5001/{tenant_name}"
    
    # Login URL
    print(f"Login URL: {base_url}/login")
    
    # Dashboard URLs
    print(f"Dashboard URL: {base_url}/dashboard/")
    print(f"Access Codes URL: {base_url}/dashboard/access-codes")
    print(f"Users URL: {base_url}/dashboard/users")
    print(f"Verification Requests URL: {base_url}/dashboard/verification-requests")
    print(f"Support Requests URL: {base_url}/dashboard/support-requests")
    
    # API URLs
    print(f"API URL: {base_url}/api/")
    
    print("-" * 50)
    print("To test non-existent tenant, try:")
    print(f"http://localhost:5001/nonexistent_tenant/login")
    print("-" * 50)

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python test_tenant_urls.py <tenant_name>")
        sys.exit(1)
    
    tenant_name = sys.argv[1]
    print_tenant_urls(tenant_name)
