"""
<PERSON><PERSON><PERSON> to create a test tenant database for multi-tenant functionality testing.
This script creates a new MongoDB database with the required collections.
"""
import sys
import os
import logging
from pymongo import MongoClient
from werkzeug.security import generate_password_hash
from datetime import datetime, timezone

# Add parent directory to path so we can import from admin_panel
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_tenant_database(tenant_name):
    """
    Create a new tenant database with the required collections.
    
    Args:
        tenant_name (str): The name of the tenant
    """
    # Validate tenant name
    if not tenant_name or not tenant_name.isalnum():
        logger.error("Tenant name must be alphanumeric")
        return False
    
    # Construct database name
    db_name = f"{tenant_name}_custdb"
    
    try:
        # Connect to MongoDB
        from config import Config
        client = MongoClient(Config.MONGO_URI)
        
        # Check if database already exists
        if db_name in client.list_database_names():
            logger.warning(f"Database '{db_name}' already exists")
            choice = input(f"Database '{db_name}' already exists. Do you want to recreate it? (y/n): ")
            if choice.lower() != 'y':
                logger.info("Operation cancelled")
                return False
            
            # Drop existing database
            client.drop_database(db_name)
            logger.info(f"Dropped existing database '{db_name}'")
        
        # Create new database
        db = client[db_name]
        
        # Create collections
        db.create_collection("activity_logs")
        db.create_collection("admins")
        db.create_collection("access_codes")
        db.create_collection("master_user_data")
        db.create_collection("verification_request")
        db.create_collection("support_request")
        db.create_collection("telegram_bots")
        db.create_collection("api_keys")
        
        # Create default admin user
        admin_data = {
            "username": "admin",
            "password_hash": generate_password_hash("Admin@1234"),
            "created_at": datetime.now(timezone.utc)
        }
        db.admins.insert_one(admin_data)
        
        # Create sample access code
        access_code_data = {
            "code": "TEST123",
            "status": "active",
            "created_at": datetime.now(timezone.utc),
            "expiration_days": 30,
            "expiration_date": datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0) + 
                              timezone.timedelta(days=30),
            "created_by": "system",
            "creation_mode": "script",
            "source": "tenant_setup"
        }
        db.access_codes.insert_one(access_code_data)
        
        logger.info(f"Successfully created tenant database '{db_name}' with required collections")
        logger.info(f"Default admin credentials: username='admin', password='Admin@1234'")
        logger.info(f"Sample access code: 'TEST123'")
        
        return True
    
    except Exception as e:
        logger.error(f"Error creating tenant database: {e}")
        return False

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python create_test_tenant.py <tenant_name>")
        sys.exit(1)
    
    tenant_name = sys.argv[1]
    create_tenant_database(tenant_name)
