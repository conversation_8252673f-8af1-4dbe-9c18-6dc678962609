"""
Script to test tenant URL rewriting.
This script simulates requests to tenant-prefixed URLs and verifies that the URL rewriting works correctly.
"""
import sys
import os
import logging
from flask import Flask, request, g, session
from werkzeug.test import Client
from werkzeug.wrappers import Response

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add parent directory to path so we can import from admin_panel
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_tenant_url_rewriting():
    """
    Test tenant URL rewriting by simulating requests to tenant-prefixed URLs.
    """
    # Import tenant middleware
    from utils.tenant_middleware import TenantMiddleware
    
    # Create a test Flask app
    app = Flask(__name__)
    app.config['TESTING'] = True
    app.config['SECRET_KEY'] = 'test_key'
    
    # Apply tenant middleware
    app.wsgi_app = TenantMiddleware(app.wsgi_app)
    
    # Create routes for testing
    @app.route('/')
    def index():
        tenant = request.environ.get('tenant')
        original_path = request.environ.get('ORIGINAL_PATH_INFO')
        return f"Index route - Tenant: {tenant}, Original path: {original_path}, Current path: {request.path}"
    
    @app.route('/dashboard')
    def dashboard():
        tenant = request.environ.get('tenant')
        original_path = request.environ.get('ORIGINAL_PATH_INFO')
        return f"Dashboard route - Tenant: {tenant}, Original path: {original_path}, Current path: {request.path}"
    
    @app.route('/login')
    def login():
        tenant = request.environ.get('tenant')
        original_path = request.environ.get('ORIGINAL_PATH_INFO')
        return f"Login route - Tenant: {tenant}, Original path: {original_path}, Current path: {request.path}"
    
    # Create a test client
    client = Client(app)
    
    # Test URLs
    test_urls = [
        '/tenant1',
        '/tenant1/',
        '/tenant1/dashboard',
        '/tenant1/login',
        '/tenant2',
        '/tenant2/dashboard',
        '/tenant2/login',
        '/dashboard',  # No tenant
        '/login',      # No tenant
    ]
    
    # Test each URL
    logger.info("Testing tenant URL rewriting...")
    for url in test_urls:
        logger.info(f"Testing URL: {url}")
        response = client.get(url)
        logger.info(f"Response: {response.data.decode('utf-8')}")
        logger.info("-" * 50)
    
    logger.info("Test completed.")

if __name__ == "__main__":
    test_tenant_url_rewriting()
