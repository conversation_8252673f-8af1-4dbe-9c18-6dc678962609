"""
Script to test tenant database switching between different tenant URLs.
This script simulates multiple requests to different tenant URLs and verifies
that the database is switched correctly.
"""
import sys
import os
import logging
import time
from flask import Flask, request, g, session
from werkzeug.test import Client
from werkzeug.wrappers import Response

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add parent directory to path so we can import from admin_panel
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_tenant_db_switching_between_urls():
    """
    Test tenant database switching between different tenant URLs.
    """
    # Import tenant middleware and MongoDB
    from utils.tenant_middleware import TenantMiddleware
    from utils.mongo_db import MongoDB
    
    # Create a test Flask app
    app = Flask(__name__)
    app.config['TESTING'] = True
    app.config['SECRET_KEY'] = 'test_key'
    
    # Apply tenant middleware
    app.wsgi_app = TenantMiddleware(app.wsgi_app)
    
    # Create a middleware to handle tenant resolution
    @app.before_request
    def resolve_tenant():
        # Get tenant from environ
        tenant_name = request.environ.get('tenant')
        
        # If we have a tenant
        if tenant_name:
            # Store tenant in session and g
            session['tenant'] = tenant_name
            g.tenant = tenant_name
            
            # Switch to tenant database
            mongo_db = MongoDB()
            tenant_db_name = f"{tenant_name}_custdb"
            
            # Check if we need to switch databases
            if mongo_db.db.name != tenant_db_name:
                logger.info(f"Switching database from {mongo_db.db.name} to {tenant_db_name}")
                mongo_db.switch_database(tenant_db_name)
    
    # Create routes for testing
    @app.route('/')
    def index():
        mongo_db = MongoDB()
        return f"Index route - Database: {mongo_db.db.name}, Tenant: {session.get('tenant')}"
    
    @app.route('/dashboard')
    def dashboard():
        mongo_db = MongoDB()
        return f"Dashboard route - Database: {mongo_db.db.name}, Tenant: {session.get('tenant')}"
    
    # Create a test client with session support
    client = Client(app, Response)
    client.preserve_context = True
    
    # Test URLs
    test_urls = [
        '/tenant1',
        '/tenant1/dashboard',
        '/tenant2',
        '/tenant2/dashboard',
        '/tenant1',
        '/tenant2',
    ]
    
    # Create a session context
    with app.test_client() as client:
        # Test each URL
        logger.info("Testing tenant database switching between URLs...")
        for url in test_urls:
            logger.info(f"Testing URL: {url}")
            response = client.get(url)
            logger.info(f"Response: {response.data.decode('utf-8')}")
            
            # Get the current database
            with app.app_context():
                mongo_db = MongoDB()
                logger.info(f"Current database: {mongo_db.db.name}")
                logger.info(f"Current tenant: {mongo_db.current_tenant}")
            
            logger.info("-" * 50)
            
            # Sleep to allow logs to be printed in order
            time.sleep(0.1)
    
    logger.info("Test completed.")

if __name__ == "__main__":
    test_tenant_db_switching_between_urls()
