#!/usr/bin/env python3
"""
Test script for tenant-specific login sessions.
This script tests the tenant-specific login functionality by:
1. Creating a simple Flask app with tenant-specific sessions
2. Creating routes to test login/logout for different tenants
3. Running the app and testing the routes
"""
import sys
import os
import logging
from flask import Flask, session, g, request, jsonify, redirect, url_for, render_template_string
from werkzeug.security import generate_password_hash, check_password_hash

# Add parent directory to path to import modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import tenant session interface
from utils.tenant_session import TenantSessionInterface
from utils.tenant_middleware import TenantMiddleware

# Configure logging
logging.basicConfig(level=logging.DEBUG, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Simple in-memory user database for testing
users = {
    'admin': {
        'password_hash': generate_password_hash('password'),
        'id': '1'
    }
}

def create_test_app():
    """Create a test Flask app with tenant-specific sessions"""
    app = Flask(__name__)
    app.config['SECRET_KEY'] = 'test_secret_key'
    app.config['DEBUG'] = True
    
    # Use tenant-specific session interface
    app.session_interface = TenantSessionInterface()
    logger.info("Using tenant-specific session interface")
    
    # Apply tenant middleware
    app.wsgi_app = TenantMiddleware(app.wsgi_app)
    
    # Middleware to handle tenant resolution
    @app.before_request
    def resolve_tenant():
        # Skip for static files
        if request.path.startswith('/static'):
            return None
        
        # Get tenant from environ (set by TenantMiddleware)
        tenant_name = request.environ.get('tenant')
        
        # If we have a tenant
        if tenant_name:
            # Store tenant in session and g
            session['tenant'] = tenant_name
            g.tenant = tenant_name
            logger.debug(f"Resolved tenant: {tenant_name}")
            logger.debug(f"Session: {dict(session)}")
        else:
            # No tenant in URL, clear tenant context
            if 'tenant' in session:
                session.pop('tenant')
            if hasattr(g, 'tenant'):
                delattr(g, 'tenant')
        
        return None
    
    # Login page template
    login_template = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Login - {{ tenant }}</title>
    </head>
    <body>
        <h1>Login for Tenant: {{ tenant }}</h1>
        {% if error %}
        <p style="color: red;">{{ error }}</p>
        {% endif %}
        {% if message %}
        <p style="color: green;">{{ message }}</p>
        {% endif %}
        <form method="post">
            <div>
                <label>Username:</label>
                <input type="text" name="username" required>
            </div>
            <div>
                <label>Password:</label>
                <input type="password" name="password" required>
            </div>
            <button type="submit">Login</button>
        </form>
        <p>Session cookie name: {{ session_name }}</p>
        <p>Current session data: {{ session_data }}</p>
        <hr>
        <h2>Test Links</h2>
        <ul>
            <li><a href="/tenant1/">Tenant 1 Home</a></li>
            <li><a href="/tenant1/login">Tenant 1 Login</a></li>
            <li><a href="/tenant1/dashboard">Tenant 1 Dashboard</a></li>
            <li><a href="/tenant1/logout">Tenant 1 Logout</a></li>
            <li><a href="/tenant2/">Tenant 2 Home</a></li>
            <li><a href="/tenant2/login">Tenant 2 Login</a></li>
            <li><a href="/tenant2/dashboard">Tenant 2 Dashboard</a></li>
            <li><a href="/tenant2/logout">Tenant 2 Logout</a></li>
        </ul>
    </body>
    </html>
    """
    
    # Dashboard template
    dashboard_template = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Dashboard - {{ tenant }}</title>
    </head>
    <body>
        <h1>Dashboard for Tenant: {{ tenant }}</h1>
        <p>You are logged in as: {{ user_id }}</p>
        <p>Session cookie name: {{ session_name }}</p>
        <p>Current session data: {{ session_data }}</p>
        <a href="/{{ tenant }}/logout">Logout</a>
        <hr>
        <h2>Test Links</h2>
        <ul>
            <li><a href="/tenant1/">Tenant 1 Home</a></li>
            <li><a href="/tenant1/login">Tenant 1 Login</a></li>
            <li><a href="/tenant1/dashboard">Tenant 1 Dashboard</a></li>
            <li><a href="/tenant1/logout">Tenant 1 Logout</a></li>
            <li><a href="/tenant2/">Tenant 2 Home</a></li>
            <li><a href="/tenant2/login">Tenant 2 Login</a></li>
            <li><a href="/tenant2/dashboard">Tenant 2 Dashboard</a></li>
            <li><a href="/tenant2/logout">Tenant 2 Logout</a></li>
        </ul>
    </body>
    </html>
    """
    
    # Routes for testing
    @app.route('/')
    def index():
        return jsonify({
            'message': 'Test app for tenant-specific sessions',
            'tenant': getattr(g, 'tenant', None),
            'session_data': dict(session)
        })
    
    @app.route('/login', methods=['GET', 'POST'])
    def login():
        # Get tenant from environ or g
        tenant = request.environ.get('tenant') or getattr(g, 'tenant', None)
        error = None
        message = None
        
        if request.method == 'POST':
            username = request.form.get('username')
            password = request.form.get('password')
            
            if username in users and check_password_hash(users[username]['password_hash'], password):
                # Clear session first
                session.clear()
                
                # Store user ID in session
                session['user_id'] = users[username]['id']
                
                # Store tenant in session
                if tenant:
                    session['tenant'] = tenant
                
                # Force session save
                session.modified = True
                
                logger.info(f"Login successful for user: {username}, tenant: {tenant}")
                logger.debug(f"Session after login: {dict(session)}")
                
                # Redirect to dashboard
                return redirect(f"/{tenant}/dashboard" if tenant else "/dashboard")
            else:
                error = "Invalid username or password"
        
        # Get session cookie name
        session_cookie_name = f"session_{tenant}" if tenant else "session"
        
        return render_template_string(
            login_template, 
            tenant=tenant, 
            error=error, 
            message=message,
            session_name=session_cookie_name,
            session_data=dict(session)
        )
    
    @app.route('/dashboard')
    def dashboard():
        # Get tenant from environ or g
        tenant = request.environ.get('tenant') or getattr(g, 'tenant', None)
        
        # Check if user is logged in
        if 'user_id' not in session:
            logger.warning(f"User not logged in for tenant: {tenant}")
            return redirect(f"/{tenant}/login" if tenant else "/login")
        
        # Get session cookie name
        session_cookie_name = f"session_{tenant}" if tenant else "session"
        
        return render_template_string(
            dashboard_template, 
            tenant=tenant, 
            user_id=session.get('user_id'),
            session_name=session_cookie_name,
            session_data=dict(session)
        )
    
    @app.route('/logout')
    def logout():
        # Get tenant from environ or g
        tenant = request.environ.get('tenant') or getattr(g, 'tenant', None)
        
        logger.info(f"Logout route accessed for tenant: {tenant}")
        logger.debug(f"Session before logout: {dict(session)}")
        
        if 'user_id' in session:
            # Clear only user_id from session, keep tenant information
            session.pop('user_id', None)
            
            # Force session save
            session.modified = True
            
            logger.info(f"User logged out for tenant: {tenant}")
            logger.debug(f"Session after logout: {dict(session)}")
            
            message = "You have been logged out"
        else:
            message = "You were not logged in"
        
        # Get session cookie name
        session_cookie_name = f"session_{tenant}" if tenant else "session"
        
        return render_template_string(
            login_template, 
            tenant=tenant, 
            error=None, 
            message=message,
            session_name=session_cookie_name,
            session_data=dict(session)
        )
    
    return app

if __name__ == '__main__':
    app = create_test_app()
    logger.info("Starting test app for tenant-specific login sessions")
    logger.info("Test URLs:")
    logger.info("  http://localhost:5000/ - Default route")
    logger.info("  http://localhost:5000/tenant1/ - Tenant 1 route")
    logger.info("  http://localhost:5000/tenant2/ - Tenant 2 route")
    logger.info("  http://localhost:5000/tenant1/login - Login for Tenant 1")
    logger.info("  http://localhost:5000/tenant2/login - Login for Tenant 2")
    logger.info("  http://localhost:5000/tenant1/dashboard - Dashboard for Tenant 1")
    logger.info("  http://localhost:5000/tenant2/dashboard - Dashboard for Tenant 2")
    logger.info("  http://localhost:5000/tenant1/logout - Logout for Tenant 1")
    logger.info("  http://localhost:5000/tenant2/logout - Logout for Tenant 2")
    logger.info("Username: admin, Password: password")
    app.run(host='0.0.0.0', port=5000)
