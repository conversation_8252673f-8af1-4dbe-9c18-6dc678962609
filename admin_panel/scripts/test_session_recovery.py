#!/usr/bin/env python3
"""
Test script for session recovery.
This script tests the session recovery functionality by:
1. Creating a simple Flask app with tenant-specific sessions
2. Creating routes to test session recovery
3. Running the app and testing the routes
"""
import sys
import os
import logging
import secrets
from flask import Flask, session, g, request, jsonify, redirect, url_for, render_template_string, make_response

# Add parent directory to path to import modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import tenant session interface
from utils.tenant_session import TenantSessionInterface
from utils.tenant_middleware import TenantMiddleware
from utils.session_serializer import CustomSessionSerializer

# Configure logging
logging.basicConfig(level=logging.DEBUG, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_test_app():
    """Create a test Flask app with tenant-specific sessions"""
    app = Flask(__name__)
    app.config['SECRET_KEY'] = 'test_secret_key'
    app.config['DEBUG'] = True
    
    # Use tenant-specific session interface
    app.session_interface = TenantSessionInterface()
    logger.info("Using tenant-specific session interface")
    
    # Apply tenant middleware
    app.wsgi_app = TenantMiddleware(app.wsgi_app)
    
    # Middleware to handle tenant resolution
    @app.before_request
    def resolve_tenant():
        # Skip for static files
        if request.path.startswith('/static'):
            return None
        
        # Get tenant from environ (set by TenantMiddleware)
        tenant_name = request.environ.get('tenant')
        
        # If we have a tenant
        if tenant_name:
            # Store tenant in session and g
            session['tenant'] = tenant_name
            g.tenant = tenant_name
            logger.debug(f"Resolved tenant: {tenant_name}")
            logger.debug(f"Session: {dict(session)}")
            logger.debug(f"Cookies: {request.cookies}")
        else:
            # No tenant in URL, clear tenant context
            if 'tenant' in session:
                session.pop('tenant')
            if hasattr(g, 'tenant'):
                delattr(g, 'tenant')
        
        return None
    
    # Template for all pages
    page_template = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>{{ title }}</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
            .debug { background: #ffe; padding: 10px; border: 1px solid #ddd; margin-top: 20px; }
        </style>
    </head>
    <body>
        <h1>{{ title }}</h1>
        
        <div>
            <h2>Session Data</h2>
            <pre>{{ session_data }}</pre>
        </div>
        
        <div>
            <h2>Cookies</h2>
            <pre>{{ cookies }}</pre>
        </div>
        
        <div>
            <h2>Actions</h2>
            <ul>
                <li><a href="/{{ tenant }}/login">Login for {{ tenant }}</a></li>
                <li><a href="/{{ tenant }}/dashboard">Dashboard for {{ tenant }}</a></li>
                <li><a href="/{{ tenant }}/logout">Logout for {{ tenant }}</a></li>
                <li><a href="/{{ tenant }}/clear-session">Clear Session for {{ tenant }}</a></li>
                <li><a href="/{{ tenant }}/clear-cookies">Clear Cookies for {{ tenant }}</a></li>
                <li><a href="/{{ tenant }}/set-backup-cookie">Set Backup Cookie for {{ tenant }}</a></li>
            </ul>
        </div>
        
        <div>
            <h2>Navigation</h2>
            <ul>
                <li><a href="/">Home</a></li>
                <li><a href="/tenant1/">Tenant 1 Home</a></li>
                <li><a href="/tenant2/">Tenant 2 Home</a></li>
            </ul>
        </div>
        
        <div class="debug">
            <h2>Debug Info</h2>
            <p><strong>Tenant:</strong> {{ tenant }}</p>
            <p><strong>Session Cookie Name:</strong> {{ session_cookie_name }}</p>
            <p><strong>Request Path:</strong> {{ request_path }}</p>
            <p><strong>Message:</strong> {{ message }}</p>
        </div>
    </body>
    </html>
    """
    
    # Routes for testing
    @app.route('/')
    def index():
        tenant = getattr(g, 'tenant', None)
        session_cookie_name = f"session_{tenant}" if tenant else "session"
        
        return render_template_string(
            page_template,
            title="Session Recovery Test",
            session_data=dict(session),
            cookies=dict(request.cookies),
            tenant=tenant or "none",
            session_cookie_name=session_cookie_name,
            request_path=request.path,
            message="Use the actions above to test session recovery"
        )
    
    @app.route('/login')
    def login():
        tenant = getattr(g, 'tenant', None)
        session_cookie_name = f"session_{tenant}" if tenant else "session"
        
        # Clear session
        session.clear()
        
        # Set admin_id in session
        admin_id = f"admin_{tenant}_{secrets.token_hex(4)}"
        session['admin_id'] = admin_id
        session['tenant'] = tenant
        session['login_time'] = "2025-04-26T12:00:00"
        session['session_id'] = secrets.token_hex(8)
        
        # Force session save
        session.modified = True
        
        # Create response
        response = make_response(render_template_string(
            page_template,
            title=f"Logged in for {tenant}",
            session_data=dict(session),
            cookies=dict(request.cookies),
            tenant=tenant or "none",
            session_cookie_name=session_cookie_name,
            request_path=request.path,
            message=f"Logged in with admin_id: {admin_id}"
        ))
        
        # Set debug cookie
        debug_cookie_name = f"debug_{tenant}"
        debug_cookie_value = f"admin_id={admin_id}"
        response.set_cookie(
            debug_cookie_name,
            debug_cookie_value,
            path=f"/{tenant}",
            httponly=False,
            max_age=3600
        )
        
        # Set backup cookie
        backup_cookie_name = f"backup_{tenant}"
        backup_cookie_value = f"admin_id={admin_id}"
        response.set_cookie(
            backup_cookie_name,
            backup_cookie_value,
            path=f"/{tenant}",
            httponly=False,
            max_age=86400
        )
        
        return response
    
    @app.route('/dashboard')
    def dashboard():
        tenant = getattr(g, 'tenant', None)
        session_cookie_name = f"session_{tenant}" if tenant else "session"
        
        # Check if admin_id is in session
        if 'admin_id' not in session:
            # Check for debug cookie
            debug_cookie_name = f"debug_{tenant}"
            backup_cookie_name = f"backup_{tenant}"
            admin_id = None
            
            # Try to get admin_id from debug cookie
            if debug_cookie_name in request.cookies:
                debug_cookie_value = request.cookies.get(debug_cookie_name)
                if debug_cookie_value.startswith('admin_id='):
                    admin_id = debug_cookie_value.replace('admin_id=', '')
                    logger.warning(f"Found admin_id in debug cookie: {admin_id}")
            
            # Try to get admin_id from backup cookie
            if not admin_id and backup_cookie_name in request.cookies:
                backup_cookie_value = request.cookies.get(backup_cookie_name)
                if backup_cookie_value.startswith('admin_id='):
                    admin_id = backup_cookie_value.replace('admin_id=', '')
                    logger.warning(f"Found admin_id in backup cookie: {admin_id}")
            
            # If we found an admin_id, add it to the session
            if admin_id:
                logger.warning(f"Restoring admin_id from cookie: {admin_id}")
                session['admin_id'] = admin_id
                session['tenant'] = tenant
                session['login_time'] = "2025-04-26T12:00:00"
                session['session_id'] = secrets.token_hex(8)
                session.modified = True
                
                return render_template_string(
                    page_template,
                    title=f"Dashboard for {tenant}",
                    session_data=dict(session),
                    cookies=dict(request.cookies),
                    tenant=tenant or "none",
                    session_cookie_name=session_cookie_name,
                    request_path=request.path,
                    message=f"Session recovered with admin_id: {admin_id}"
                )
            
            # Redirect to login
            return redirect(f"/{tenant}/login")
        
        return render_template_string(
            page_template,
            title=f"Dashboard for {tenant}",
            session_data=dict(session),
            cookies=dict(request.cookies),
            tenant=tenant or "none",
            session_cookie_name=session_cookie_name,
            request_path=request.path,
            message=f"Logged in with admin_id: {session.get('admin_id')}"
        )
    
    @app.route('/logout')
    def logout():
        tenant = getattr(g, 'tenant', None)
        session_cookie_name = f"session_{tenant}" if tenant else "session"
        
        # Get admin_id before clearing session
        admin_id = session.get('admin_id')
        
        # Clear session
        session.clear()
        
        # Keep tenant in session
        if tenant:
            session['tenant'] = tenant
        
        # Force session save
        session.modified = True
        
        return render_template_string(
            page_template,
            title=f"Logged out for {tenant}",
            session_data=dict(session),
            cookies=dict(request.cookies),
            tenant=tenant or "none",
            session_cookie_name=session_cookie_name,
            request_path=request.path,
            message=f"Logged out admin_id: {admin_id}"
        )
    
    @app.route('/clear-session')
    def clear_session():
        tenant = getattr(g, 'tenant', None)
        session_cookie_name = f"session_{tenant}" if tenant else "session"
        
        # Clear session
        session.clear()
        
        # Keep tenant in session
        if tenant:
            session['tenant'] = tenant
        
        # Force session save
        session.modified = True
        
        return render_template_string(
            page_template,
            title=f"Session cleared for {tenant}",
            session_data=dict(session),
            cookies=dict(request.cookies),
            tenant=tenant or "none",
            session_cookie_name=session_cookie_name,
            request_path=request.path,
            message=f"Session cleared for tenant: {tenant}"
        )
    
    @app.route('/clear-cookies')
    def clear_cookies():
        tenant = getattr(g, 'tenant', None)
        session_cookie_name = f"session_{tenant}" if tenant else "session"
        
        # Create response
        response = make_response(render_template_string(
            page_template,
            title=f"Cookies cleared for {tenant}",
            session_data=dict(session),
            cookies=dict(request.cookies),
            tenant=tenant or "none",
            session_cookie_name=session_cookie_name,
            request_path=request.path,
            message=f"Cookies cleared for tenant: {tenant}"
        ))
        
        # Clear tenant-specific cookies
        if tenant:
            response.delete_cookie(f"session_{tenant}", path=f"/{tenant}")
            response.delete_cookie(f"debug_{tenant}", path=f"/{tenant}")
            response.delete_cookie(f"backup_{tenant}", path=f"/{tenant}")
        
        return response
    
    @app.route('/set-backup-cookie')
    def set_backup_cookie():
        tenant = getattr(g, 'tenant', None)
        session_cookie_name = f"session_{tenant}" if tenant else "session"
        
        # Create response
        response = make_response(render_template_string(
            page_template,
            title=f"Backup cookie set for {tenant}",
            session_data=dict(session),
            cookies=dict(request.cookies),
            tenant=tenant or "none",
            session_cookie_name=session_cookie_name,
            request_path=request.path,
            message=f"Backup cookie set for tenant: {tenant}"
        ))
        
        # Set backup cookie
        if tenant:
            admin_id = f"admin_{tenant}_{secrets.token_hex(4)}"
            backup_cookie_name = f"backup_{tenant}"
            backup_cookie_value = f"admin_id={admin_id}"
            response.set_cookie(
                backup_cookie_name,
                backup_cookie_value,
                path=f"/{tenant}",
                httponly=False,
                max_age=86400
            )
            
            logger.info(f"Set backup cookie: {backup_cookie_name}={backup_cookie_value}")
        
        return response
    
    return app

if __name__ == '__main__':
    app = create_test_app()
    logger.info("Starting test app for session recovery")
    logger.info("Test URLs:")
    logger.info("  http://localhost:5000/ - Home")
    logger.info("  http://localhost:5000/tenant1/ - Tenant 1 Home")
    logger.info("  http://localhost:5000/tenant2/ - Tenant 2 Home")
    logger.info("  http://localhost:5000/tenant1/login - Login for Tenant 1")
    logger.info("  http://localhost:5000/tenant2/login - Login for Tenant 2")
    logger.info("  http://localhost:5000/tenant1/dashboard - Dashboard for Tenant 1")
    logger.info("  http://localhost:5000/tenant2/dashboard - Dashboard for Tenant 2")
    app.run(host='0.0.0.0', port=5000)
