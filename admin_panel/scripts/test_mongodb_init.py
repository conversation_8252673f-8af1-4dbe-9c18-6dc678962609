"""
<PERSON><PERSON>t to test MongoDB initialization outside of application context.
This script verifies that the MongoDB class can be initialized without an application context.
"""
import sys
import os
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add parent directory to path so we can import from admin_panel
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_mongodb_init():
    """
    Test MongoDB initialization outside of application context.
    """
    logger.info("Testing MongoDB initialization outside of application context...")
    
    try:
        # Import MongoDB class
        from utils.mongo_db import MongoDB
        
        # Initialize MongoDB
        mongo_db = MongoDB()
        
        # Check if MongoDB is initialized
        logger.info(f"MongoDB initialized successfully")
        logger.info(f"Current database: {mongo_db.db.name}")
        logger.info(f"Current tenant: {mongo_db.current_tenant}")
        
        # Test database switching
        logger.info("Testing database switching...")
        
        # Switch to a different database
        mongo_db.switch_database("test_db")
        logger.info(f"Switched to database: {mongo_db.db.name}")
        
        # Switch back to default database
        mongo_db.switch_database(mongo_db.default_db_name)
        logger.info(f"Switched back to default database: {mongo_db.db.name}")
        
        logger.info("Test completed successfully")
        return True
    except Exception as e:
        logger.error(f"Error testing MongoDB initialization: {e}")
        return False

if __name__ == "__main__":
    test_mongodb_init()
