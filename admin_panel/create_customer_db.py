#!/usr/bin/env python3
"""
Create Customer Database Script

This script creates a new customer database with all required collections and a default admin user.
It's designed for setting up new customer environments in production.

Usage:
    python create_customer_db.py <customer_name>

Example:
    python create_customer_db.py acme

This will create a new MongoDB database named 'acme_custdb' with all required collections
and a default admin user (username: admin, password: Admin@1234).
No test data or sample records will be created.
"""
import sys
import logging
from pymongo import MongoClient
from werkzeug.security import generate_password_hash
from datetime import datetime, timezone

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# No need to add admin_panel to path since we're already in that directory

def create_customer_database(customer_name):
    """
    Create a new customer database with the required collections.

    Args:
        customer_name (str): The name of the customer

    Returns:
        bool: True if successful, False otherwise
    """
    # Validate customer name
    if not customer_name or not customer_name.isalnum():
        logger.error("Customer name must be alphanumeric")
        return False

    # Construct database name
    db_name = f"{customer_name}_custdb"

    try:
        # Connect to MongoDB
        from config import Config
        client = MongoClient(Config.MONGO_URI)

        # Check if database already exists
        if db_name in client.list_database_names():
            logger.warning(f"Database '{db_name}' already exists")
            choice = input(f"Database '{db_name}' already exists. Do you want to recreate it? (y/n): ")
            if choice.lower() != 'y':
                logger.info("Operation cancelled")
                return False

            # Drop existing database
            client.drop_database(db_name)
            logger.info(f"Dropped existing database '{db_name}'")

        # Create new database
        db = client[db_name]

        # Create collections
        collections = [
            "activity_logs",
            "admins",
            "access_codes",
            "master_user_data",
            "verification_request",
            "support_request",
            "telegram_bots",
            "api_keys"
        ]

        for collection in collections:
            db.create_collection(collection)
            logger.debug(f"Created collection: {collection}")

        # Create default admin user
        admin_data = {
            "username": "admin",
            "password_hash": generate_password_hash("Admin@1234"),
            "created_at": datetime.now(timezone.utc)
        }
        db.admins.insert_one(admin_data)

        logger.info(f"Successfully created customer database '{db_name}' with required collections")
        logger.info(f"Default admin credentials: username='admin', password='Admin@1234'")

        return True

    except Exception as e:
        logger.error(f"Error creating customer database: {e}")
        return False

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python create_customer_db.py <customer_name>")
        sys.exit(1)

    customer_name = sys.argv[1]
    success = create_customer_database(customer_name)

    if success:
        sys.exit(0)
    else:
        sys.exit(1)
