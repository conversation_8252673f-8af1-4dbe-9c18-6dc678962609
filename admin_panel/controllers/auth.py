from flask import Blueprint, render_template, request, redirect, flash, session, g
from werkzeug.security import check_password_hash
from utils.mongo_db import MongoDB
from utils.url_helpers import tenant_url_for
import logging
import secrets
from datetime import datetime

logger = logging.getLogger(__name__)
auth_bp = Blueprint('auth', __name__)
mongo_db = MongoDB()

@auth_bp.route('/')
def index():
    # Get tenant from request.environ first (most reliable), then session or g
    tenant = request.environ.get('tenant') or session.get('tenant') or getattr(g, 'tenant', None)

    # Log the current mode and tenant for debugging
    from config import Config
    mode = Config.get_mode()
    logger.debug(f"Index route: mode={mode}, tenant={tenant}")

    # Always use tenant_url_for to generate the correct URL based on the mode
    redirect_url = tenant_url_for('dashboard.home_route')
    logger.debug(f"Redirecting to: {redirect_url}")

    return redirect(redirect_url)

@auth_bp.route('/login', methods=['GET', 'POST'])
def login():
    # Get tenant from request.environ first (most reliable), then session or g
    tenant = request.environ.get('tenant') or session.get('tenant') or getattr(g, 'tenant', None)

    # Log the current mode and tenant for debugging
    from config import Config
    mode = Config.get_mode()
    logger.info(f"Login route accessed for tenant: {tenant}, mode: {mode}")
    logger.debug(f"Session before login: {dict(session)}")
    logger.debug(f"Request cookies: {request.cookies}")
    logger.debug(f"Session cookie name: session_{tenant if tenant else ''}")
    logger.debug(f"Request path: {request.path}, Host: {request.host}")

    if request.method == 'POST':
        username = request.form.get('username')
        password = request.form.get('password')

        logger.info(f"Login attempt for username: {username}, tenant: {tenant}")

        admin = mongo_db.get_admin_by_username(username)

        if admin and check_password_hash(admin['password_hash'], password):
            # Store admin ID as string to avoid ObjectId conversion issues
            session.clear()  # Clear any existing session data first
            session['admin_id'] = str(admin['_id'])

            # Ensure tenant is stored in session
            if tenant:
                session['tenant'] = tenant

            # Add a timestamp to help with debugging
            session['login_time'] = datetime.now().isoformat()

            # Add a unique session identifier for debugging
            session['session_id'] = secrets.token_hex(8)

            # Ensure logged_out flag is not set
            if 'logged_out' in session:
                session.pop('logged_out')

            # Force session save
            session.modified = True

            logger.info(f"Login successful for admin: {username}, tenant: {tenant}")
            logger.debug(f"Session after login: {dict(session)}")

            # Add a flash message with session info for debugging
            flash(f'Login successful!')

            # Log activity
            mongo_db.add_activity_log(
                action="Admin Login",
                details=f"Admin {username} logged in",
                admin_id=str(admin['_id'])
            )

            # Create a response object to ensure cookies are properly set
            # Always use tenant_url_for to generate the correct URL based on the mode
            redirect_url = tenant_url_for('dashboard.home_route')
            logger.info(f"Redirecting to: {redirect_url}")

            # Create response
            response = redirect(redirect_url)

            # Set a custom cookie for debugging if tenant is available
            if tenant:
                # Get the cookie path based on the current mode
                from config import Config
                mode = Config.get_mode()

                # In prod mode with subdomain routing, use root path
                # In test mode or fallback, use tenant-prefixed path
                cookie_path = "/" if mode == "prod" and "." in request.host else f"/{tenant}"

                debug_cookie_name = f"debug_{tenant}"
                debug_cookie_value = f"admin_id={session['admin_id']}"
                response.set_cookie(
                    debug_cookie_name,
                    debug_cookie_value,
                    path=cookie_path,
                    httponly=False,  # Allow JavaScript to read for debugging
                    max_age=3600     # 1 hour expiration
                )

                # Also set a backup cookie with root path for cross-tenant access
                backup_cookie_name = f"backup_{tenant}"
                response.set_cookie(
                    backup_cookie_name,
                    debug_cookie_value,
                    path="/",
                    httponly=False,
                    max_age=3600
                )

            # Log the response cookies
            logger.debug(f"Response cookies: {response.headers.get('Set-Cookie', 'None')}")

            return response

        logger.warning(f"Login failed for username: {username}, tenant: {tenant}")
        flash('Invalid username or password', 'error')

    # Add debug information to the template
    return render_template(
        'login.html',
        now=datetime.now(),
        tenant=tenant,
        session_data=dict(session),
        cookies=request.cookies
    )

@auth_bp.route('/logout')
def logout():
    # Get tenant from request.environ first (most reliable), then session or g
    tenant = request.environ.get('tenant') or session.get('tenant') or getattr(g, 'tenant', None)

    # Log the current mode and tenant for debugging
    from config import Config
    mode = Config.get_mode()
    logger.info(f"Logout route accessed for tenant: {tenant}, mode: {mode}")
    logger.debug(f"Session before logout: {dict(session)}")
    logger.debug(f"Request cookies: {request.cookies}")
    logger.debug(f"Request path: {request.path}, Host: {request.host}")

    if 'admin_id' in session:
        admin_id = session['admin_id']

        # Get admin info for logging
        admin = mongo_db.get_admin_by_id(admin_id)

        # Log activity
        if admin:
            mongo_db.add_activity_log(
                action="Admin Logout",
                details=f"Admin {admin['username']} logged out from tenant: {tenant}",
                admin_id=admin_id
            )

        # Clear admin_id from session, keep tenant information
        session.pop('admin_id', None)

        # Set logged_out flag to prevent session recovery
        session['logged_out'] = True

        # Force session save
        session.modified = True

        logger.info(f"User logged out for tenant: {tenant}")
        logger.debug(f"Session after logout: {dict(session)}")

        flash('You have been logged out', 'info')
    else:
        logger.warning(f"Logout attempted but no admin_id in session for tenant: {tenant}")

    # Note: We don't clear tenant from session to maintain tenant context

    # Create response to clear cookies
    # Always use tenant_url_for to generate the correct URL based on the mode
    redirect_url = tenant_url_for('auth.login')
    logger.info(f"Redirecting to: {redirect_url}")
    response = redirect(redirect_url)

    # Clear cookies if tenant is available
    if tenant:
        # Get the cookie path based on the current mode
        from config import Config
        mode = Config.get_mode()

        # In prod mode with subdomain routing, use root path
        # In test mode or fallback, use tenant-prefixed path
        cookie_path = "/" if mode == "prod" and "." in request.host else f"/{tenant}"

        debug_cookie_name = f"debug_{tenant}"
        backup_cookie_name = f"backup_{tenant}"

        # Delete cookies with the correct path
        response.delete_cookie(debug_cookie_name, path=cookie_path)
        response.delete_cookie(backup_cookie_name, path=cookie_path)

        # Also try deleting cookies with root path (in case they were set incorrectly)
        response.delete_cookie(debug_cookie_name, path="/")
        response.delete_cookie(backup_cookie_name, path="/")

        # Log that cookies were cleared
        logger.info(f"Cleared cookies for tenant: {tenant}")

    return response