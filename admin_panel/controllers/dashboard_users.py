# admin_panel/controllers/dashboard_users.py
from flask import render_template, redirect, flash, request, session, jsonify
from utils.db_common import mongo_db
from utils.url_helpers import tenant_url_for
import logging
from datetime import datetime, timezone
# Change from relative to absolute import
from utils.telegram_utils import (
    check_telegram_membership,
    create_telegram_invite_link,
    send_telegram_message,
    kick_telegram_user,
    unban_telegram_user,
    get_bot_credentials
)

logger = logging.getLogger(__name__)
# Using mongo_db from utils.db_common

def user_detail(user_id):
    """Display detailed information for a specific user"""
    admin_id = session.get('admin_id') # Assumes login_required decorator applied

    try:
        # Ensure user_id is an integer
        user_id = int(user_id)
    except ValueError:
        flash('Invalid User ID format.', 'error')
        return redirect(tenant_url_for('dashboard.home_route')) # Use correct endpoint name

    subscription = mongo_db.get_subscription(user_id)

    if not subscription:
        flash(f'User with ID {user_id} not found in master_user_data.', 'warning')
        # Decide: redirect home or show a basic page indicating user not found?
        # Showing a message on home might be better.
        # Let's stick to redirect for now as per original logic.
        return redirect(tenant_url_for('dashboard.home_route'))

    # Log viewing activity
    if admin_id:
        admin = mongo_db.get_admin_by_id(admin_id)
        admin_username = admin['username'] if admin else f"Admin ID {admin_id}"
        mongo_db.add_activity_log(
            action="View User Detail",
            details=f"Admin {admin_username} viewed details for user ID {user_id}",
            admin_id=admin_id
        )

    # Check Telegram channel membership status
    telegram_status = check_telegram_membership(user_id)

    # Get current time for display purposes (e.g., calculating 'days remaining')
    now = datetime.now(timezone.utc)

    # Get access code details including expiration date
    access_code_details = None
    if subscription and 'access_code' in subscription:
        access_code_details = mongo_db.db["access_code"].find_one({"code": subscription['access_code']})

    # Get channel ID from database
    _, channel_id = get_bot_credentials()
    if not channel_id:
        logger.warning(f"No channel ID found in database when viewing user {user_id}")
        channel_id = ""  # Empty string as fallback

    return render_template('user_detail.html',
                          subscription=subscription,
                          user_id=user_id,
                          now=now,
                          telegram_status=telegram_status,
                          channel_id=channel_id, # Get channel_id from database
                          access_code_details=access_code_details)

def send_telegram_invite(user_id):
    admin_id = session.get('admin_id')
    if not admin_id:
         # This should ideally be caught by @login_required, but double-check
         return jsonify({"success": False, "message": "Not authorized"}), 401

    try:
        # Ensure user_id is valid (integer)
        user_id = int(user_id)
    except ValueError:
        return jsonify({"success": False, "message": "Invalid User ID format."}), 400

    data = request.json
    if not data:
         return jsonify({"success": False, "message": "Invalid request data."}), 400

    custom_message = data.get('message', '').strip()

    # 1. Create invite link
    invite_link, error = create_telegram_invite_link()
    if error or not invite_link:
        return jsonify({"success": False, "message": f"Failed to create invite link: {error}"}), 500

    # 2. Construct a beautiful message with HTML formatting
    message_text = f"<b>🔔 Invitation to Our Channel</b>\n\n" \
                 f"You've been invited to join our exclusive Telegram channel!\n\n" \
                 f"<b>Important Information:</b>\n" \
                 f"• This is a personal, one-time use invite link\n" \
                 f"• The link will expire in 7 days\n" \
                 f"• Click the button below to join\n"

    if custom_message:
        message_text = f"<b>🔔 Invitation to Our Channel</b>\n\n" \
                     f"{custom_message}\n\n" \
                     f"<b>Important Information:</b>\n" \
                     f"• This is a personal, one-time use invite link\n" \
                     f"• The link will expire in 7 days\n" \
                     f"• Click the button below to join\n"

    # Create a button for the invite link
    button = {
        'text': '🚀 Join Our Channel',
        'url': invite_link
    }

    # 3. Send message to user with the button
    sent, send_error = send_telegram_message(user_id, message_text, inline_button=button)

    if not sent:
         # Attempt to clean up the created link if sending failed? Maybe not critical.
         return jsonify({"success": False, "message": f"Invite link created, but failed to send message: {send_error}"}), 500

    # 4. Log activity
    admin = mongo_db.get_admin_by_id(admin_id)
    admin_username = admin['username'] if admin else f"Admin ID {admin_id}"
    mongo_db.add_activity_log(
        action="Send Telegram Invite",
        details=f"Admin {admin_username} sent Telegram invite link to user ID {user_id}",
        admin_id=admin_id
    )

    return jsonify({"success": True, "message": "Invite link created and sent successfully."})

    # Error handling for the overall function
    # except Exception as e: # Catch broader exceptions
    #     logger.error(f"Error sending Telegram invite to user {user_id}: {e}", exc_info=True)
    #     return jsonify({"success": False, "message": f"An unexpected server error occurred: {str(e)}"}), 500
    # Specific exceptions (like ValueError) are handled above.

def kick_user(user_id):
    """Kick (ban) a user from the Telegram channel"""
    admin_id = session.get('admin_id') # Assumes login_required

    try:
        user_id = int(user_id)
    except ValueError:
         return jsonify({"success": False, "message": "Invalid User ID format."}), 400

    if not admin_id:
         return jsonify({"success": False, "message": "Not authorized"}), 401

    success, error = kick_telegram_user(user_id)

    if success:
        admin = mongo_db.get_admin_by_id(admin_id)
        admin_username = admin['username'] if admin else f"Admin ID {admin_id}"
        mongo_db.add_activity_log(
            action="Kick User",
            details=f"Admin {admin_username} kicked user ID {user_id} from Telegram channel",
            admin_id=admin_id
        )
        return jsonify({"success": True, "message": f"User {user_id} kicked successfully."})
    else:
        return jsonify({"success": False, "message": f"Failed to kick user {user_id}: {error}"})

def unban_user(user_id):
    """Unban a user from the Telegram channel"""
    admin_id = session.get('admin_id') # Assumes login_required

    try:
        user_id = int(user_id)
    except ValueError:
         return jsonify({"success": False, "message": "Invalid User ID format."}), 400

    if not admin_id:
         return jsonify({"success": False, "message": "Not authorized"}), 401

    success, error = unban_telegram_user(user_id)

    if success:
        admin = mongo_db.get_admin_by_id(admin_id)
        admin_username = admin['username'] if admin else f"Admin ID {admin_id}"
        mongo_db.add_activity_log(
            action="Unban User",
            details=f"Admin {admin_username} unbanned user ID {user_id} from Telegram channel",
            admin_id=admin_id
        )
        return jsonify({"success": True, "message": f"User {user_id} unbanned successfully."})
    else:
        # Provide specific error if user wasn't banned
        if error and "user is not banned" in error.lower():
             return jsonify({"success": True, "message": f"User {user_id} is already not banned."}) # Still success=true
        else:
             return jsonify({"success": False, "message": f"Failed to unban user {user_id}: {error}"})