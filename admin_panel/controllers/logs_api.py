"""
API routes for handling log files
"""
from flask import Blueprint, request, jsonify, session, g
from utils.decorators import login_required
import logging
import os
import glob
import re
from datetime import datetime

logger = logging.getLogger(__name__)
logs_api_bp = Blueprint('logs_api', __name__, url_prefix='/api/logs')

@logs_api_bp.route('/customer', methods=['GET'])
@login_required
def get_customer_logs():
    """
    Get customer-specific logs from the auto_runner logs directory

    Returns:
        JSON response with log content
    """
    try:
        # Get tenant name from request
        tenant = request.environ.get('tenant')
        if not tenant:
            tenant = getattr(g, 'tenant', None) or session.get('tenant')

        if not tenant:
            return jsonify({
                "success": False,
                "error": "No tenant found",
                "message": "Could not determine the current tenant"
            }), 400

        # Define the path to the customer logs directory
        logs_dir = '/app/customer_logs'

        # Check if the directory exists
        if not os.path.exists(logs_dir):
            logger.warning(f"Customer logs directory {logs_dir} does not exist")
            return jsonify({
                "success": True,
                "log_file": "sample.log",
                "file_info": {
                    "size": 0,
                    "size_formatted": "0 B",
                    "modified": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                },
                "log_content": f"No logs found for tenant: {tenant}\nThe customer logs directory does not exist."
            })

        # Get log file name from query parameters (optional)
        log_file = request.args.get('file', None)

        # If no specific file is requested, find the most appropriate one for this tenant
        if not log_file:
            # Get all .log files in the directory
            log_files = []
            for file_path in glob.glob(os.path.join(logs_dir, '*.log')):
                file_name = os.path.basename(file_path)

                # Get file stats
                try:
                    stats = os.stat(file_path)
                    size = stats.st_size
                    modified = datetime.fromtimestamp(stats.st_mtime)

                    # Check if the file name contains the tenant name
                    tenant_match = re.search(re.escape(tenant), file_name, re.IGNORECASE)

                    log_files.append({
                        "name": file_name,
                        "path": file_path,
                        "size": size,
                        "modified": modified,
                        "modified_str": modified.strftime('%Y-%m-%d %H:%M:%S'),
                        "tenant_match": bool(tenant_match)
                    })
                except Exception as e:
                    logger.error(f"Error getting stats for {file_path}: {e}")

            # If we have log files, find the best match
            if log_files:
                # First, try to find files that match the tenant name
                tenant_matches = [f for f in log_files if f["tenant_match"]]

                if tenant_matches:
                    # Sort by modified date (newest first) and take the first one
                    tenant_matches.sort(key=lambda x: x["modified"], reverse=True)
                    log_file = tenant_matches[0]["name"]
                    logger.info(f"Found tenant-matching log file: {log_file}")
                else:
                    # If no tenant-specific file, just take the newest log file
                    log_files.sort(key=lambda x: x["modified"], reverse=True)
                    log_file = log_files[0]["name"]
                    logger.info(f"No tenant-matching log file found, using newest: {log_file}")
            else:
                # No log files found, return a sample response
                logger.warning(f"No log files found in {logs_dir}")
                return jsonify({
                    "success": True,
                    "log_file": "sample.log",
                    "file_info": {
                        "size": 0,
                        "size_formatted": "0 B",
                        "modified": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    },
                    "log_content": f"No logs found for tenant: {tenant}\nThe customer logs directory is empty."
                })

        # Now we have a log file, get its path
        log_path = os.path.join(logs_dir, log_file)

        # Validate the log file path to prevent directory traversal
        if not os.path.normpath(log_path).startswith(logs_dir) or '..' in log_path:
            return jsonify({
                "success": False,
                "error": "Invalid log file",
                "message": "The specified log file is invalid"
            }), 400

        # Check if the log file exists
        if not os.path.exists(log_path) or not os.path.isfile(log_path):
            logger.warning(f"Requested log file {log_path} does not exist")
            return jsonify({
                "success": True,
                "log_file": log_file,
                "file_info": {
                    "size": 0,
                    "size_formatted": "0 B",
                    "modified": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                },
                "log_content": f"The requested log file '{log_file}' does not exist.\nNo logs found for tenant: {tenant}"
            })

        # Get file stats for the response
        try:
            stats = os.stat(log_path)
            file_size = stats.st_size
            file_modified = datetime.fromtimestamp(stats.st_mtime).strftime('%Y-%m-%d %H:%M:%S')
        except Exception as e:
            logger.error(f"Error getting stats for {log_path}: {e}")
            file_size = 0
            file_modified = "Unknown"

        # Read the log file content
        try:
            with open(log_path, 'r') as f:
                # Get the last 100 lines (or fewer if the file is smaller)
                lines = f.readlines()
                if len(lines) > 100:
                    lines = lines[-100:]

                # Filter lines that contain the tenant name (case-insensitive)
                tenant_pattern = re.compile(re.escape(tenant), re.IGNORECASE)
                filtered_lines = [line for line in lines if tenant_pattern.search(line)]

                # If no tenant-specific lines found, return all lines
                if not filtered_lines:
                    filtered_lines = lines

                log_content = ''.join(filtered_lines)
        except Exception as e:
            logger.error(f"Error reading log file {log_path}: {e}")
            return jsonify({
                "success": False,
                "error": "Error reading log file",
                "message": str(e)
            }), 500

        # Format file size for display
        if file_size < 1024:
            size_str = f"{file_size} B"
        elif file_size < 1024 * 1024:
            size_str = f"{(file_size / 1024):.1f} KB"
        else:
            size_str = f"{(file_size / (1024 * 1024)):.1f} MB"

        return jsonify({
            "success": True,
            "log_file": log_file,
            "file_info": {
                "size": file_size,
                "size_formatted": size_str,
                "modified": file_modified
            },
            "log_content": log_content
        })

    except Exception as e:
        logger.error(f"Error getting customer logs: {e}", exc_info=True)
        return jsonify({
            "success": False,
            "error": "Server error",
            "message": str(e)
        }), 500
