"""
Broker API routes for the admin panel
"""
from flask import Blueprint, request, jsonify, session, g
from utils.decorators import login_required
from utils.db_common import mongo_db
from utils.url_helpers import tenant_url_for
from utils.crypto_utils import encrypt_data, decrypt_data
import logging
import os
import json
import http.client
import socket
import urllib.parse
from datetime import datetime, timezone, timedelta

logger = logging.getLogger(__name__)
broker_api_bp = Blueprint('broker_api', __name__, url_prefix='/api/broker')

def get_tenant():
    """Get tenant from request.environ, g, or session"""
    tenant = None
    if hasattr(request, 'environ') and 'tenant' in request.environ:
        tenant = request.environ.get('tenant')
    if not tenant and hasattr(g, 'tenant'):
        tenant = g.tenant
    if not tenant and 'tenant' in session:
        tenant = session.get('tenant')

    # Log the tenant and current database
    if tenant:
        logger.info(f"Current tenant: {tenant}")
        expected_db = f"{tenant}_custdb"
        current_db = mongo_db.db.name
        logger.info(f"Expected database: {expected_db}, Current database: {current_db}")

        # Check if we're using the correct database
        if current_db != expected_db:
            logger.warning(f"Database mismatch! Expected: {expected_db}, Using: {current_db}")
            # Force database switch
            mongo_db.switch_database(expected_db)
            logger.info(f"Switched to database: {mongo_db.db.name}")
    else:
        logger.warning("No tenant found in request.environ, g, or session")

    return tenant

def get_tenant_broker_setup_url():
    """Get the tenant-specific URL for the broker setup page"""
    tenant = get_tenant()
    if tenant:
        return f"/{tenant}/dashboard/broker-setup"
    else:
        return "/dashboard/broker-setup"

# Constants for Exness API
API_HOST = "my.exnessaffiliates.com"
AUTH_ENDPOINT = "/api/v2/auth"

def follow_redirects(host, path, method="GET", headers=None, body=None, max_redirects=5):
    """
    Make an HTTP request and follow redirects manually.

    Args:
        host: The host to connect to
        path: The path to request
        method: HTTP method (GET, POST, etc.)
        headers: HTTP headers dictionary
        body: Request body (for POST, PUT, etc.)
        max_redirects: Maximum number of redirects to follow

    Returns:
        Tuple of (status_code, response_data, final_url, redirect_history)
    """
    if headers is None:
        headers = {}

    # Add common headers that help avoid bot detection
    if 'User-Agent' not in headers:
        headers['User-Agent'] = 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'

    redirect_history = []
    current_host = host
    current_path = path
    current_url = f"https://{current_host}{current_path}"

    for redirect_count in range(max_redirects + 1):
        try:
            logger.info(f"Making request to: {current_url}")
            conn = http.client.HTTPSConnection(current_host, timeout=10)

            # Make the request
            conn.request(method, current_path, body, headers)
            response = conn.getresponse()
            status = response.status

            # Add to redirect history
            redirect_history.append({
                'url': current_url,
                'status': status
            })

            # Check if this is a redirect
            if status in (301, 302, 303, 307, 308):
                # Get the redirect location
                location = response.getheader('Location')
                if not location:
                    logger.warning(f"Received redirect status {status} but no Location header")
                    break

                logger.info(f"Following redirect ({status}) to: {location}")

                # Parse the redirect URL
                if location.startswith('http'):
                    # Absolute URL
                    parsed = urllib.parse.urlparse(location)
                    current_host = parsed.netloc
                    current_path = parsed.path
                    if parsed.query:
                        current_path += f"?{parsed.query}"
                else:
                    # Relative URL
                    current_path = location

                current_url = f"https://{current_host}{current_path}"

                # For POST redirected to GET (303 and sometimes 302)
                if status in (303, 302) and method == "POST":
                    method = "GET"
                    body = None

                # Read and discard the response body
                response.read()
                conn.close()

                # Continue to the next redirect
                continue

            # Not a redirect, read the response
            data = response.read().decode('utf-8')
            conn.close()

            return status, data, current_url, redirect_history

        except Exception as e:
            logger.error(f"Error during HTTP request: {str(e)}")
            return None, str(e), current_url, redirect_history

    # If we get here, we've exceeded the maximum number of redirects
    logger.warning(f"Exceeded maximum number of redirects ({max_redirects})")
    return None, "Exceeded maximum number of redirects", current_url, redirect_history

def ensure_broker_setup_collection():
    """
    Ensure that the broker_setup collection exists in the current database
    """
    try:
        # Get tenant and check database
        tenant = get_tenant()
        expected_db = f"{tenant}_custdb" if tenant else "default_custdb"
        current_db = mongo_db.db.name

        logger.info(f"Ensuring broker_setup collection in database: {current_db}")
        logger.info(f"Expected database: {expected_db}, Current database: {current_db}")

        # Force database switch if needed
        if current_db != expected_db:
            logger.warning(f"Database mismatch! Expected: {expected_db}, Using: {current_db}")
            mongo_db.switch_database(expected_db)
            logger.info(f"Switched to database: {mongo_db.db.name}")

        # Check if broker_setup collection exists
        collections = mongo_db.db.list_collection_names()
        logger.info(f"Available collections: {collections}")

        if "broker_setup" not in collections:
            logger.info(f"Creating broker_setup collection in {mongo_db.db.name}")
            mongo_db.db.create_collection("broker_setup")
            logger.info("broker_setup collection created successfully")

            # Verify collection was created
            collections = mongo_db.db.list_collection_names()
            if "broker_setup" in collections:
                logger.info("Verified broker_setup collection exists")
            else:
                logger.error("Failed to create broker_setup collection!")
                return False
        else:
            logger.info("broker_setup collection already exists")

            # Check if collection has any documents
            count = mongo_db.db["broker_setup"].count_documents({})
            logger.info(f"broker_setup collection has {count} documents")

        return True
    except Exception as e:
        logger.error(f"Error ensuring broker_setup collection: {e}", exc_info=True)
        return False

@broker_api_bp.route('/authenticate', methods=['POST'])
@login_required
def authenticate_broker():
    """
    Authenticate with the broker API using provided credentials

    Accepts:
        - username: Broker API username
        - password: Broker API password

    Returns:
        JSON response with authentication result
    """
    try:
        # Ensure broker_setup collection exists
        ensure_broker_setup_collection()

        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "Invalid request",
                "message": "Request body must be valid JSON"
            }), 400

        username = data.get('username')
        password = data.get('password')
        dev_mode = data.get('dev_mode', False)

        if not username or not password:
            return jsonify({
                "success": False,
                "error": "Missing credentials",
                "message": "Both username and password are required"
            }), 400

        # Set development mode environment variable if requested
        if dev_mode:
            logger.info("Development mode enabled for this request")
            os.environ['FLASK_ENV'] = 'development'

        # Authenticate with broker API
        auth_result = authenticate(username, password)

        # Reset environment variable
        if dev_mode and 'FLASK_ENV' in os.environ:
            del os.environ['FLASK_ENV']

        if auth_result["success"]:
            # Log successful authentication
            admin_id = session.get('admin_id', 'unknown')
            tenant = get_tenant()

            # Log with tenant information
            logger.info(f"Broker API authentication successful for tenant: {tenant}, admin: {admin_id}")

            # Get broker selection from request
            broker_selection = data.get('broker', 'exness')
            mode = data.get('mode', 'auto')

            # Calculate expiry date (30 days from now)
            now = datetime.now(timezone.utc)
            expiry_date = now + timedelta(days=30)

            # Get customer_name_custdb from tenant
            customer_name_custdb = f"{tenant}_custdb" if tenant else "default_custdb"

            # Encrypt sensitive credentials
            encrypted_username = encrypt_data(username)
            encrypted_password = encrypt_data(password)

            # Store token and encrypted credentials in MongoDB broker_setup collection
            broker_setup_data = {
                "jwt_token": auth_result["token"],
                "jwt_token_valid": True,
                "jwt_token_status": "Generated (not validated)",
                "expiry_date": expiry_date,
                "mode": mode,
                "active_broker": broker_selection,
                "customer_name_custdb": customer_name_custdb,
                "encrypted_username": encrypted_username,
                "encrypted_password": encrypted_password,
                "created_by": admin_id,
                "created_at": now,
                "last_updated": now
            }

            # Log the database and collection we're using
            logger.info(f"Using database: {mongo_db.db.name}, collection: broker_setup")
            logger.info(f"Storing broker setup data: {broker_setup_data}")

            try:
                # Ensure the collection exists
                if "broker_setup" not in mongo_db.db.list_collection_names():
                    logger.info(f"Creating broker_setup collection in {mongo_db.db.name}")
                    mongo_db.db.create_collection("broker_setup")

                # Upsert the broker setup data (update if exists, insert if not)
                result = mongo_db.db["broker_setup"].update_one(
                    {"active_broker": broker_selection},
                    {"$set": broker_setup_data},
                    upsert=True
                )

                logger.info(f"Upsert result: matched={result.matched_count}, modified={result.modified_count}, upserted_id={result.upserted_id}")
            except Exception as db_error:
                logger.error(f"Database error: {db_error}", exc_info=True)
                raise

            # Log activity
            mongo_db.add_activity_log(
                action="Broker API Authentication",
                details=f"Successfully authenticated with broker API for tenant: {tenant}, broker: {broker_selection}, mode: {mode}",
                admin_id=admin_id
            )

            return jsonify({
                "success": True,
                "token": auth_result["token"],
                "message": "Authentication successful"
            })
        else:
            return jsonify({
                "success": False,
                "error": "Authentication failed",
                "message": auth_result["message"]
            }), 401

    except Exception as e:
        logger.error(f"Error authenticating with broker API: {e}", exc_info=True)
        return jsonify({
            "success": False,
            "error": "Server error",
            "message": str(e)
        }), 500

@broker_api_bp.route('/token-status', methods=['GET'])
@login_required
def token_status():
    """
    Get the current broker token status

    Returns:
        JSON response with token status
    """
    try:
        # Ensure broker_setup collection exists
        ensure_broker_setup_collection()

        # Log tenant and database information
        tenant = get_tenant()
        logger.info(f"Token status request for tenant: {tenant}")
        logger.info(f"Using database: {mongo_db.db.name}")

        # Check if broker_setup collection exists
        collections = mongo_db.db.list_collection_names()
        logger.info(f"Available collections: {collections}")

        if "broker_setup" not in collections:
            logger.warning(f"broker_setup collection does not exist in {mongo_db.db.name}")
            return jsonify({
                "success": True,
                "has_token": False,
                "message": "Broker setup collection does not exist"
            })

        # Get broker setup data from MongoDB
        broker_setup = mongo_db.db["broker_setup"].find_one(
            {},
            sort=[("last_updated", -1)]  # Get the most recently updated record
        )

        logger.info(f"Found broker setup: {broker_setup}")

        # Check if broker setup data exists and has a valid token
        if broker_setup and broker_setup.get("jwt_token"):
            # Check if token is expired
            expiry_date = broker_setup.get("expiry_date")
            is_expired = False

            if expiry_date:
                # Check if expiry_date is timezone-aware
                if hasattr(expiry_date, 'tzinfo') and expiry_date.tzinfo is not None:
                    # Compare with timezone-aware datetime
                    is_expired = datetime.now(timezone.utc) > expiry_date
                    logger.info(f"Token expiry check (timezone-aware): now={datetime.now(timezone.utc)}, expiry={expiry_date}, is_expired={is_expired}")
                else:
                    # Convert naive datetime to aware for comparison
                    # First make sure we're comparing in UTC
                    now = datetime.now()
                    logger.info(f"Token expiry check (timezone-naive): now={now}, expiry={expiry_date}")
                    is_expired = now > expiry_date
                    logger.info(f"Expiry result: {is_expired}")
            else:
                logger.warning("No expiry date found in broker setup")

            # Get token validity from database
            jwt_token_valid = broker_setup.get("jwt_token_valid", False)
            logger.info(f"Token validity from database: jwt_token_valid={jwt_token_valid}")

            # Calculate overall validity
            is_valid = jwt_token_valid and not is_expired
            logger.info(f"Overall token validity: is_valid={is_valid} (jwt_token_valid={jwt_token_valid}, not is_expired={not is_expired})")

            # Format expiry date for display
            expiry_str = ""
            if expiry_date:
                expiry_str = expiry_date.isoformat()

            # Get mode and broker
            mode = broker_setup.get("mode", "auto")
            active_broker = broker_setup.get("active_broker", "exness")
            logger.info(f"Mode from database: {mode}, Active broker: {active_broker}")

            # Get token status
            jwt_token_status = broker_setup.get("jwt_token_status", "Not validated")
            logger.info(f"Token status from database: {jwt_token_status}")

            # Get customer_name_custdb
            customer_name_custdb = broker_setup.get("customer_name_custdb", "")

            # Prepare response - don't include the actual token or credentials for security
            response_data = {
                "success": True,
                "has_token": True,
                "expiry": expiry_str,
                "is_expired": is_expired,
                "is_valid": is_valid,
                "token_status": jwt_token_status,
                "mode": mode,
                "active_broker": active_broker,
                "customer_name_custdb": customer_name_custdb
            }

            logger.info(f"Token status response: {response_data}")

            return jsonify(response_data)
        else:
            return jsonify({
                "success": True,
                "has_token": False
            })

    except Exception as e:
        logger.error(f"Error getting broker token status: {e}", exc_info=True)
        return jsonify({
            "success": False,
            "error": "Server error",
            "message": str(e)
        }), 500

@broker_api_bp.route('/validate-token', methods=['POST'])
@login_required
def validate_token():
    """
    Validate the broker API token stored in the database

    Returns:
        JSON response with validation result
    """
    try:
        # Ensure broker_setup collection exists
        ensure_broker_setup_collection()

        # Get admin ID for database lookup
        admin_id = session.get('admin_id', 'unknown')

        # Find the broker setup record for this admin
        broker_setup = mongo_db.db["broker_setup"].find_one({})

        if not broker_setup or not broker_setup.get("jwt_token"):
            return jsonify({
                "success": False,
                "error": "No token found",
                "message": "No token has been generated yet. Please generate a token first."
            }), 400

        # Get the token from the database
        token = broker_setup.get("jwt_token")

        # Validate token by making an API call to Exness Affiliates API
        try:
            # Prepare headers with browser-like user agent to avoid WAF detection
            headers = {
                'Content-Type': 'application/json',
                'Authorization': token,
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'application/json, text/plain, */*',
                'Accept-Language': 'en-US,en;q=0.9',
                'Origin': f'https://{API_HOST}',
                'Referer': f'https://{API_HOST}/'
            }

            # Use our custom follow_redirects function
            # Use the correct token validation endpoint
            validation_endpoint = "/api/v2/auth/token/"
            logger.info(f"Using validation endpoint: {validation_endpoint}")

            status, data, final_url, redirect_history = follow_redirects(
                API_HOST,
                validation_endpoint,
                method="GET",
                headers=headers
            )

            # Log the response for debugging
            logger.info(f"API Response Status: {status}")
            logger.info(f"API Response URL (after redirects): {final_url}")

            # Check if there was a redirect
            if len(redirect_history) > 1:
                redirect_chain = []
                for r in redirect_history:
                    redirect_chain.append(f"{r['status']} {r['url']}")
                redirect_str = " -> ".join(redirect_chain)
                logger.info(f"Request was redirected: {redirect_str}")

            # Check if the request was successful (status code 200) and has the expected response
            is_valid = False
            token_status = "Unknown"

            if status == 200:
                try:
                    if data:
                        logger.debug(f"API Response Data: {data}")

                        # Try to parse as JSON
                        response_data = json.loads(data)

                        # Check for the expected "Ok" message
                        if response_data.get('message') == 'Ok':
                            is_valid = True
                            token_status = "Valid"
                            logger.info(f"Token validation successful: {response_data}")
                        else:
                            token_status = f"Unexpected response: {response_data.get('message', 'Unknown')}"
                            logger.warning(f"Unexpected response format: {response_data}")
                    else:
                        token_status = "Empty response"
                        logger.warning("Empty response data despite 200 status code")
                except json.JSONDecodeError:
                    token_status = "Invalid JSON response"
                    logger.error("Failed to parse JSON response despite 200 status code")
                    logger.debug(f"Raw response data: {data}")
            elif status == 401:
                token_status = "Unauthorized"
                logger.warning("Token validation failed: Unauthorized (401)")
            elif status == 403:
                token_status = "Forbidden"
                logger.warning("Token validation failed: Forbidden (403)")
            elif status == 404:
                token_status = "Endpoint not found"
                logger.warning("Token validation failed: Endpoint not found (404)")
            elif status >= 500:
                token_status = "Server error"
                logger.warning(f"Token validation failed: Server error ({status})")
            else:
                token_status = f"HTTP error {status}"
                logger.warning(f"Token validation failed: HTTP error ({status})")

            # Log the validation result
            logger.info(f"Token validation result: {is_valid} (Status: {status}, Token status: {token_status})")

            # If not valid, try to get more detailed error message
            if not is_valid:
                try:
                    if data:
                        logger.debug(f"API Response Data: {data}")

                        # Try to parse as JSON
                        try:
                            response_data = json.loads(data)

                            # Handle the expected error format: {"code": "string", "message": "string"}
                            if 'code' in response_data and 'message' in response_data:
                                error_code = response_data.get('code')
                                error_message = response_data.get('message')
                                # Update token status with more specific information
                                token_status = f"Error: {error_message} (Code: {error_code})"
                                logger.error(f"Token validation failed: Code={error_code}, Message={error_message}")
                            else:
                                # Fallback to other possible error formats
                                error_message = (response_data.get('message') or
                                                response_data.get('error') or
                                                response_data.get('error_description') or
                                                f"HTTP Error: {status}")
                                # Update token status if we have more specific information
                                if error_message != f"HTTP Error: {status}":
                                    token_status = f"Error: {error_message}"
                                logger.error(f"Token validation failed: {error_message}")
                        except json.JSONDecodeError:
                            # Keep the token status we already set based on HTTP status
                            logger.error(f"Token validation failed with status: {status} (non-JSON response)")
                            logger.debug(f"Raw response data: {data}")
                    else:
                        # Keep the token status we already set based on HTTP status
                        logger.error(f"Token validation failed with status: {status} (empty response)")
                except Exception as parse_error:
                    # If we encounter an error parsing the response, update token status
                    token_status = f"Error parsing response: {str(parse_error)[:50]}"
                    logger.error(f"Error parsing validation response: {parse_error}")
                    logger.error(f"Token validation failed with status: {status}")

        except Exception as e:
            logger.error(f"Error validating token: {str(e)}")
            is_valid = False
            token_status = f"Connection error: {str(e)[:100]}"

        # Get admin ID and tenant for logging
        admin_id = session.get('admin_id', 'unknown')
        tenant = get_tenant()

        # Find the broker setup record with this token
        broker_setup = mongo_db.db["broker_setup"].find_one({"jwt_token": token})

        if is_valid:
            # Log successful validation
            logger.info(f"Broker API token validation successful for tenant: {tenant}, admin: {admin_id}")

            if broker_setup:
                # Update the validation status to valid - always use "Valid" with capital V for consistency
                mongo_db.db["broker_setup"].update_one(
                    {"_id": broker_setup["_id"]},
                    {
                        "$set": {
                            "jwt_token_valid": True,
                            "jwt_token_status": "Valid",  # Use consistent capitalization
                            "last_validated": datetime.now(timezone.utc),
                            "last_updated": datetime.now(timezone.utc)
                        }
                    }
                )

                # Log the update with the standardized status
                logger.info(f"Updated token validation status to Valid for broker setup: {broker_setup['_id']}")
                # Also log the original token_status for debugging
                logger.info(f"Original token_status was: {token_status}")

            # Log activity
            mongo_db.add_activity_log(
                action="Broker API Token Validation",
                details=f"Successfully validated broker API token for tenant: {tenant}",
                admin_id=admin_id
            )

            return jsonify({
                "success": True,
                "message": "Token is valid",
                "token_status": "Valid"  # Include the token status in the response
            })
        else:
            # Token validation failed
            logger.info(f"Broker API token validation failed for tenant: {tenant}, admin: {admin_id}")

            if broker_setup:
                # Update the validation status to invalid - always use "Invalid" with capital I for consistency
                mongo_db.db["broker_setup"].update_one(
                    {"_id": broker_setup["_id"]},
                    {
                        "$set": {
                            "jwt_token_valid": False,
                            "jwt_token_status": "Invalid",  # Use consistent capitalization
                            "last_validated": datetime.now(timezone.utc),
                            "last_updated": datetime.now(timezone.utc)
                        }
                    }
                )

                # Log the update with the standardized status
                logger.info(f"Updated token validation status to Invalid for broker setup: {broker_setup['_id']}")
                # Also log the original token_status for debugging
                logger.info(f"Original token_status was: {token_status}")

            # Log activity
            mongo_db.add_activity_log(
                action="Broker API Token Validation",
                details=f"Failed to validate broker API token for tenant: {tenant}",
                admin_id=admin_id
            )

            return jsonify({
                "success": False,
                "error": "Invalid token",
                "message": "The provided token is not valid",
                "token_status": "Invalid"  # Include the token status in the response
            }), 401

    except Exception as e:
        logger.error(f"Error validating broker API token: {e}", exc_info=True)
        return jsonify({
            "success": False,
            "error": "Server error",
            "message": str(e)
        }), 500

@broker_api_bp.route('/update-broker', methods=['POST'])
@login_required
def update_broker():
    """
    Update the active broker

    Accepts:
        - broker: The broker to set

    Returns:
        JSON response with update result
    """
    try:
        # Ensure broker_setup collection exists
        ensure_broker_setup_collection()

        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "Invalid request",
                "message": "Request body must be valid JSON"
            }), 400

        broker = data.get('broker')

        if not broker:
            return jsonify({
                "success": False,
                "error": "Invalid broker",
                "message": "Broker must be specified"
            }), 400

        # Get admin ID and tenant
        admin_id = session.get('admin_id', 'unknown')
        tenant = get_tenant()

        # Log the database and collection we're using
        logger.info(f"Using database: {mongo_db.db.name}, collection: broker_setup")
        logger.info(f"Updating broker to: {broker}")

        # Find the most recent broker setup record
        broker_setup = mongo_db.db["broker_setup"].find_one(
            {},
            sort=[("last_updated", -1)]
        )

        if broker_setup:
            try:
                # Update the broker
                result = mongo_db.db["broker_setup"].update_one(
                    {"_id": broker_setup["_id"]},
                    {
                        "$set": {
                            "active_broker": broker,
                            "last_updated": datetime.now(timezone.utc)
                        }
                    }
                )

                logger.info(f"Update result: matched={result.matched_count}, modified={result.modified_count}")
            except Exception as db_error:
                logger.error(f"Database error: {db_error}", exc_info=True)
                raise

            if result.modified_count > 0:
                # Log activity
                mongo_db.add_activity_log(
                    action="Broker Update",
                    details=f"Updated active broker to {broker} for tenant: {tenant}",
                    admin_id=admin_id
                )

                return jsonify({
                    "success": True,
                    "message": f"Broker updated to {broker}"
                })
            else:
                return jsonify({
                    "success": False,
                    "error": "Update failed",
                    "message": "No changes were made"
                }), 500
        else:
            return jsonify({
                "success": False,
                "error": "No broker setup",
                "message": "No broker setup found. Please generate a token first."
            }), 404

    except Exception as e:
        logger.error(f"Error updating broker: {e}", exc_info=True)
        return jsonify({
            "success": False,
            "error": "Server error",
            "message": str(e)
        }), 500

@broker_api_bp.route('/refresh-setup', methods=['GET'])
@login_required
def refresh_setup():
    """
    Force a refresh of the broker setup data from the database

    Returns:
        JSON response with the current broker setup data
    """
    try:
        # Ensure broker_setup collection exists
        ensure_broker_setup_collection()

        # Log tenant and database information
        tenant = get_tenant()
        logger.info(f"Refresh setup request for tenant: {tenant}")
        logger.info(f"Using database: {mongo_db.db.name}")

        # Get broker setup data from MongoDB
        broker_setup = mongo_db.db["broker_setup"].find_one(
            {},
            sort=[("last_updated", -1)]  # Get the most recently updated record
        )

        logger.info(f"Found broker setup: {broker_setup}")

        if broker_setup:
            # Check if token is expired
            expiry_date = broker_setup.get("expiry_date")
            is_expired = False

            if expiry_date:
                # Check if expiry_date is timezone-aware
                if hasattr(expiry_date, 'tzinfo') and expiry_date.tzinfo is not None:
                    # Compare with timezone-aware datetime
                    is_expired = datetime.now(timezone.utc) > expiry_date
                    logger.info(f"Comparing timezone-aware datetimes: now={datetime.now(timezone.utc)}, expiry={expiry_date}")
                else:
                    # Convert naive datetime to aware for comparison
                    # First make sure we're comparing in UTC
                    now = datetime.now()
                    logger.info(f"Comparing timezone-naive datetimes: now={now}, expiry={expiry_date}")
                    is_expired = now > expiry_date

            # Format expiry date for display
            expiry_str = ""
            if expiry_date:
                expiry_str = expiry_date.isoformat()

            # Get token validity from database
            jwt_token_valid = broker_setup.get("jwt_token_valid", False)

            # Get token status from database
            jwt_token_status = broker_setup.get("jwt_token_status", "Not validated")
            logger.info(f"Token status from database: {jwt_token_status}")

            # Calculate overall validity
            is_valid = jwt_token_valid and not is_expired

            # Get customer_name_custdb
            customer_name_custdb = broker_setup.get("customer_name_custdb", "")

            # Prepare response - don't include the actual token or credentials for security
            response_data = {
                "success": True,
                "has_token": True,
                "expiry": expiry_str,
                "is_expired": is_expired,
                "is_valid": is_valid,
                "token_status": jwt_token_status,
                "mode": broker_setup.get("mode", "auto"),
                "active_broker": broker_setup.get("active_broker", "exness"),
                "customer_name_custdb": customer_name_custdb,
                "last_updated": broker_setup.get("last_updated").isoformat() if (broker_setup.get("last_updated") and hasattr(broker_setup.get("last_updated"), "isoformat")) else None
            }

            logger.info(f"Refresh setup response: {response_data}")

            return jsonify(response_data)
        else:
            return jsonify({
                "success": True,
                "has_token": False,
                "message": "No broker setup found"
            })

    except Exception as e:
        logger.error(f"Error refreshing broker setup: {e}", exc_info=True)
        return jsonify({
            "success": False,
            "error": "Server error",
            "message": str(e)
        }), 500

@broker_api_bp.route('/update-mode', methods=['POST'])
@login_required
def update_mode():
    """
    Update the broker mode (auto or manual)

    Accepts:
        - mode: The mode to set (auto or manual)

    Returns:
        JSON response with update result
    """
    try:
        # Ensure broker_setup collection exists
        ensure_broker_setup_collection()

        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "Invalid request",
                "message": "Request body must be valid JSON"
            }), 400

        mode = data.get('mode')

        if not mode or mode not in ['auto', 'manual']:
            return jsonify({
                "success": False,
                "error": "Invalid mode",
                "message": "Mode must be either 'auto' or 'manual'"
            }), 400

        # Get admin ID and tenant
        admin_id = session.get('admin_id', 'unknown')
        tenant = get_tenant()

        # Find the most recent broker setup record
        broker_setup = mongo_db.db["broker_setup"].find_one(
            {},
            sort=[("last_updated", -1)]
        )

        if broker_setup:
            # Log the database and collection we're using
            logger.info(f"Using database: {mongo_db.db.name}, collection: broker_setup")
            logger.info(f"Updating mode to: {mode} for record: {broker_setup['_id']}")

            try:
                # Update the mode
                result = mongo_db.db["broker_setup"].update_one(
                    {"_id": broker_setup["_id"]},
                    {
                        "$set": {
                            "mode": mode,
                            "last_updated": datetime.now(timezone.utc)
                        }
                    }
                )

                logger.info(f"Update result: matched={result.matched_count}, modified={result.modified_count}")
            except Exception as db_error:
                logger.error(f"Database error: {db_error}", exc_info=True)
                raise

            if result.modified_count > 0:
                # Log activity
                mongo_db.add_activity_log(
                    action="Broker Mode Update",
                    details=f"Updated broker mode to {mode} for tenant: {tenant}",
                    admin_id=admin_id
                )

                return jsonify({
                    "success": True,
                    "message": f"Mode updated to {mode}"
                })
            else:
                return jsonify({
                    "success": False,
                    "error": "Update failed",
                    "message": "No changes were made"
                }), 500
        else:
            return jsonify({
                "success": False,
                "error": "No broker setup",
                "message": "No broker setup found. Please generate a token first."
            }), 404

    except Exception as e:
        logger.error(f"Error updating broker mode: {e}", exc_info=True)
        return jsonify({
            "success": False,
            "error": "Server error",
            "message": str(e)
        }), 500

def get_broker_credentials():
    """
    Retrieve and decrypt the stored broker credentials.

    Returns:
        tuple: (username, password) if credentials exist, (None, None) otherwise
    """
    try:
        # Ensure broker_setup collection exists
        ensure_broker_setup_collection()

        # Get broker setup data from MongoDB
        broker_setup = mongo_db.db["broker_setup"].find_one(
            {},
            sort=[("last_updated", -1)]  # Get the most recently updated record
        )

        if broker_setup and broker_setup.get("encrypted_username") and broker_setup.get("encrypted_password"):
            # Decrypt the credentials
            username = decrypt_data(broker_setup.get("encrypted_username"))
            password = decrypt_data(broker_setup.get("encrypted_password"))

            return username, password
        else:
            logger.warning("No encrypted credentials found in broker setup")
            return None, None

    except Exception as e:
        logger.error(f"Error retrieving broker credentials: {e}", exc_info=True)
        return None, None

def authenticate(username, password):
    """
    Authenticate with the Exness API using provided credentials.

    Args:
        username: The API username
        password: The API password

    Returns:
        Dictionary containing:
            success: Boolean indicating if authentication was successful.
            token: Authentication token if successful, empty string otherwise.
            message: Error message if authentication failed.
    """
    try:
        # Log the API host we're trying to connect to
        logger.info(f"Attempting to connect to API host: {API_HOST}")

        # For testing/development, we can generate a mock token if the API is not available
        # This allows development and testing to continue even if the external API is not accessible
        if os.environ.get('FLASK_ENV') == 'development' or os.environ.get('TESTING') == 'true':
            logger.warning("Running in development/testing mode. Using mock token.")
            mock_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"
            return {
                "success": True,
                "token": f"Bearer {mock_token}",
                "message": "Authentication successful (MOCK TOKEN)"
            }

        try:
            # Prepare payload
            payload = json.dumps({
                "login": username,
                "password": password
            })

            # Prepare headers with browser-like user agent to avoid WAF detection
            headers = {
                'Content-Type': 'application/json',
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'application/json, text/plain, */*',
                'Accept-Language': 'en-US,en;q=0.9',
                'Origin': f'https://{API_HOST}',
                'Referer': f'https://{API_HOST}/'
            }

            # Use our custom follow_redirects function
            status, data, final_url, redirect_history = follow_redirects(
                API_HOST,
                AUTH_ENDPOINT,
                method="POST",
                headers=headers,
                body=payload
            )

            # Log the response for debugging
            logger.info(f"API Response Status: {status}")
            logger.info(f"API Response URL (after redirects): {final_url}")

            # Check if there was a redirect
            if len(redirect_history) > 1:
                redirect_chain = []
                for r in redirect_history:
                    redirect_chain.append(f"{r['status']} {r['url']}")
                redirect_str = " -> ".join(redirect_chain)
                logger.info(f"Request was redirected: {redirect_str}")

            # Try to parse the response as JSON
            try:
                if data:
                    logger.debug(f"API Response Data: {data}")
                    response_data = json.loads(data)
                    logger.debug(f"Response structure: {list(response_data.keys())}")
                else:
                    logger.warning("Empty response data")
                    response_data = {}
            except json.JSONDecodeError as json_err:
                logger.error(f"Failed to parse JSON response: {json_err}")
                logger.debug(f"Raw response data: {data}")
                response_data = {}

            # Check response status code first
            if status == 200:
                # Exness Affiliates API might have different response format
                # Try different possible token field names
                token = (response_data.get('token') or
                         response_data.get('access_token') or
                         response_data.get('jwt') or
                         response_data.get('auth_token') or
                         response_data.get('data', {}).get('token') or
                         response_data.get('data', {}).get('access_token') or
                         '')

                if token:
                    # Check if token already has Bearer prefix
                    if not token.startswith('Bearer '):
                        token = f"Bearer {token}"

                    return {
                        "success": True,
                        "token": token,
                        "message": "Authentication successful"
                    }
                else:
                    logger.error(f"Authentication succeeded but no token found in response")
                    return {
                        "success": False,
                        "token": "",
                        "message": "Authentication succeeded but no token was returned by the API"
                    }
            else:
                # Try to extract error message from different possible fields
                try:
                    error_message = (response_data.get('message') or
                                    response_data.get('error') or
                                    response_data.get('error_description') or
                                    response_data.get('errors', {}).get('message') or
                                    f"HTTP Error: {status}")
                except:
                    error_message = f"HTTP Error: {status}"

                logger.error(f"Authentication failed: {error_message}")

                # Special handling for redirects that didn't result in success
                if len(redirect_history) > 1:
                    redirect_chain = []
                    for r in redirect_history:
                        redirect_chain.append(f"{r['status']} {r['url']}")
                    redirect_info = f" (Request was redirected: {' -> '.join(redirect_chain)})"
                    error_message += redirect_info

                return {
                    "success": False,
                    "token": "",
                    "message": f"Authentication failed: {error_message}"
                }

        except http.client.HTTPException as http_err:
            logger.error(f"HTTP error during authentication: {str(http_err)}")
            return {
                "success": False,
                "token": "",
                "message": f"HTTP error: {str(http_err)}. Please check your network connection and try again."
            }
        except socket.gaierror as dns_err:
            logger.error(f"DNS resolution error: {str(dns_err)}")
            return {
                "success": False,
                "token": "",
                "message": f"Cannot connect to broker API server. DNS resolution failed. Please check your network connection and try again."
            }
        except socket.timeout as timeout_err:
            logger.error(f"Connection timeout: {str(timeout_err)}")
            return {
                "success": False,
                "token": "",
                "message": "Connection to broker API server timed out. Please try again later."
            }

    except Exception as e:
        logger.error(f"Error during authentication: {str(e)}")
        return {
            "success": False,
            "token": "",
            "message": f"Error connecting to broker API: {str(e)}. Please check your network connection and try again."
        }
