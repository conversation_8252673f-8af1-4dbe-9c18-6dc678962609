from flask import render_template, redirect, url_for, flash, request, session, jsonify
from utils.mongo_db import MongoDB
import logging
import re
import requests
from datetime import datetime, timedelta
from bson.objectid import ObjectId

# Fix the blueprint import
from . import dashboard_bp
from .main import login_required

logger = logging.getLogger(__name__)
mongo_db = MongoDB()

@dashboard_bp.route('/telegram-bots')
@login_required
def telegram_bots():
    """Display users added by a specific Telegram bot"""
    try:
        bot = mongo_db.db["telegram_bots"].find_one({'_id': ObjectId(bot_id)})
        if not bot:
            flash('Bot not found', 'error')
            return redirect(url_for('dashboard.telegram_bots_route'))

        bot_users = []
        for user_id, user_data in bot.get('users', {}).items():
            # Get subscription details
            subscription = mongo_db.get_subscription(int(user_id))
            
            # Get channel membership status
            is_member = False
            try:
                response = requests.get(f"https://api.telegram.org/bot{bot['token']}/getChatMember", 
                                      params={"chat_id": "-1002682842201", "user_id": user_id})
                if response.json()['result']['status'] in ["member", "administrator", "creator"]:
                    is_member = True
            except Exception as e:
                logger.error(f"Error checking membership: {e}")

            bot_users.append({
                'user_id': user_id,
                'name': subscription.get('user_details', {}).get('name', 'N/A'),
                'access_code': subscription.get('access_code', 'N/A'),
                'is_member': is_member,
                'channel_link_sent': user_data.get('channel_link_sent', False),
                'interaction_time': user_data.get('interaction_time')
            })

        return render_template('telegram_bot_users.html', 
                            bot=bot,
                            bot_users=bot_users)

    except Exception as e:
        logger.error(f"Error loading bot users: {e}")
        flash('Error loading bot users', 'error')
        return redirect(url_for('dashboard.telegram_bots_route'))