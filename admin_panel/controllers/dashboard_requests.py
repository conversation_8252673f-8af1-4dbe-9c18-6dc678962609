# admin_panel/controllers/dashboard_requests.py
from flask import render_template, session, redirect, url_for, jsonify, flash, request
from utils.db_common import mongo_db
from utils.telegram_utils import create_telegram_invite_link, send_telegram_message
import logging
from datetime import datetime, timezone

logger = logging.getLogger(__name__)
# Using mongo_db from utils.db_common

def verification_requests():
    """
    Route to handle verification requests
    """
    # Check if user is logged in
    if 'admin_id' not in session:
        return redirect(url_for('auth.login'))

    # Get admin info
    admin_id = session.get('admin_id')
    admin_info = mongo_db.get_admin_by_id(admin_id)

    # Fetch verification requests from MongoDB
    verification_requests_collection = mongo_db.db.verification_request
    found_requests = list(verification_requests_collection.find().sort('created_at', -1))

    # Check if each access code exists in the system
    for request in found_requests:
        access_code = request.get('access_code')
        if access_code:
            request['access_code_exists'] = mongo_db.db["access_code"].find_one({"code": access_code}) is not None
        else:
            request['access_code_exists'] = False

    # Render the verification requests template
    return render_template(
        'verification_requests.html',
        admin=admin_info,
        requests=found_requests,
        page_title="Verification Requests"
    )

def support_requests():
    """
    Route to handle support requests
    """
    # Check if user is logged in
    if 'admin_id' not in session:
        return redirect(url_for('auth.login'))

    # Get admin info
    admin_id = session.get('admin_id')
    admin_info = mongo_db.get_admin_by_id(admin_id)

    # Fetch support requests from MongoDB
    support_requests_collection = mongo_db.db.support_request
    found_requests = list(support_requests_collection.find().sort('created_at', -1))

    # Ensure each request has the expected fields
    for request in found_requests:
        if 'user_details' not in request:
            request['user_details'] = {'name': 'N/A', 'email': 'N/A', 'whatsapp': 'N/A'}

    # Render the support requests template
    return render_template(
        'support_requests.html',
        admin=admin_info,
        requests=found_requests,
        page_title="Support Requests"
    )

def verification_request_details(request_id):
    """
    Route to handle verification request details
    """
    # Check if user is logged in
    if 'admin_id' not in session:
        return redirect(url_for('auth.login'))

    # Check if admin exists (for authentication purposes only)
    admin_id = session.get('admin_id')
    if not mongo_db.get_admin_by_id(admin_id):
        return jsonify({'error': 'Unauthorized access'}), 401

    # Fetch verification request from MongoDB
    verification_requests_collection = mongo_db.db.verification_request
    request_details = verification_requests_collection.find_one({'request_id': request_id})

    if not request_details:
        return jsonify({'error': 'Verification request not found'}), 404

    # Check if the access code exists in the system
    access_code = request_details.get('access_code')
    access_code_exists = False
    if access_code:
        access_code_exists = mongo_db.db["access_code"].find_one({"code": access_code}) is not None

    # Create a response dictionary with safe JSON serializable values
    response_data = {
        'request_id': request_details.get('request_id'),
        'user_id': request_details.get('user_id'),
        'access_code': request_details.get('access_code'),
        'access_code_exists': access_code_exists,
        'status': request_details.get('status'),
        'verified': request_details.get('verified', False),
        'user_details': {
            'name': request_details.get('user_details', {}).get('name', 'N/A'),
            'email': request_details.get('user_details', {}).get('email', 'N/A'),
            'whatsapp': request_details.get('user_details', {}).get('whatsapp', 'N/A')
        },
        'admin_notes': request_details.get('admin_notes', []),
        'verification_history': []
    }

    # Handle datetime objects safely
    created_at = request_details.get('created_at')
    if created_at:
        response_data['created_at'] = created_at.strftime('%Y-%m-%d %H:%M')
    else:
        response_data['created_at'] = 'N/A'

    updated_at = request_details.get('updated_at')
    if updated_at:
        response_data['updated_at'] = updated_at.strftime('%Y-%m-%d %H:%M')
    else:
        response_data['updated_at'] = 'N/A'

    # Handle verification history safely
    history = request_details.get('verification_history', [])
    for entry in history:
        history_data = {
            'status': entry.get('status', ''),
            'note': entry.get('note', '')
        }

        timestamp = entry.get('timestamp')
        if timestamp:
            history_data['timestamp'] = timestamp.strftime('%Y-%m-%d %H:%M')
        else:
            history_data['timestamp'] = 'N/A'

        response_data['verification_history'].append(history_data)

    # Return the request details as JSON
    return jsonify(response_data)

def support_request_details(request_id):
    """
    Route to handle support request details
    """
    # Check if user is logged in
    if 'admin_id' not in session:
        return redirect(url_for('auth.login'))

    # Check if admin exists (for authentication purposes only)
    admin_id = session.get('admin_id')
    if not mongo_db.get_admin_by_id(admin_id):
        return jsonify({'error': 'Unauthorized access'}), 401

    # Fetch support request from MongoDB
    support_requests_collection = mongo_db.db.support_request
    request_details = support_requests_collection.find_one({'request_id': request_id})

    if not request_details:
        return jsonify({'error': 'Support request not found'}), 404

    # Create a response dictionary with safe JSON serializable values
    response_data = {
        'request_id': request_details.get('request_id'),
        'user_id': request_details.get('user_id'),
        'user_name': request_details.get('user_details', {}).get('name'),
        'user_email': request_details.get('user_details', {}).get('email'),
        'user_whatsapp': request_details.get('user_details', {}).get('whatsapp'),
        'status': request_details.get('status'),
        'is_resolved': request_details.get('is_resolved', False),
        'messages': []
    }

    # Handle datetime objects safely
    created_at = request_details.get('created_at')
    if created_at:
        response_data['created_at'] = created_at.strftime('%Y-%m-%d %H:%M')
    else:
        response_data['created_at'] = 'N/A'

    updated_at = request_details.get('updated_at')
    if updated_at:
        response_data['updated_at'] = updated_at.strftime('%Y-%m-%d %H:%M')
    else:
        response_data['updated_at'] = 'N/A'

    # Handle messages safely
    messages = request_details.get('messages', [])
    for msg in messages:
        message_data = {
            'text': msg.get('text', ''),
            'type': msg.get('type', 'unknown')
        }

        timestamp = msg.get('timestamp')
        if timestamp:
            message_data['timestamp'] = timestamp.strftime('%Y-%m-%d %H:%M')
        else:
            message_data['timestamp'] = 'N/A'

        response_data['messages'].append(message_data)

    # Return the request details as JSON
    return jsonify(response_data)

def approve_verification_request(request_id):
    """
    Route to approve a verification request
    """
    # Check if user is logged in
    if 'admin_id' not in session:
        return redirect(url_for('auth.login'))

    # Get admin info
    admin_id = session.get('admin_id')
    admin = mongo_db.get_admin_by_id(admin_id)
    if not admin:
        flash('Unauthorized access', 'error')
        return redirect(url_for('auth.login'))

    # Fetch verification request from MongoDB
    verification_requests_collection = mongo_db.db.verification_request
    request_details = verification_requests_collection.find_one({'request_id': request_id})

    if not request_details:
        flash('Verification request not found', 'error')
        return redirect(url_for('dashboard.verification_requests_route'))

    # Check if the access code exists in the access_code collection
    access_code = request_details.get('access_code')
    if access_code:
        access_code_exists = mongo_db.db["access_code"].find_one({"code": access_code})
        if not access_code_exists:
            # Return a JSON error response instead of redirecting
            error_message = f'Access code "{access_code}" not available. Please add the access code to the system before approving.'
            logger.warning(f"Approval failed: {error_message}")
            return jsonify({
                'success': False,
                'error': error_message
            }), 400

    # Update the request status to approved
    result = verification_requests_collection.update_one(
        {'request_id': request_id},
        {'$set': {
            'status': 'approved',
            'verified': True,
            'updated_at': datetime.now(timezone.utc)
        },
        '$push': {
            'verification_history': {
                'status': 'approved',
                'timestamp': datetime.now(timezone.utc),
                'note': f'Approved by admin {admin.get("username", "Unknown")}',
                'admin_id': admin_id
            }
        }}
    )

    if result.modified_count > 0:
        # Update master_user_data collection
        user_id = request_details.get('user_id')
        if user_id:
            # Update the user's verification status and subscription status in master_user_data
            # Also add subscribed_on field with current time when subscription is set to active
            current_time = datetime.now(timezone.utc)
            master_user_data_result = mongo_db.master_user_data.update_one(
                {'user_id': user_id},
                {'$set': {
                    'user_verify': True,
                    "user_verify_status" : 'approved',
                    'subscription': 'active',
                    'broker_reg': True,
                    'user_status': 'active',  # Set the new user_status field to 'active'
                    'subscribed_on': current_time,  # Add subscribed_on field with current time
                    'last_updated': current_time
                }}
            )

            if master_user_data_result.modified_count > 0:
                logger.info(f"Updated master_user_data for user {user_id} - verification approved")
            else:
                logger.warning(f"Failed to update master_user_data for user {user_id}")

            # Send Telegram notification with invite link
            try:
                # Create a one-time use invite link
                invite_link, error = create_telegram_invite_link()

                # Create the new message format
                message = f"<b>Congratulations! Your profile is verified ✅</b>\n\n" \
                         f"You are an exclusive Member With us 🥷"

                # Create buttons for the message
                if invite_link:
                    # Create all the buttons
                    buttons = [
                        {
                            'text': 'Access Premium space 🏆',
                            'url': invite_link
                        },
                        {
                            'text': 'Explore my Benefits 🌍',
                            'callback_data': 'verify_explore_offerings'
                        },
                        {
                            'text': 'My Community Ranking 🏅',
                            'callback_data': 'community_ranking'
                        },
                        {
                            'text': 'Contact Withdrawals/Deposit Team 🔰',
                            'url': 'http://wa.me/+971585727623'
                        },
                        {
                            'text': 'Need More Support?',
                            'callback_data': 'submit_request_start'
                        }
                    ]

                    # Send message with multiple buttons
                    send_telegram_message(user_id, message, inline_buttons=buttons)
                else:
                    # If no invite link, still send the message without the first button
                    logger.error(f"Failed to create invite link: {error}")
                    buttons = [
                        {
                            'text': 'invite link cretion failed, please contact admin using below button',
                            'callback_data': 'submit_request_start'
                        }
                    ]
                    send_telegram_message(user_id, message, inline_buttons=buttons)

                # Log the activity
                mongo_db.add_activity_log(
                    action="Send Telegram Invite",
                    details=f"System automatically sent Telegram invite link to user ID {user_id} after verification approval",
                    admin_id=admin_id
                )
            except Exception as e:
                logger.error(f"Failed to send Telegram notification with invite link: {e}")

        flash('Verification request approved successfully', 'success')
    else:
        flash('Failed to update verification request', 'error')

    return redirect(url_for('dashboard.verification_requests_route'))

def deny_verification_request(request_id):
    """
    Route to deny a verification request
    """
    # Check if user is logged in
    if 'admin_id' not in session:
        return redirect(url_for('auth.login'))

    # Get admin info
    admin_id = session.get('admin_id')
    admin = mongo_db.get_admin_by_id(admin_id)
    if not admin:
        flash('Unauthorized access', 'error')
        return redirect(url_for('auth.login'))

    # Fetch verification request from MongoDB
    verification_requests_collection = mongo_db.db.verification_request
    request_details = verification_requests_collection.find_one({'request_id': request_id})

    if not request_details:
        flash('Verification request not found', 'error')
        return redirect(url_for('dashboard.verification_requests_route'))

    # Get the access code from the request details
    access_code = request_details.get('access_code', 'Unknown')

    # Update the request status to denied
    result = verification_requests_collection.update_one(
        {'request_id': request_id},
        {'$set': {
            'status': 'denied',
            'verified': True,  # This indicates the request has been processed, not that it was approved
            'updated_at': datetime.now(timezone.utc)
        },
        '$push': {
            'verification_history': {
                'status': 'denied',
                'timestamp': datetime.now(timezone.utc),
                'note': f'Denied by admin {admin.get("username", "Unknown")}',
                'admin_id': admin_id
            }
        }}
    )

    if result.modified_count > 0:
        # Update master_user_data collection
        user_id = request_details.get('user_id')
        if user_id:
            # Update the user's verification status in master_user_data
            # When a verification request is denied, user_verify should be false
            master_user_data_result = mongo_db.master_user_data.update_one(
                {'user_id': user_id},
                {'$set': {
                    'user_verify': False,  # Set to false when denied
                    'user_verify_status': 'denied',  # Add the new user_verify_status field
                    'subscription': 'inactive',  # Keep subscription inactive
                    'user_status': 'verification_denied',  # Set the user_status field to 'Verification denied'
                    'last_updated': datetime.now(timezone.utc)
                }}
            )

            if master_user_data_result.modified_count > 0:
                logger.info(f"Updated master_user_data for user {user_id} - verification denied")
            else:
                logger.warning(f"Failed to update master_user_data for user {user_id}")

            # Send Telegram notification with a more detailed message
            try:
                message = f"❌ <b>Verification Request Denied</b>\n\n" \
                         f"Your verification request for access code <b>{access_code}</b> has been denied.\n\n" \
                         f"If you believe this is an error or need assistance, please contact our support team.\n\n" \
                         f"<i>Thank you for your understanding.</i>"

                # Create button for reverification
                buttons = [
                    {
                        'text': 'Try Verification Again',
                        'callback_data': 'reg_start_verification'
                    }
                ]

                # Send message with reverification button
                send_telegram_message(user_id, message, inline_buttons=buttons)
                logger.info(f"Sent denial notification to user {user_id}")
            except Exception as e:
                logger.error(f"Failed to send Telegram notification: {e}")

        flash('Verification request denied successfully', 'info')
    else:
        flash('Failed to update verification request', 'error')

    return redirect(url_for('dashboard.verification_requests_route'))

def add_verification_request_note(request_id):
    """
    Route to add an admin note to a verification request
    """
    # Check if user is logged in
    if 'admin_id' not in session:
        return jsonify({'error': 'Authentication required'}), 401

    # Get admin info
    admin_id = session.get('admin_id')
    admin = mongo_db.get_admin_by_id(admin_id)
    if not admin:
        return jsonify({'error': 'Unauthorized access'}), 401

    # Get note from request body
    data = request.get_json()
    if not data or 'note' not in data or not data['note'].strip():
        return jsonify({'error': 'Note text is required'}), 400

    note_text = data['note'].strip()

    # Fetch verification request from MongoDB
    verification_requests_collection = mongo_db.db.verification_request
    request_details = verification_requests_collection.find_one({'request_id': request_id})

    if not request_details:
        return jsonify({'error': 'Verification request not found'}), 404

    # Create note object
    note = {
        'text': note_text,
        'admin_id': admin_id,
        'admin_name': admin.get('username', 'Admin'),
        'timestamp': datetime.now(timezone.utc)
    }

    # Add note to verification request
    result = verification_requests_collection.update_one(
        {'request_id': request_id},
        {'$push': {'admin_notes': note},
         '$set': {'updated_at': datetime.now(timezone.utc)}}
    )

    if result.modified_count > 0:
        return jsonify({
            'success': True,
            'message': 'Note added successfully',
            'admin_name': admin.get('username', 'Admin')
        })
    else:
        return jsonify({
            'success': False,
            'message': 'Failed to add note'
        }), 500

def send_support_notification(request_id=None):
    """
    Route to send a notification to a user about their support request
    """
    # Check if user is logged in
    if 'admin_id' not in session:
        return jsonify({'error': 'Authentication required'}), 401

    # Get admin info
    admin_id = session.get('admin_id')
    admin = mongo_db.get_admin_by_id(admin_id)
    if not admin:
        return jsonify({'error': 'Unauthorized access'}), 401

    # Get data from request body
    data = request.get_json()
    if not data:
        return jsonify({'error': 'No data provided'}), 400

    # Extract required fields
    user_id = data.get('user_id')
    request_id = data.get('request_id') or request_id
    status = data.get('status')
    message = data.get('message')
    query_summary = data.get('query_summary', 'your support request')

    # Validate required fields
    if not user_id or not request_id or not status or not message:
        return jsonify({'error': 'Missing required fields'}), 400

    try:
        # Format the notification message
        notification = f"<b>Support Request #: {request_id}</b>\n" \
                       f"<b>Your Query:</b> {query_summary}\n" \
                       f"<b>Status:</b> {status}\n" \
                       f"-----------------------------------------\n\n" \
                       f"{message}"

        # Send the notification via Telegram
        success = send_telegram_message(user_id, notification)

        if success:
            # Update the support request with the notification
            support_requests_collection = mongo_db.db.support_request

            # Add the notification to the messages array
            notification_entry = {
                'text': message,
                'type': 'admin',
                'timestamp': datetime.now(timezone.utc),
                'admin_id': admin_id,
                'admin_name': admin.get('username', 'Admin'),
                'status': status
            }

            # Update the request status if it's not already resolved
            update_data = {
                '$push': {'messages': notification_entry},
                '$set': {'updated_at': datetime.now(timezone.utc)}
            }

            # If status is 'Resolved', mark the request as resolved
            if status == 'Resolved':
                update_data['$set']['status'] = 'resolved'
                update_data['$set']['is_resolved'] = True
                update_data['$set']['resolved_by'] = admin_id
                update_data['$set']['resolved_at'] = datetime.now(timezone.utc)
            elif status == 'In Progress':
                update_data['$set']['status'] = 'in_progress'

            # Update the request
            result = support_requests_collection.update_one(
                {'request_id': request_id},
                update_data
            )

            if result.modified_count > 0:
                return jsonify({
                    'success': True,
                    'message': 'Notification sent successfully'
                })
            else:
                return jsonify({
                    'success': False,
                    'message': 'Failed to update support request'
                }), 500
        else:
            return jsonify({
                'success': False,
                'message': 'Failed to send notification'
            }), 500
    except Exception as e:
        logger.error(f"Error sending support notification: {e}")
        return jsonify({
            'success': False,
            'message': f'Error: {str(e)}'
        }), 500

def resolve_support_request(request_id):
    """
    Route to mark a support request as resolved
    """
    # Check if user is logged in
    if 'admin_id' not in session:
        return redirect(url_for('auth.login'))

    # Check if admin exists (for authentication purposes only)
    admin_id = session.get('admin_id')
    if not mongo_db.get_admin_by_id(admin_id):
        return jsonify({'error': 'Unauthorized access'}), 401

    # Fetch support request from MongoDB
    support_requests_collection = mongo_db.db.support_request
    request_details = support_requests_collection.find_one({'request_id': request_id})

    if not request_details:
        return jsonify({'error': 'Support request not found'}), 404

    # Update the request status to resolved
    result = support_requests_collection.update_one(
        {'request_id': request_id},
        {'$set': {
            'status': 'resolved',
            'is_resolved': True,
            'updated_at': datetime.now(timezone.utc),
            'resolved_by': admin_id,
            'resolved_at': datetime.now(timezone.utc)
        }}
    )

    if result.modified_count > 0:
        return jsonify({
            'success': True,
            'message': 'Support request marked as resolved successfully'
        })
    else:
        return jsonify({
            'success': False,
            'message': 'Failed to update support request status'
        }), 500
