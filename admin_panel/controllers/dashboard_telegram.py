# admin_panel/controllers/dashboard.py
import logging
from flask import Blueprint, session, redirect, url_for, flash, request, jsonify, render_template
from utils.url_helpers import tenant_url_for
from utils.db_common import mongo_db
import re
from datetime import datetime, timezone
from bson.objectid import ObjectId

# Import the decorator - Change from relative to absolute import
from utils.decorators import login_required
from utils.telegram_utils import get_telegram_bot_info, check_bot_channel_status

# Import handler functions from the new files
from .dashboard_main import home, admin_profile, change_password, search, users, export_users_csv
from .dashboard_access_codes import (
    access_codes,
    add_access_code,
    upload_access_codes,
    edit_access_code,
    delete_access_code, # The single-code delete endpoint
    access_code_details,
    check_if_code_in_use, # Renamed function
    bulk_delete_access_codes
)
from .dashboard_users import (
    user_detail,
    send_telegram_invite,
    kick_user,
    unban_user
)
from .dashboard_requests import (
    verification_requests,
    support_requests,
    verification_request_details,
    support_request_details,
    resolve_support_request,
    approve_verification_request,
    deny_verification_request,
    add_verification_request_note,
    send_support_notification
)

# Setup logger and blueprint - THIS IS THE PUBLIC INTERFACE
logger = logging.getLogger(__name__)
dashboard_bp = Blueprint('dashboard', __name__, url_prefix='/dashboard')
# Using mongo_db from utils.db_common

# Define telegram bot functions here
def telegram_bots():
    """Display all Telegram bots"""
    try:
        bots = list(mongo_db.db["telegram_bots"].find().sort("added_at", -1))
        return render_template('telegram_bots.html', bots=bots)
    except Exception as e:
        logger.error(f"Error fetching Telegram bots: {e}")
        flash(f'Error: {str(e)}', 'error')
        return redirect(tenant_url_for('dashboard.home_route'))

def broker_setup():
    """Display broker setup page"""
    try:
        return render_template('broker_setup.html')
    except Exception as e:
        logger.error(f"Error displaying broker setup page: {e}")
        flash(f'Error: {str(e)}', 'error')
        return redirect(tenant_url_for('dashboard.home_route'))

def add_telegram_bot():
    """Add a new Telegram bot"""
    try:
        # Get form data
        name = request.form.get('name')
        username = request.form.get('username')
        token = request.form.get('token')
        channel_id = request.form.get('channel_id')

        # Validate inputs
        if not name or not username or not token or not channel_id:
            flash('All fields are required', 'error')
            return redirect(tenant_url_for('dashboard.telegram_bots_route'))

        # Validate token format
        if not re.match(r'^\d+:[A-Za-z0-9_-]+$', token):
            flash('Invalid token format', 'error')
            return redirect(tenant_url_for('dashboard.telegram_bots_route'))

        # Get bot info from Telegram API to verify token
        bot_info = get_telegram_bot_info(token)
        if not bot_info:
            flash('Could not verify bot token with Telegram API', 'error')
            return redirect(tenant_url_for('dashboard.telegram_bots_route'))

        # Extract bot details from API response
        telegram_id = bot_info.get('id')
        telegram_username = bot_info.get('username')
        telegram_first_name = bot_info.get('first_name')

        # Add bot to database
        mongo_db.db["telegram_bots"].insert_one({
            'name': name,
            'username': username,
            'token': token,
            'channel_id': channel_id,
            'telegram_id': telegram_id,
            'telegram_username': telegram_username,
            'telegram_first_name': telegram_first_name,
            'is_active': True,
            'added_at': datetime.now(timezone.utc),
            'updated_at': datetime.now(timezone.utc)
        })

        flash('Bot added successfully', 'success')

    except Exception as e:
        logger.error(f"Error adding Telegram bot: {e}")
        flash(f'Error adding Telegram bot: {str(e)}', 'error')

    return redirect(tenant_url_for('dashboard.telegram_bots_route'))

def edit_telegram_bot(bot_id):
    """
    Edit an existing Telegram bot
    """
    try:
        # Get form data
        name = request.form.get('name')
        username = request.form.get('username')
        token = request.form.get('token')
        channel_id = request.form.get('channel_id')
        is_active = True if request.form.get('is_active') else False

        # Validate inputs
        if not name or not username or not token or not channel_id:
            flash('All fields are required', 'error')
            return redirect(tenant_url_for('dashboard.telegram_bots_route'))

        # Validate token format
        if not re.match(r'^\d+:[A-Za-z0-9_-]+$', token):
            flash('Invalid token format', 'error')
            return redirect(tenant_url_for('dashboard.telegram_bots_route'))

        # Get bot info from Telegram API to verify token
        try:
            bot_info = get_telegram_bot_info(token)
            if not bot_info:
                flash('Could not verify bot token with Telegram API', 'error')
                return redirect(tenant_url_for('dashboard.telegram_bots_route'))

            # Extract bot details from API response
            telegram_id = bot_info.get('id')
            telegram_username = bot_info.get('username')
            telegram_first_name = bot_info.get('first_name')

            # Update bot in database
            result = mongo_db.db["telegram_bots"].update_one(
                {'_id': ObjectId(bot_id)},
                {
                    '$set': {
                        'name': name,
                        'username': username,
                        'token': token,
                        'channel_id': channel_id,
                        'telegram_id': telegram_id,
                        'telegram_username': telegram_username,
                        'telegram_first_name': telegram_first_name,
                        'is_active': is_active,
                        'updated_at': datetime.now(timezone.utc)
                    }
                }
            )

            if result.matched_count > 0:
                flash('Bot updated successfully', 'success')
            else:
                flash('No changes were made', 'info')

        except Exception as e:
            logger.error(f"Error verifying bot token: {str(e)}")
            flash(f'Error verifying bot token: {str(e)}', 'error')
            return redirect(tenant_url_for('dashboard.telegram_bots_route'))

    except Exception as e:
        logger.error(f"Error updating Telegram bot: {str(e)}")
        flash(f'Error updating bot: {str(e)}', 'error')

    return redirect(tenant_url_for('dashboard.telegram_bots_route'))

def delete_telegram_bot(bot_id):
    """
    Delete a Telegram bot
    """
    try:
        result = mongo_db.db["telegram_bots"].delete_one({'_id': ObjectId(bot_id)})

        if result.deleted_count > 0:
            return jsonify({'success': True})
        else:
            return jsonify({'success': False, 'message': 'Bot not found'})

    except Exception as e:
        logger.error(f"Error deleting Telegram bot: {str(e)}")
        return jsonify({'success': False, 'message': str(e)})

def test_telegram_bot(bot_id):
    """
    Test a Telegram bot connection and check if it's a member and admin of the channel
    """
    try:
        bot = mongo_db.db["telegram_bots"].find_one({'_id': ObjectId(bot_id)})

        if not bot:
            return jsonify({'success': False, 'message': 'Bot not found'})

        token = bot.get('token')
        channel_id = bot.get('channel_id')
        bot_info = get_telegram_bot_info(token)

        if not bot_info:
            return jsonify({'success': False, 'message': 'Could not connect to Telegram API'})

        # Check if the bot is a member and admin of the channel
        channel_status = check_bot_channel_status(token, channel_id)

        # Add channel status, channel ID, and channel info to the response
        response = {
            'success': True,
            'bot_info': bot_info,
            'channel_id': channel_id,  # Include the channel ID in the response
            'channel_status': {
                'is_member': channel_status['is_member'],
                'is_admin': channel_status['is_admin'],
                'status': channel_status['status']
            }
        }

        # Add channel info if available
        if channel_status.get('channel_info'):
            response['channel_info'] = channel_status['channel_info']

        # Add error message if there was an error checking channel status
        if channel_status['error']:
            response['channel_status']['error'] = channel_status['error']

        return jsonify(response)

    except Exception as e:
        logger.error(f"Error testing Telegram bot: {str(e)}")
        return jsonify({'success': False, 'message': str(e)})

def telegram_bot_users(bot_telegram_username):
    """
    Display users added by a specific Telegram bot
    Uses master_user_data collection to match users with telegram bots
    """
    try:
        logger.info(f"Fetching users for Telegram bot: {bot_telegram_username}")

        # Get the bot details
        bot = mongo_db.db["telegram_bots"].find_one({'telegram_username': bot_telegram_username})

        if not bot:
            flash('Bot not found', 'error')
            return redirect(tenant_url_for('dashboard.telegram_bots_route'))

        # Get the bot ID
        bot_id = bot.get('telegram_id')
        logger.info(f"Bot ID: {bot_id}")

        if not bot_id:
            flash('Bot ID not found', 'error')
            return redirect(tenant_url_for('dashboard.telegram_bots_route'))

        # Find users from master_user_data collection that match this bot's ID
        users = list(mongo_db.db["master_user_data"].find({'telegram_bot_id': bot_id}))

        # Log the number of users found
        logger.info(f"Found {len(users)} users for bot {bot_telegram_username} (ID: {bot_id})")

        # Extract user information
        bot_users = []
        for user in users:
            # Create user info object
            user_info = {
                'user_id': user.get('user_id'),
                'name': user.get('name', 'Unknown'),
                'access_code': user.get('access_code', 'N/A'),
                'status': user.get('user_status', 'N/A'),
                'subscription_time': user.get('registration_time')
            }

            # Add to the list
            bot_users.append(user_info)

        # We no longer use the 'users' field in the telegram_bots collection
        # All user information is now stored in the master_user_data collection

        # Sort users by user_id
        try:
            # Sort users by user_id for consistent ordering
            bot_users.sort(key=lambda x: x.get('user_id', 0) if isinstance(x.get('user_id'), int) else 0)
            logger.info(f"Successfully sorted {len(bot_users)} users by user_id")
        except Exception as sort_error:
            logger.error(f"Error sorting users: {sort_error}")
            # Continue without sorting if there's an error

        logger.info(f"Returning {len(bot_users)} users for bot {bot_telegram_username}")

        return render_template(
            'telegram_bot_users.html',
            bot=bot,
            bot_users=bot_users
        )

    except Exception as e:
        logger.error(f"Error displaying bot users: {e}")
        flash(f'Error: {str(e)}', 'error')
        return redirect(tenant_url_for('dashboard.telegram_bots_route'))

# Using get_telegram_bot_info from utils/telegram_utils.py

# --- Route Definitions ---
# These map URLs to the imported handler functions.
# The external interface (dashboard_bp) remains the same.

# --- Main Dashboard & Profile ---
@dashboard_bp.route('/')
@login_required # Apply decorator (original had manual check)
def home_route():
    # The login_required decorator will handle the redirect if not logged in
    return home() # Call imported handler

@dashboard_bp.route('/profile', methods=['GET'])
@login_required # Apply decorator
def admin_profile_route():
    return admin_profile()

@dashboard_bp.route('/profile/change-password', methods=['POST'])
@login_required # Apply decorator
def change_password_route():
    return change_password()

@dashboard_bp.route('/search')
@login_required # Apply decorator
def search_route():
    return search()

# --- Access Code Management ---
@dashboard_bp.route('/access-codes')
@login_required # Apply decorator
def access_codes_route():
    return access_codes()

@dashboard_bp.route('/add-access-code', methods=['POST'])
@login_required # Apply decorator (original had manual check)
def add_access_code_route():
    return add_access_code()

@dashboard_bp.route('/upload-access-codes', methods=['POST'])
@login_required # Apply decorator (original had manual check)
def upload_access_codes_route():
    return upload_access_codes()

@dashboard_bp.route('/edit-access-code', methods=['POST'])
@login_required # Apply decorator (original had manual check)
def edit_access_code_route():
    return edit_access_code()

# Route for the single-code delete action (POST with JSON body)
@dashboard_bp.route('/delete-access-code', methods=['POST'])
@login_required # Apply decorator
def delete_access_code_route():
    return delete_access_code()

# Route for legacy multi-code delete action - now redirects to the new bulk delete endpoint
@dashboard_bp.route('/delete-access-codes', methods=['POST'])
@login_required
def delete_access_codes_route():
    # Redirect legacy calls to the new bulk delete endpoint
    logger.warning(f"Legacy endpoint '/delete-access-codes' called by Admin ID {session.get('admin_id')}. Redirecting to bulk-delete-access-codes.")
    return bulk_delete_access_codes()

@dashboard_bp.route('/bulk-delete-access-codes', methods=['POST'])
@login_required # Apply decorator
def bulk_delete_access_codes_route():
    return bulk_delete_access_codes()

@dashboard_bp.route('/access-code-details/<code>', methods=['GET'])
@login_required # Apply decorator
def access_code_details_route(code):
    return access_code_details(code)

# Corrected route for checking code usage (was '/dashboard/check...' and '/check...')
# Using the shorter version based on the final code structure provided
@dashboard_bp.route('/check-code-in-use/<code>', methods=['GET'])
@login_required # Make sure this requires login if it's admin-only
def check_if_code_in_use_route(code):
    return check_if_code_in_use(code) # Call the renamed handler


# --- User Management ---
@dashboard_bp.route('/user/<int:user_id>')
@login_required # Apply decorator
def user_detail_route(user_id):
    return user_detail(user_id)

@dashboard_bp.route('/user/<user_id>/send-invite', methods=['POST'])
@login_required # Apply decorator (original had manual check)
def send_telegram_invite_route(user_id):
     # Basic validation maybe? Handler does int conversion.
    return send_telegram_invite(user_id)

@dashboard_bp.route('/user/<int:user_id>/kick', methods=['POST'])
@login_required # Apply decorator
def kick_user_route(user_id):
    return kick_user(user_id)

@dashboard_bp.route('/user/<int:user_id>/unban', methods=['POST'])
@login_required # Apply decorator
def unban_user_route(user_id):
    return unban_user(user_id)


# --- Telegram Bot Management ---
@dashboard_bp.route('/telegram-bots')
@login_required # Apply decorator
def telegram_bots_route():
    return telegram_bots()

@dashboard_bp.route('/broker-setup')
@login_required # Apply decorator
def broker_setup_route():
    return broker_setup()

@dashboard_bp.route('/add-telegram-bot', methods=['POST'])
@login_required # Apply decorator
def add_telegram_bot_route():
    return add_telegram_bot()

# Need ObjectId for these routes if IDs are passed directly in URL
# Not needed here as the handler takes the string and converts it
@dashboard_bp.route('/edit-telegram-bot/<bot_id>', methods=['POST'])
@login_required # Apply decorator
def edit_telegram_bot_route(bot_id):
    return edit_telegram_bot(bot_id)

@dashboard_bp.route('/delete-telegram-bot/<bot_id>', methods=['POST'])
@login_required # Apply decorator
def delete_telegram_bot_route(bot_id):
    return delete_telegram_bot(bot_id)

@dashboard_bp.route('/test-telegram-bot/<bot_id>', methods=['POST'])
@login_required # Apply decorator
def test_telegram_bot_route(bot_id):
    return test_telegram_bot(bot_id)

@dashboard_bp.route('/telegram-bot-users/<bot_telegram_username>')
@login_required # Apply decorator
def telegram_bot_users_route(bot_telegram_username):
    return telegram_bot_users(bot_telegram_username)

@dashboard_bp.route('/telegram-bot/<bot_name>')
@login_required # Apply decorator
def telegram_bot_conf_route(bot_name):
    return render_template(
            'telegram_bot_conf.html',
        )

# Users routes
@dashboard_bp.route('/users')
@login_required
def users_route():
    return users()

@dashboard_bp.route('/export-users-csv')
@login_required
def export_users_csv_route():
    return export_users_csv()

# Requests routes
@dashboard_bp.route('/verification-requests')
@login_required
def verification_requests_route():
    return verification_requests()

@dashboard_bp.route('/support-requests')
@login_required
def support_requests_route():
    return support_requests()

@dashboard_bp.route('/verification-request-details/<request_id>', methods=['GET'])
@login_required
def verification_request_details_route(request_id):
    return verification_request_details(request_id)

@dashboard_bp.route('/support-request-details/<request_id>', methods=['GET'])
@login_required
def support_request_details_route(request_id):
    return support_request_details(request_id)

@dashboard_bp.route('/resolve-support-request/<request_id>', methods=['POST'])
@login_required
def resolve_support_request_route(request_id):
    return resolve_support_request(request_id)

@dashboard_bp.route('/verification-requests/approve/<request_id>', methods=['POST'])
@login_required
def approve_verification_request_route(request_id):
    return approve_verification_request(request_id)

@dashboard_bp.route('/verification-requests/deny/<request_id>', methods=['POST'])
@login_required
def deny_verification_request_route(request_id):
    return deny_verification_request(request_id)

@dashboard_bp.route('/verification-request/add-note/<request_id>', methods=['POST'])
@login_required
def add_verification_request_note_route(request_id):
    return add_verification_request_note(request_id)

@dashboard_bp.route('/send-support-notification', methods=['POST'])
@login_required
def send_support_notification_route():
    return send_support_notification()

# --- End of Route Definitions ---

logger.info("Dashboard blueprint routes registered.")