# admin_panel/controllers/dashboard_access_codes.py

from flask import render_template, redirect, url_for, flash, request, session, jsonify
from utils.url_helpers import tenant_url_for
from utils.db_common import mongo_db
import logging
import math
from datetime import datetime, timedelta, timezone
import csv
from io import StringIO
# Make sure you have pymongo installed and bson available
try:
    from bson.objectid import ObjectId
except ImportError:
    # Handle case where bson might not be directly available or pymongo not installed
    logging.error("bson.objectid not found. Is pymongo installed?")
    ObjectId = None # Or raise an error

# Import UpdateOne for bulk operations
try:
    from pymongo import UpdateOne
except ImportError:
    logging.error("pymongo.UpdateOne not found. Is pymongo installed and updated?")
    UpdateOne = None # Handle absence

logger = logging.getLogger(__name__)
# Using mongo_db from utils.db_common

# --- Display Access Codes Page (with Pagination and Filtering/Sorting) ---
def access_codes():
    """Display access codes with pagination, filtering, and sorting."""
    admin_id = session.get('admin_id')
    if not admin_id:
        flash('Authentication error. Please log in again.', 'error')
        return redirect(tenant_url_for('auth.login'))

    try:
        # Pagination parameters
        allowed_per_page = [10, 25, 50, 100, 250, 500]
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 25, type=int)
        if per_page not in allowed_per_page:
            per_page = 25 # Default to 25 if invalid value passed

        # Sorting parameters
        sort_field = request.args.get('sort', 'status') # Default sort by status
        sort_order_str = request.args.get('order', 'asc') # Default order ascending
        sort_order = -1 if sort_order_str == 'desc' else 1

        # Define valid sort fields and their MongoDB equivalents
        valid_sort_fields = {
            'code': 'code',
            'added_at': 'added_at',
            'expiration_date': 'expiration_date'
            # 'status', 'user_id', and 'subscribed_on' are handled specially below
        }

        # Use the MongoDB field name if it's a valid sort field, otherwise default to 'added_at'
        mongo_sort_field = valid_sort_fields.get(sort_field, 'added_at')

        # Filtering parameters
        filter_status = request.args.get('status', 'all') # 'all', 'available', 'used', 'active', 'inactive'
        filter_query = request.args.get('q', '').strip() # Search query for code
        filter_user_id = request.args.get('user_id', '').strip() # Filter by user ID
        filter_subscribed_from = request.args.get('subscribed_from', '') # Subscription date range start
        filter_subscribed_to = request.args.get('subscribed_to', '') # Subscription date range end
        filter_expired_from = request.args.get('expired_from', '') # Expiration date range start
        filter_expired_to = request.args.get('expired_to', '') # Expiration date range end

        # Flag to show advanced filters section
        show_advanced_filters = any([filter_user_id, filter_subscribed_from, filter_subscribed_to,
                                    filter_expired_from, filter_expired_to, filter_status != 'all'])

        # --- Build the MongoDB query ---
        query = {}
        if filter_query:
            # Case-insensitive regex search on the code itself
            query['code'] = {'$regex': filter_query, '$options': 'i'}

        # Handle expiration date range filters
        expiration_conditions = {}
        if filter_expired_from:
            try:
                from_date = datetime.strptime(filter_expired_from, '%Y-%m-%d').replace(tzinfo=timezone.utc)
                expiration_conditions['$gte'] = from_date
            except ValueError:
                logger.warning(f"Invalid expired_from date format: {filter_expired_from}")

        if filter_expired_to:
            try:
                to_date = datetime.strptime(filter_expired_to, '%Y-%m-%d').replace(tzinfo=timezone.utc)
                # Add 1 day to include the end date fully
                to_date = to_date + timedelta(days=1)
                expiration_conditions['$lt'] = to_date
            except ValueError:
                logger.warning(f"Invalid expired_to date format: {filter_expired_to}")

        if expiration_conditions:
            query['expiration_date'] = expiration_conditions

        # Get all potentially matching code documents first (pre-status filtering)
        # This count is needed for pagination *before* we filter by computed status
        total_codes_matching_query = mongo_db.db["access_code"].count_documents(query)
        total_pages = math.ceil(total_codes_matching_query / per_page)
        skip = (page - 1) * per_page

        # Check if we need to apply user-related filters
        need_user_data = filter_user_id or filter_subscribed_from or filter_subscribed_to or sort_field in ['status', 'user_id', 'subscribed_on']

        # Special handling for sorting by fields that require joins or calculations or when user filters are applied
        if need_user_data or sort_field == 'expiration_date':
            # For these fields, we need to fetch all codes and sort them in Python
            # First, get all codes matching the query
            all_codes = list(mongo_db.db["access_code"].find(query))

            # Get all user data for these codes in one query
            code_values = [c.get('code') for c in all_codes if c.get('code')]
            users_data = {}

            # Build user data query with additional filters
            user_query = {"access_code": {"$in": code_values}}

            # Add user ID filter if provided
            if filter_user_id:
                try:
                    # Try to convert to number for exact match
                    user_id_num = int(filter_user_id)
                    user_query["user_id"] = user_id_num
                except ValueError:
                    # If not a valid number, use string comparison
                    # This will convert NumberLong to string for comparison
                    user_query["user_id"] = {"$regex": filter_user_id, "$options": "i"}

            # Add subscription date range filters
            subscription_date_conditions = {}
            if filter_subscribed_from:
                try:
                    from_date = datetime.strptime(filter_subscribed_from, '%Y-%m-%d').replace(tzinfo=timezone.utc)
                    subscription_date_conditions['$gte'] = from_date
                except ValueError:
                    logger.warning(f"Invalid subscribed_from date format: {filter_subscribed_from}")

            if filter_subscribed_to:
                try:
                    to_date = datetime.strptime(filter_subscribed_to, '%Y-%m-%d').replace(tzinfo=timezone.utc)
                    # Add 1 day to include the end date fully
                    to_date = to_date + timedelta(days=1)
                    subscription_date_conditions['$lt'] = to_date
                except ValueError:
                    logger.warning(f"Invalid subscribed_to date format: {filter_subscribed_to}")

            if subscription_date_conditions:
                user_query["registration_time"] = subscription_date_conditions

            if code_values:
                users_cursor = mongo_db.db["master_user_data"].find(
                    user_query,
                    {"_id": 0, "access_code": 1, "user_id": 1, "subscription": 1, "registration_time": 1, "name": 1, "user_status": 1}
                )
                for user in users_cursor:
                    users_data[user['access_code']] = user

            # If user ID or subscription date filters are applied, filter the codes list
            if filter_user_id or filter_subscribed_from or filter_subscribed_to:
                # Keep only codes that have matching user data
                all_codes = [code for code in all_codes if code.get('code') in users_data]

            # Sort the codes based on the requested field
            if sort_field == 'status':
                # Sort by status (available, active, inactive, etc.)
                def get_status_sort_key(code_doc):
                    code_value = code_doc.get('code')
                    if code_value in users_data:
                        return users_data[code_value].get('user_status', 'zzz')
                    return 'available'  # Available codes come first or last depending on sort order

                all_codes.sort(key=get_status_sort_key, reverse=(sort_order == -1))

            elif sort_field == 'user_id':
                # Sort by user ID
                def get_user_id_sort_key(code_doc):
                    code_value = code_doc.get('code')
                    if code_value in users_data:
                        return str(users_data[code_value].get('user_id', ''))
                    return ''  # Empty string for codes without users

                all_codes.sort(key=get_user_id_sort_key, reverse=(sort_order == -1))

            elif sort_field == 'subscribed_on':
                # Sort by subscription date
                def get_subscription_date_sort_key(code_doc):
                    code_value = code_doc.get('code')
                    if code_value in users_data and 'registration_time' in users_data[code_value]:
                        reg_time = users_data[code_value]['registration_time']
                        # Ensure datetime is timezone-aware
                        if reg_time and isinstance(reg_time, datetime):
                            if reg_time.tzinfo is None:
                                reg_time = reg_time.replace(tzinfo=timezone.utc)
                            return reg_time
                        # Handle string dates or other formats
                        elif reg_time:
                            try:
                                # Try to parse as string if it's not a datetime
                                if isinstance(reg_time, str):
                                    parsed_date = datetime.fromisoformat(reg_time.replace('Z', '+00:00'))
                                    return parsed_date
                                return reg_time
                            except (ValueError, TypeError):
                                # If parsing fails, return a default date
                                return datetime.min.replace(tzinfo=timezone.utc)
                    # Default for codes without subscription
                    return datetime.min.replace(tzinfo=timezone.utc)

                all_codes.sort(key=get_subscription_date_sort_key, reverse=(sort_order == -1))

            elif sort_field == 'expiration_date':
                # Sort by expiration date
                current_time = datetime.now(timezone.utc)

                def get_expiration_date_sort_key(code_doc):
                    if 'expiration_date' in code_doc and code_doc['expiration_date']:
                        # Calculate days remaining using date-only comparison
                        expiration_date = code_doc['expiration_date']
                        if expiration_date.tzinfo is None:
                            expiration_date = expiration_date.replace(tzinfo=timezone.utc)
                        # Extract date components only
                        expiration_date_only = expiration_date.date()
                        current_date_only = current_time.date()
                        # Calculate the difference in days
                        delta = (expiration_date_only - current_date_only).days
                        # Ensure non-negative value
                        days_remaining = max(0, delta)
                        return days_remaining
                    return -float('inf')  # Codes without expiration date come last

                all_codes.sort(key=get_expiration_date_sort_key, reverse=(sort_order == -1))

            # Apply pagination after sorting
            total_codes_matching_query = len(all_codes)
            codes_on_page = all_codes[skip:skip + per_page]
        else:
            # For other fields, we can use MongoDB's native sorting
            # But first check if we need to apply user-related filters
            if filter_user_id or filter_subscribed_from or filter_subscribed_to:
                # We need to fetch all codes and filter them by user data
                all_codes = list(mongo_db.db["access_code"].find(query))

                # Get all user data for these codes in one query
                code_values = [c.get('code') for c in all_codes if c.get('code')]
                users_data = {}

                # Build user data query with additional filters
                user_query = {"access_code": {"$in": code_values}}

                # Add user ID filter if provided
                if filter_user_id:
                    try:
                        # Try to convert to number for exact match
                        user_id_num = int(filter_user_id)
                        user_query["user_id"] = user_id_num
                    except ValueError:
                        # If not a valid number, use string comparison
                        # This will convert NumberLong to string for comparison
                        user_query["user_id"] = {"$regex": filter_user_id, "$options": "i"}

                # Add subscription date range filters
                subscription_date_conditions = {}
                if filter_subscribed_from:
                    try:
                        from_date = datetime.strptime(filter_subscribed_from, '%Y-%m-%d').replace(tzinfo=timezone.utc)
                        subscription_date_conditions['$gte'] = from_date
                    except ValueError:
                        logger.warning(f"Invalid subscribed_from date format: {filter_subscribed_from}")

                if filter_subscribed_to:
                    try:
                        to_date = datetime.strptime(filter_subscribed_to, '%Y-%m-%d').replace(tzinfo=timezone.utc)
                        # Add 1 day to include the end date fully
                        to_date = to_date + timedelta(days=1)
                        subscription_date_conditions['$lt'] = to_date
                    except ValueError:
                        logger.warning(f"Invalid subscribed_to date format: {filter_subscribed_to}")

                if subscription_date_conditions:
                    user_query["registration_time"] = subscription_date_conditions

                if code_values:
                    users_cursor = mongo_db.db["master_user_data"].find(
                        user_query,
                        {"_id": 0, "access_code": 1, "user_id": 1, "subscription": 1, "registration_time": 1, "name": 1, "user_status": 1}
                    )
                    for user in users_cursor:
                        users_data[user['access_code']] = user

                # Filter the codes list by user data
                all_codes = [code for code in all_codes if code.get('code') in users_data]

                # Sort the filtered codes
                all_codes.sort(key=lambda x: x.get(mongo_sort_field, ''), reverse=(sort_order == -1))

                # Apply pagination
                total_codes_matching_query = len(all_codes)
                codes_on_page = all_codes[skip:skip + per_page]
            else:
                # No user-related filters, use MongoDB's native sorting
                codes_cursor = mongo_db.db["access_code"].find(query).sort(mongo_sort_field, sort_order).skip(skip).limit(per_page)
                codes_on_page = list(codes_cursor)

        # --- Determine Usage Status ---
        # Get codes present on the current page to check their usage status
        page_code_values = [c.get('code') for c in codes_on_page if c.get('code')]
        used_codes = {}
        if page_code_values:
            # Find users associated with the codes on the current page
            users_cursor = mongo_db.db["master_user_data"].find(
                {"access_code": {"$in": page_code_values}},
                {"_id": 0, "access_code": 1, "user_id": 1, "subscription": 1, "registration_time": 1, "name": 1, "user_status": 1} # Project needed fields including user_status
            )
            for sub in users_cursor:
                user_id_str = str(sub.get('user_id', 'N/A'))
                user_name = sub.get('name', 'Unknown Name')
                used_codes[sub['access_code']] = {
                    'user_id': user_id_str,
                    'user_name': user_name,
                    'status': 'active' if sub.get('subscription') == 'active' else 'inactive',
                    'user_status': sub.get('user_status', 'inactive'),  # Include the user_status field
                    'subscription_time': sub.get('registration_time')
                }

        # --- Filter by Status (Post-Fetch) ---
        # Now apply the status filter to the codes fetched for the current page
        filtered_codes_for_template = []
        for code in codes_on_page:
            code_value = code.get('code')
            is_used = code_value in used_codes
            usage_info = used_codes.get(code_value, {})
            status_matches = False

            if filter_status == 'all':
                status_matches = True
            elif filter_status == 'available' and not is_used:
                status_matches = True
            elif filter_status == 'used' and is_used:
                status_matches = True
            elif filter_status == 'active' and is_used and (usage_info.get('user_status') == 'active' or usage_info.get('status') == 'active'):
                status_matches = True
            elif filter_status == 'inactive' and is_used and usage_info.get('user_status') != 'active' and usage_info.get('status') != 'active':
                # Includes 'unknown', 'expired', 'cancelled', etc.
                status_matches = True

            if status_matches:
                 # Convert ObjectId and dates for template compatibility
                 if '_id' in code and ObjectId and isinstance(code['_id'], ObjectId):
                     code['_id_str'] = str(code['_id']) # Use a different key like _id_str
                 else:
                     code['_id_str'] = str(code.get('_id', ''))

                 for date_field in ['added_at', 'updated_at', 'reg_date']:
                     if date_field in code and isinstance(code[date_field], datetime):
                         if date_field == 'reg_date':
                              code[f'{date_field}_str'] = code[date_field].strftime('%Y-%m-%d')
                         else:
                              code[f'{date_field}_str'] = code[date_field].strftime('%Y-%m-%d %H:%M:%S')
                     elif date_field in code: # Handle if stored as string already
                         code[f'{date_field}_str'] = str(code[date_field])

                 filtered_codes_for_template.append(code)

        # --- Prepare Pagination Data ---
        # Note: Total count for pagination is based on the initial query (code search),
        # not the final status-filtered list on the page. This is standard practice.
        pagination = {
            'page': page,
            'per_page': per_page,
            'total': total_codes_matching_query,
            'total_pages': total_pages,
            'has_next': page < total_pages,
            'has_prev': page > 1,
            'allowed_per_page': allowed_per_page
        }
        start_item = skip + 1
        end_item = min((page * per_page), total_codes_matching_query)

        return render_template(
            'access_codes.html',
            access_codes=filtered_codes_for_template, # Use the filtered list for display
            used_codes=used_codes, # Pass usage info for codes on the page
            pagination=pagination,
            start_item=start_item,
            end_item=end_item,
            sort_field=sort_field,
            sort_order=sort_order_str,
            filter_status=filter_status,
            filter_query=filter_query,
            filter_user_id=filter_user_id,
            filter_subscribed_from=filter_subscribed_from,
            filter_subscribed_to=filter_subscribed_to,
            filter_expired_from=filter_expired_from,
            filter_expired_to=filter_expired_to,
            show_advanced_filters=show_advanced_filters,
            now=datetime.now(timezone.utc) # Pass current datetime for expiration calculations
        )

    except Exception as e:
        logger.error(f"Error fetching access codes page {page}: {e}", exc_info=True)
        flash('Error retrieving access codes. Please try again later.', 'danger')
        return render_template(
            'access_codes.html',
            access_codes=[],
            used_codes={},
            pagination={'page': 1, 'per_page': per_page, 'total': 0, 'total_pages': 1, 'has_next': False, 'has_prev': False, 'allowed_per_page': allowed_per_page},
            start_item=0,
            end_item=0,
            sort_field='status',
            sort_order='asc',
            filter_status='all',
            filter_query='',
            filter_user_id='',
            filter_subscribed_from='',
            filter_subscribed_to='',
            filter_expired_from='',
            filter_expired_to='',
            show_advanced_filters=False,
            now=datetime.now(timezone.utc) # Pass current datetime for expiration calculations
        )


# --- Add Single Access Code ---
def add_access_code():
    """Handles the form submission for adding a single access code."""
    code = request.form.get('code', '').strip()
    admin_id = session.get('admin_id')
    expiration_days_str = request.form.get('expiration_days', '').strip()

    if not admin_id:
         flash('Authentication error. Please log in again.', 'error')
         return redirect(tenant_url_for('auth.login')) # Redirect to login if session lost

    if not code:
        flash('Access code cannot be empty.', 'warning')
        return redirect(tenant_url_for('dashboard.access_codes_route')) # Use correct blueprint endpoint name

    # Process expiration days - default to 30 days if not provided
    expiration_date = None
    expiration_days = None

    if expiration_days_str:
        try:
            expiration_days = int(expiration_days_str)
            if expiration_days <= 0:
                flash('Expiration days must be a positive number.', 'warning')
                return redirect(tenant_url_for('dashboard.access_codes_route'))
        except ValueError:
            flash('Invalid expiration days value. Please enter a valid number.', 'warning')
            return redirect(tenant_url_for('dashboard.access_codes_route'))
    else:
        # Default to 30 days if not provided
        expiration_days = 30

    # Calculate expiration date
    expiration_date = datetime.now(timezone.utc) + timedelta(days=expiration_days)

    try:
        existing_code = mongo_db.db["access_code"].find_one({"code": code})
        if existing_code:
            flash(f'Access code "{code}" already exists.', 'error')
            return redirect(tenant_url_for('dashboard.access_codes_route'))

        # Prepare code document
        code_doc = {
            "code": code,
            "source": "manually_added",
            "added_by": admin_id, # Storing as whatever type admin_id is (e.g., ObjectId or string)
            "added_at": datetime.now(timezone.utc)
        }

        # Add expiration date if provided
        if expiration_date is not None:
            code_doc["expiration_date"] = expiration_date

        # Insert the new code
        insert_result = mongo_db.db["access_code"].insert_one(code_doc)

        if insert_result.inserted_id:
            flash(f'Access code "{code}" added successfully.', 'success')
            # Log activity
            admin = mongo_db.get_admin_by_id(admin_id)
            admin_username = admin['username'] if admin else f"Admin ID {admin_id}"
            log_details = f"Admin {admin_username} added access code: {code}"
            if expiration_date:
                log_details += f" with expiration date {expiration_date.strftime('%Y-%m-%d')}"
            mongo_db.add_activity_log(
                action="Add Access Code",
                details=log_details,
                admin_id=admin_id
            )
        else:
             # Should not happen if no exception, but as a safeguard
             flash('Failed to add access code.', 'error')
             logger.error(f"Failed to insert access code {code} for admin {admin_id}, but no exception raised.")

    except Exception as e:
        logger.error(f"Error adding access code '{code}' by admin {admin_id}: {e}", exc_info=True)
        flash('Error adding access code due to a server issue. Please try again.', 'danger')

    return redirect(tenant_url_for('dashboard.access_codes_route'))

# --- Upload Access Codes from File (Refined Version) ---
def upload_access_codes():
    """Handles uploading access codes from a .txt or .csv file."""
    admin_id = session.get('admin_id')
    if not admin_id:
        # *** CORRECTED INDENTATION HERE ***
        flash('Authentication error. Please log in again.', 'error')
        return redirect(tenant_url_for('auth.login'))

    # Check if this is an API request
    is_api_request = session.get('api_request', False)

    # Fetch admin username early for logging/messages
    admin = mongo_db.get_admin_by_id(admin_id)
    admin_username = admin['username'] if admin else f"Admin ID {admin_id}"

    # Only log for UI requests
    if not is_api_request:
        logger.info(f"Admin {admin_username} initiating access code upload.")

    if 'file' not in request.files:
        flash('No file part in the request', 'error')
        logger.warning(f"Upload attempt failed by Admin {admin_username}: No file part.")
        return redirect(tenant_url_for('dashboard.access_codes_route'))

    file = request.files['file']
    if file.filename == '':
        flash('No file selected for upload', 'error')
        logger.warning(f"Upload attempt failed by Admin {admin_username}: No file selected.")
        return redirect(tenant_url_for('dashboard.access_codes_route'))

    # Check for pymongo dependencies needed for uploads
    if not UpdateOne and file.filename.lower().endswith('.csv'):
         flash('Server configuration error: Missing dependency for CSV upload (UpdateOne). Please contact support.', 'danger')
         logger.critical("Upload Access Codes: pymongo.UpdateOne is not available, needed for CSV bulk write.")
         return redirect(tenant_url_for('dashboard.access_codes_route'))


    added_count = 0
    updated_count = 0
    skipped_in_file_duplicates = 0
    skipped_db_duplicates = 0 # Specifically for text files hitting existing DB entries
    skipped_errors = 0
    processed_count = 0
    file_type = 'unknown'

    # Set upload source based on whether it's an API or UI request
    is_api_request = session.get('api_request', False)
    upload_source = "unknown_upload"

    try:
        # Use original filename for logging clarity, but be mindful if using it elsewhere
        safe_filename = file.filename

        logger.info(f"Processing uploaded file: {safe_filename} by Admin {admin_username}")

        content = file.read().decode('utf-8-sig') # Handle BOM

        if safe_filename.lower().endswith('.csv'):
            file_type = 'csv'
            upload_source = "api_csv_upload" if is_api_request else "csv_upload"
        elif safe_filename.lower().endswith('.txt'):
            file_type = 'text'
            upload_source = "api_text_upload" if is_api_request else "text_upload"
        else:
            flash('Unsupported file type. Please upload a .txt or .csv file.', 'error')
            logger.warning(f"Upload attempt failed by Admin {admin_username}: Unsupported file type {safe_filename}")
            return redirect(tenant_url_for('dashboard.access_codes_route'))

        codes_to_insert = [] # Used only for text files
        bulk_update_ops = [] # Used only for CSV files

        if file_type == 'csv':
            csv_file = StringIO(content)
            try:
                # Increased sniff size for potentially messy CSVs
                dialect = csv.Sniffer().sniff(csv_file.read(2048))
                csv_file.seek(0)
                csv_reader = csv.DictReader(csv_file, dialect=dialect)
                # Normalize field names (lowercase, strip spaces) for robustness
                csv_reader.fieldnames = [name.lower().strip() for name in csv_reader.fieldnames] if csv_reader.fieldnames else []
                required_header = 'client_account' # Assuming 'client_account' is the code field
                if not csv_reader.fieldnames or required_header not in csv_reader.fieldnames:
                    raise ValueError(f"CSV file must contain a '{required_header}' header. Found headers: {csv_reader.fieldnames}")
            except (csv.Error, ValueError) as e:
                flash(f"Could not process CSV file '{safe_filename}': {e}. Ensure it's valid and contains the required headers.", "error")
                logger.error(f"CSV Processing Error for {safe_filename} by Admin {admin_username}: {e}")
                return redirect(tenant_url_for('dashboard.access_codes_route'))

            processed_codes_in_file = set() # Track codes within this CSV to avoid duplicate operations
            for row_num, row in enumerate(csv_reader, start=2): # Start at 2 because 1 is header
                processed_count += 1
                access_code = row.get(required_header, '').strip()

                if not access_code:
                    logger.warning(f"Skipping row {row_num} in CSV '{safe_filename}': empty {required_header}")
                    skipped_errors += 1
                    continue

                if access_code in processed_codes_in_file:
                    logger.warning(f"Skipping duplicate code '{access_code}' within CSV '{safe_filename}' at row {row_num}.")
                    skipped_in_file_duplicates += 1
                    continue
                processed_codes_in_file.add(access_code)

                # Extract other potential fields (case-insensitive keys from normalized headers)
                reg_date_str = row.get('reg_date', '').strip()
                volume_mln_usd = row.get('volume_mln_usd', '').strip()
                trade_fn = row.get('trade_fn', '').strip()

                reg_date = None
                if reg_date_str:
                    parsed = False
                    # Add more formats if needed, try common ones first
                    possible_formats = ['%Y-%m-%d', '%m/%d/%Y', '%d-%b-%Y', '%Y%m%d', '%m-%d-%Y']
                    for fmt in possible_formats:
                        try:
                            reg_date = datetime.strptime(reg_date_str, fmt)
                            parsed = True
                            break # Stop if format matches
                        except ValueError:
                            continue
                    if not parsed:
                        logger.warning(f"Could not parse reg_date '{reg_date_str}' for code '{access_code}' in '{safe_filename}' (row {row_num}). Storing as string.")
                        reg_date = reg_date_str # Store as string if parsing fails

                # Calculate default expiration date (30 days from now)
                default_expiration_date = datetime.now(timezone.utc) + timedelta(days=30)

                # Prepare the update operation for bulk_write
                update_data = {
                    "$set": {
                        # Update these fields regardless of whether it's an insert or update
                        "reg_date": reg_date,
                        "volume_mln_usd": volume_mln_usd,
                        "trade_fn": trade_fn,
                        "updated_at": datetime.now(timezone.utc),
                        # Always update source on CSV upload to reflect latest info source
                        "source": upload_source,
                        "added_by": admin_id # Update who last touched it via upload
                    },
                    "$setOnInsert": {
                        # These fields are only set when a *new* document is created (upsert=True)
                        "code": access_code,
                        "added_at": datetime.now(timezone.utc),
                        "expiration_date": default_expiration_date  # Only set expiration date for new codes
                    }
                }
                bulk_update_ops.append(UpdateOne({"code": access_code}, update_data, upsert=True))

        elif file_type == 'text':
            lines = [line.strip() for line in content.splitlines() if line.strip()]
            processed_count = len(lines)
            unique_codes_from_file = list(set(lines))
            skipped_in_file_duplicates = len(lines) - len(unique_codes_from_file)

            # For text files, we simply try to insert new ones.
            # We will check for existing ones using insert_many(ordered=False)
            # and calculate skipped_db_duplicates later based on the result.
            # Calculate default expiration date (30 days from now)
            default_expiration_date = datetime.now(timezone.utc) + timedelta(days=30)

            for code in unique_codes_from_file:
                code_data = {
                    "code": code,
                    "source": upload_source,
                    "added_by": admin_id,
                    "added_at": datetime.now(timezone.utc),
                    "expiration_date": default_expiration_date  # Add default 30-day expiration
                    # Text files don't usually contain extra fields like reg_date
                }
                codes_to_insert.append(code_data)

        # --- Perform Bulk Operations ---
        if codes_to_insert: # Text file case
            if not codes_to_insert:
                 logger.info("No unique codes found in the text file to insert.")
                 flash("The uploaded text file contained no unique codes to add.", "info")
            else:
                 try:
                    # ordered=False: continues inserting even if duplicates (in DB) are encountered
                    # Returns BulkWriteResult
                    insert_result = mongo_db.db["access_code"].insert_many(codes_to_insert, ordered=False)
                    added_count = len(insert_result.inserted_ids)
                    # Calculate how many were skipped because they already existed in the DB
                    attempted_inserts = len(codes_to_insert)
                    # Duplicates cause a BulkWriteError caught below, not reflected directly here.
                    # We rely on the exception handling to report errors.
                    skipped_db_duplicates = attempted_inserts - added_count # Initial estimate
                    logger.info(f"Text file insert: Attempted={attempted_inserts}, Added={added_count}")

                 except Exception as e: # Catch pymongo.errors.BulkWriteError specifically if needed
                    # Handle duplicate key errors gracefully if possible
                    if hasattr(e, 'details') and 'writeErrors' in e.details:
                         # Count successful inserts if any happened before the error stopped processing (if ordered=True)
                         # Or count how many failed due to duplicate key (error code 11000)
                         duplicate_errors = sum(1 for err in e.details['writeErrors'] if err.get('code') == 11000)
                         # Successful writes might be in e.details['nInserted'] if available
                         added_count = e.details.get('nInserted', 0)
                         skipped_db_duplicates = duplicate_errors
                         other_errors = len(e.details['writeErrors']) - duplicate_errors
                         logger.warning(f"Bulk insert for text file '{safe_filename}' encountered errors. Added: {added_count}, Skipped (DB Duplicates): {skipped_db_duplicates}, Other Errors: {other_errors}")
                         if other_errors > 0:
                              flash(f"Completed text upload with errors. Added {added_count}, Skipped {skipped_db_duplicates} DB duplicates. {other_errors} other errors occurred.", "warning")
                         # Fallthrough to general flash/log below, counts are now updated
                    else:
                         # General error during bulk insert
                         logger.error(f"Error during bulk insert for text file '{safe_filename}': {e}", exc_info=True)
                         flash(f"An error occurred during the bulk insert: {e}", "danger")
                         added_count = 0 # Reset counts on general error
                         skipped_db_duplicates = len(codes_to_insert)


        if bulk_update_ops: # CSV file case
            if not bulk_update_ops:
                logger.info("No valid rows found in the CSV file to process.")
                flash("The uploaded CSV file contained no data rows to process.", "info")
            else:
                try:
                    # ordered=False: process all operations even if some fail (like validation errors if schema used)
                    # Returns BulkWriteResult
                    update_result = mongo_db.db["access_code"].bulk_write(bulk_update_ops, ordered=False)
                    added_count = update_result.upserted_count # New codes added via upsert
                    updated_count = update_result.modified_count # Existing codes updated
                    # Check for errors during the write operations
                    if update_result.bulk_api_result.get('writeErrors'):
                         num_errors = len(update_result.bulk_api_result['writeErrors'])
                         skipped_errors += num_errors # Count these as skipped due to error
                         logger.error(f"Write errors occurred during CSV bulk write for '{safe_filename}': {num_errors} errors. Details: {update_result.bulk_api_result['writeErrors']}")
                         flash(f"CSV upload completed, but {num_errors} errors occurred during database update. Check logs for details.", "warning")
                         # Note: added_count and updated_count reflect successful ops before errors if ordered=False

                except Exception as e: # Catch pymongo.errors.BulkWriteError or others
                    logger.error(f"Error during bulk write for CSV file '{safe_filename}': {e}", exc_info=True)
                    flash(f"A critical error occurred during the CSV bulk update/insert: {e}", "danger")
                    added_count = 0 # Reset counts on error
                    updated_count = 0


        # --- Flash Messages and Logging ---
        log_details_parts = [
            f"Admin {admin_username} uploaded {safe_filename} ({file_type}).",
            f"Processed: {processed_count} items.",
        ]
        flash_messages = []
        final_log_status = "Completed"

        if file_type == 'csv':
            log_details_parts.append(f"Added(New): {added_count}.")
            log_details_parts.append(f"Updated(Existing): {updated_count}.")
            log_details_parts.append(f"Skipped(In-file duplicates): {skipped_in_file_duplicates}.")
            log_details_parts.append(f"Skipped(Row/Write errors): {skipped_errors}.") # Combined errors
            flash_msg = f"CSV Upload: Added {added_count} new, updated {updated_count} existing."
            skipped_total = skipped_in_file_duplicates + skipped_errors
            if skipped_total > 0:
                flash_msg += f" Skipped {skipped_total} ({skipped_in_file_duplicates} duplicates in file, {skipped_errors} row/write errors)."
            flash_level = 'success' if (added_count > 0 or updated_count > 0) else 'info'
            # Downgrade to warning if errors occurred but some succeeded
            if skipped_errors > 0 and (added_count > 0 or updated_count > 0):
                flash_level = 'warning'
            flash_messages.append((flash_msg, flash_level))
            if skipped_errors > 0: final_log_status = "Completed with errors"


        elif file_type == 'text':
            log_details_parts.append(f"Added(New): {added_count}.")
            log_details_parts.append(f"Skipped(In-file duplicates): {skipped_in_file_duplicates}.")
            log_details_parts.append(f"Skipped(Already in DB or Write Errors): {skipped_db_duplicates}.") # Combined
            flash_msg = f"Text Upload: Added {added_count} new access codes."
            skipped_total = skipped_in_file_duplicates + skipped_db_duplicates
            if skipped_total > 0:
                 flash_msg += f" Skipped {skipped_total} duplicates (in file or already in database/errors)."
            flash_level = 'success' if added_count > 0 else 'info'
            if skipped_db_duplicates > 0 and added_count > 0: # If some added but others skipped due to DB/error
                flash_level = 'warning'
            flash_messages.append((flash_msg, flash_level))
            if skipped_db_duplicates > 0: final_log_status = "Completed with skips/errors"


        # Log the summary
        log_summary = " ".join(log_details_parts)
        logger.info(log_summary)

        # Only add to activity log for UI requests, skip for API
        if not is_api_request:
            mongo_db.add_activity_log(
                action=f"Upload Access Codes ({file_type.upper()})",
                details=f"Status: {final_log_status}. {log_summary}",
                admin_id=admin_id
            )

        # Flash the messages collected
        for msg, level in flash_messages:
            flash(msg, level)

    except UnicodeDecodeError:
        logger.error(f"Error decoding uploaded file {file.filename} by Admin {admin_username}. Ensure it is UTF-8 encoded.", exc_info=True)
        flash('Error processing file: Could not decode file. Please ensure it is UTF-8 encoded.', 'error')

        # Only add to activity log for UI requests, skip for API
        if not is_api_request:
            mongo_db.add_activity_log(action="Upload Access Codes", details=f"Failed upload by Admin {admin_username} ({file.filename}): UTF-8 decoding error", admin_id=admin_id)
    except Exception as e:
        logger.error(f"Critical error during access code upload from file {file.filename} by Admin {admin_username}: {e}", exc_info=True)
        flash(f'An unexpected error occurred while processing the file: {str(e)}', 'error')

        # Only add to activity log for UI requests, skip for API
        if not is_api_request:
            mongo_db.add_activity_log(action="Upload Access Codes", details=f"Failed upload by Admin {admin_username or admin_id} ({file.filename}): Critical Error - {str(e)}", admin_id=admin_id)

    return redirect(tenant_url_for('dashboard.access_codes_route'))

# --- Edit Access Code ---
def edit_access_code():
    """Handles the form submission for editing an existing access code."""
    original_code = request.form.get('original_code', '').strip()
    new_code = request.form.get('new_code', '').strip()
    admin_id = session.get('admin_id')
    expiration_days_str = request.form.get('expiration_days', '').strip()

    if not admin_id:
        flash('Authentication error. Please log in again.', 'error')
        return redirect(tenant_url_for('auth.login'))

    if not original_code or not new_code:
        flash('Both original and new code values are required.', 'warning')
        return redirect(tenant_url_for('dashboard.access_codes_route'))

    # Process expiration days if provided
    expiration_date = None
    expiration_days = None
    update_expiration = False
    if expiration_days_str:
        try:
            expiration_days = int(expiration_days_str)
            if expiration_days <= 0:
                flash('Expiration days must be a positive number.', 'warning')
                return redirect(tenant_url_for('dashboard.access_codes_route'))
            # Calculate expiration date
            expiration_date = datetime.now(timezone.utc) + timedelta(days=expiration_days)
            update_expiration = True
        except ValueError:
            flash('Invalid expiration days value. Please enter a valid number.', 'warning')
            return redirect(tenant_url_for('dashboard.access_codes_route'))

    try:
        # Skip code check if only updating expiration
        code_changed = original_code != new_code

        if code_changed:
            # 1. Check if the NEW code already exists (unless it's the same as the original)
            existing_new_code = mongo_db.db["access_code"].find_one({"code": new_code})
            if existing_new_code:
                flash(f'The code "{new_code}" already exists. Cannot rename to a duplicate code.', 'error')
                return redirect(tenant_url_for('dashboard.access_codes_route'))

        # 2. Find the original code document
        original_doc = mongo_db.db["access_code"].find_one({"code": original_code})
        if not original_doc:
             flash(f'The original access code "{original_code}" was not found. It might have been deleted.', 'error')
             return redirect(tenant_url_for('dashboard.access_codes_route'))

        # 3. Check if original code is in use by an *active* subscription
        is_in_use = mongo_db.db["master_user_data"].find_one({"access_code": original_code, "user_status": "active"})
        if is_in_use and code_changed:  # Only block code changes for active codes, allow expiration updates
             user_id_str = str(is_in_use.get("user_id", "unknown"))
             flash(f'Cannot edit code "{original_code}" as it is currently in use by user ID {user_id_str}. Please resolve the subscription first.', 'warning')
             # Policy: Block edits of codes in active use.
             return redirect(tenant_url_for('dashboard.access_codes_route'))

        # 4. Prepare update document
        update_doc = {"updated_at": datetime.now(timezone.utc)}

        # Add code change if needed
        if code_changed:
            update_doc["code"] = new_code

        # Add expiration date if provided
        if update_expiration:
            update_doc["expiration_date"] = expiration_date

        # 5. Proceed with the update
        result = mongo_db.db["access_code"].update_one(
            {"code": original_code}, # Filter by the original code again for safety
            {"$set": update_doc}
        )

        if result.modified_count > 0:
            admin = mongo_db.get_admin_by_id(admin_id)
            admin_username = admin['username'] if admin else f"Admin ID {admin_id}"

            # Build log message
            log_details = f"Admin {admin_username} updated access code"
            if code_changed:
                log_details += f" from '{original_code}' to '{new_code}'"
            else:
                log_details += f" '{original_code}'"

            if update_expiration:
                log_details += f" with expiration date {expiration_date.strftime('%Y-%m-%d')}"

            mongo_db.add_activity_log(
                action="Edit Access Code",
                details=log_details,
                admin_id=admin_id
            )

            # Build flash message
            flash_message = 'Access code successfully updated'
            if code_changed:
                flash_message += f' from "{original_code}" to "{new_code}"'
            if update_expiration:
                flash_message += f' with expiration date {expiration_date.strftime("%Y-%m-%d")}'
            flash(flash_message + '.', 'success')

        elif result.matched_count == 1:
             # This could happen if the update operation somehow didn't change the document
             flash('Code found, but update operation reported no changes. Please check the code.', 'warning')
             logger.warning(f"Edit Access Code: Matched '{original_code}' but modified_count was 0 when changing to '{new_code}'.")
        else:
            # Should have been caught by find_one earlier, but as a fallback
            flash(f'Original access code "{original_code}" not found during update attempt.', 'error')
            logger.error(f"Edit Access Code: Failed to find '{original_code}' during update_one, despite finding it earlier.")

    except Exception as e:
        logger.error(f"Error editing access code from '{original_code}' to '{new_code}' by admin {admin_id}: {e}", exc_info=True)
        flash('An error occurred while editing the access code. Please try again.', 'danger')

    return redirect(tenant_url_for('dashboard.access_codes_route'))

# --- Delete Single Access Code (AJAX) ---
def delete_access_code():
    """Handles AJAX request to delete a single access code, with 'in-use' check."""
    admin_id = session.get('admin_id')
    if not admin_id:
         return jsonify({"success": False, "message": "Authentication required."}), 401

    if not request.is_json:
        logger.error("Delete access code request failed: Content-Type is not application/json")
        return jsonify({"success": False, "message": "Invalid request format, expected JSON"}), 400

    data = request.get_json()
    if not data:
        logger.error("Delete access code request failed: No JSON data received")
        return jsonify({"success": False, "message": "No data received"}), 400

    code = data.get('code', '').strip()
    force = data.get('force', False) # Check if frontend sent force confirmation

    if not code:
        logger.error("Delete access code request failed: 'code' field is missing or empty")
        return jsonify({"success": False, "message": "Missing 'code' parameter"}), 400

    logger.info(f"Admin ID {admin_id} attempting to delete access code: '{code}', force: {force}")

    try:
        # 1. Check if the code exists
        code_doc = mongo_db.db["access_code"].find_one({"code": code}, {"_id": 1}) # Only need existence check
        if not code_doc:
             logger.warning(f"Attempted to delete non-existent access code: '{code}' by Admin ID {admin_id}")
             # Return success=true because the desired state (code doesn't exist) is achieved.
             return jsonify({"success": True, "message": f"Access code '{code}' not found (perhaps already deleted?)."})

        # 2. Check if the code is in use by an *active* subscription
        is_in_use = mongo_db.db["master_user_data"].find_one(
            {"access_code": code, "user_status": "active"},
            {"_id": 0, "user_id": 1} # Project only user_id
        )

        # 3. Handle 'in-use' case: require force confirmation
        if is_in_use and not force:
            user_id_str = str(is_in_use.get('user_id', 'unknown'))
            logger.warning(f"Deletion of code '{code}' requires confirmation (in use by User ID: {user_id_str}). Request by Admin ID {admin_id}.")
            return jsonify({
                "success": False,
                "message": f"Access code '{code}' is currently in use by User ID: {user_id_str}. Deletion requires confirmation.",
                "requires_confirmation": True,
                "user_id": user_id_str # Send user_id back for context in confirmation dialog
            }), 200 # Use 200 OK for actions requiring user confirmation

        # 4. Proceed with deletion (either not in use, or force=True)
        delete_result = mongo_db.db["access_code"].delete_one({"code": code})

        admin = mongo_db.get_admin_by_id(admin_id)
        admin_username = admin['username'] if admin else f"Admin ID {admin_id}"
        log_details = f"Admin {admin_username} deleted access code: {code}"
        status_message = f"Access code '{code}' deleted successfully."

        if is_in_use and force:
            user_id_str = str(is_in_use.get('user_id', 'unknown'))
            log_details += f" (FORCE deleted while in use by User ID: {user_id_str})"
            status_message = f"Access code '{code}' FORCE deleted successfully (was in use by User ID: {user_id_str})."
            logger.warning(log_details) # Log force deletions as warnings

        mongo_db.add_activity_log(
            action="Delete Access Code",
            details=log_details,
            admin_id=admin_id
        )

        if delete_result.deleted_count > 0:
            logger.info(f"Successfully deleted access code: '{code}' by Admin {admin_username}")
            return jsonify({"success": True, "message": status_message})
        else:
            # Should have been caught by find_one earlier, but maybe race condition?
             logger.error(f"Code '{code}' found initially but delete_one reported 0 deleted for Admin {admin_username}.")
             return jsonify({"success": False, "message": f"Access code '{code}' found but could not be deleted. Please refresh and try again."}), 500

    except Exception as e:
        logger.error(f"Error during access code deletion for code '{code}' by Admin ID {admin_id}: {e}", exc_info=True)
        return jsonify({"success": False, "message": "An internal server error occurred during deletion."}), 500

# --- Get Access Code Details (AJAX) ---
def access_code_details(code):
    """API endpoint to get detailed information for a specific access code."""
    admin_id = session.get('admin_id')
    if not admin_id:
        return jsonify({"success": False, "message": "Authentication required."}), 401

    if not code:
         return jsonify({"success": False, "message": "No code provided"}), 400

    try:
        code_details = mongo_db.db["access_code"].find_one({"code": code})

        if not code_details:
            return jsonify({"success": False, "message": "Access code not found"}), 404

        # Prepare data for JSON serialization (convert ObjectId, datetime)
        if '_id' in code_details and ObjectId and isinstance(code_details['_id'], ObjectId):
            code_details['_id'] = str(code_details['_id'])
        if 'added_by' in code_details:
            # Store added_by ID as string regardless of original type
            added_by_id_str = str(code_details['added_by'])
            code_details['added_by'] = added_by_id_str # Overwrite/ensure it's string
            # Attempt to fetch username based on the ID
            try:
                # Attempt conversion to ObjectId if needed for lookup
                lookup_id = ObjectId(added_by_id_str) if ObjectId and ObjectId.is_valid(added_by_id_str) else added_by_id_str
                added_by_admin = mongo_db.get_admin_by_id(lookup_id)
                code_details['added_by_username'] = added_by_admin['username'] if added_by_admin else f"ID: {added_by_id_str}"
            except Exception: # Handle invalid ObjectId format or other lookup errors
                code_details['added_by_username'] = f"ID: {added_by_id_str} (Lookup failed)"


        for date_field in ['added_at', 'updated_at']:
             if date_field in code_details and isinstance(code_details[date_field], datetime):
                 # Use ISO format with Z for UTC, suitable for JS Date objects
                 code_details[date_field] = code_details[date_field].isoformat() + 'Z'

        if 'reg_date' in code_details:
            if isinstance(code_details['reg_date'], datetime):
                # Format date part only for display
                 code_details['reg_date'] = code_details['reg_date'].strftime('%Y-%m-%d')
            # If stored as string, leave it as is

        # Log admin activity (optional, can be noisy)
        # admin = mongo_db.get_admin_by_id(admin_id)
        # admin_username = admin['username'] if admin else f"Admin ID {admin_id}"
        # mongo_db.add_activity_log(action="View Access Code Details", details=f"Admin {admin_username} viewed details for code {code}", admin_id=admin_id)

        return jsonify({"success": True, "data": code_details})

    except Exception as e:
        logger.error(f"Error getting access code details for '{code}' by Admin ID {admin_id}: {e}", exc_info=True)
        return jsonify({"success": False, "message": "An internal server error occurred."}), 500

# --- Check if Code is In Use (AJAX) ---
def check_if_code_in_use(code):
    """Check if an access code is currently associated with *any* subscription (active or not)."""
    admin_id = session.get('admin_id') # Optional: Add auth check if needed for this endpoint
    if not admin_id:
        return jsonify({"success": False, "message": "Authentication required."}), 401

    if not code:
        return jsonify({"success": False, "message": "No code provided."}), 400

    try:
        # Check if code exists in master_user_data collection
        user_data = mongo_db.db["master_user_data"].find_one(
            {"access_code": code},
            {"_id": 0, "user_id": 1, "user_status": 1} # Project only needed fields
        )

        if user_data:
            user_id_str = str(user_data.get("user_id", "N/A"))
            status = user_data.get("user_status", "inactive")
            return jsonify({
                "success": True,
                "in_use": True,
                "user_id": user_id_str,
                "status": status
            })
        else:
            return jsonify({"success": True, "in_use": False})
    except Exception as e:
        logger.error(f"Error checking if code '{code}' is in use: {e}", exc_info=True)
        return jsonify({"success": False, "message": f"Error checking code usage: {str(e)}"}), 500

# --- Bulk Delete Access Codes (AJAX) ---
def bulk_delete_access_codes():
    """Handles AJAX request for bulk deletion of access codes, checking for active use."""
    admin_id = session.get('admin_id')
    if not admin_id:
        return jsonify({"success": False, "message": "Authentication required."}), 401

    if not request.is_json:
        return jsonify({"success": False, "message": "Invalid request format, expected JSON"}), 400

    data = request.get_json()
    if not data or 'codes' not in data or not isinstance(data['codes'], list):
        return jsonify({"success": False, "message": "Invalid request data: 'codes' array missing or invalid."}), 400

    # Sanitize input codes
    codes_to_delete = list(set([c.strip() for c in data['codes'] if isinstance(c, str) and c.strip()]))
    if not codes_to_delete:
        return jsonify({"success": False, "message": "No valid codes provided for deletion."}), 400

    logger.info(f"Admin ID {admin_id} attempting bulk delete of {len(codes_to_delete)} codes: {codes_to_delete}")

    try:
        # 1. Check which of the provided codes are in use by *active* subscriptions
        in_use_pipeline = [
             {"$match": {"access_code": {"$in": codes_to_delete}, "user_status": "active"}},
             {"$group": {"_id": "$access_code", "user_id": {"$first": "$user_id"}}} # Get code and one user using it
        ]
        in_use_results = mongo_db.db["master_user_data"].aggregate(in_use_pipeline)
        in_use_map = {doc["_id"]: str(doc["user_id"]) for doc in in_use_results} # Store user ID as string

        # 2. If any codes are in use, block the bulk deletion. Require individual deletion with force.
        if in_use_map:
            in_use_codes_list = list(in_use_map.keys())
            logger.warning(f"Bulk delete blocked for Admin ID {admin_id}. Codes in use: {in_use_codes_list}")
            # Provide more context in the message
            in_use_details = [f"'{code}' (User ID: {user_id})" for code, user_id in in_use_map.items()]
            return jsonify({
                "success": False,
                "message": f"Cannot bulk delete. The following {len(in_use_map)} code(s) are currently in use: {', '.join(in_use_details)}. Please deselect them or delete individually using the single delete action (which allows confirmation).",
                "in_use_codes": in_use_codes_list # Send back list of codes for frontend UI highlighting
            }), 400 # Bad request because the request cannot be fulfilled safely as is

        # 3. Proceed with deletion if no codes are in active use
        # Although we checked all codes in `codes_to_delete`, the filter ensures we only delete those specified.
        delete_result = mongo_db.db["access_code"].delete_many({"code": {"$in": codes_to_delete}})
        deleted_count = delete_result.deleted_count

        admin = mongo_db.get_admin_by_id(admin_id)
        admin_username = admin['username'] if admin else f"Admin ID {admin_id}"
        log_details = f"Admin {admin_username} bulk deleted {deleted_count} access code(s)."
        # Log which codes were targeted, even if some didn't exist anymore
        # Avoid logging potentially huge list in detail, just count attempted
        log_details += f" (Targeted {len(codes_to_delete)} codes for deletion)"

        mongo_db.add_activity_log(
            action="Bulk Delete Access Codes",
            details=log_details,
            admin_id=admin_id
        )

        logger.info(f"Admin {admin_username} successfully bulk deleted {deleted_count} codes.")
        return jsonify({
            "success": True,
            "message": f"Successfully deleted {deleted_count} access code(s).",
            "deleted_count": deleted_count
        })

    except Exception as e:
        logger.error(f"Error during bulk access code deletion by Admin ID {admin_id}: {e}", exc_info=True)
        return jsonify({"success": False, "message": "An internal server error occurred during bulk deletion."}), 500

# The legacy delete_access_codes_legacy function has been removed.
# All calls are now redirected to the bulk_delete_access_codes function.