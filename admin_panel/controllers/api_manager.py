"""
API Key Management controller
"""
from flask import Blueprint, render_template, request, redirect, flash, session, jsonify
from utils.decorators import login_required
from utils.api_key import create_api_key, get_api_keys, update_api_key_status, delete_api_key
from utils.db_common import mongo_db
from utils.url_helpers import tenant_url_for
import logging

logger = logging.getLogger(__name__)
# Keep the leading slash in url_prefix as required by Werkzeug
api_manager_bp = Blueprint('api_manager', __name__, url_prefix='/api-manager')

@api_manager_bp.route('/')
@login_required
def index():
    """API Key Management page"""
    admin_id = session.get('admin_id')
    if not admin_id:
        flash('Authentication error. Please log in again.', 'error')
        return redirect(tenant_url_for('auth.login'))

    # Get all API keys
    api_keys = get_api_keys()

    # Get admin usernames for display
    admin_ids = set()
    for key in api_keys:
        if 'created_by' in key:
            admin_ids.add(key['created_by'])

    admins = {}
    for admin_id in admin_ids:
        admin = mongo_db.get_admin_by_id(admin_id)
        if admin:
            admins[admin_id] = admin.get('username', 'Unknown')

    # Check if there's a new API key to display
    new_api_key = None
    if 'new_api_key' in session:
        new_api_key = session.pop('new_api_key')  # Remove from session after retrieving

    return render_template('api_manager.html', api_keys=api_keys, admins=admins, new_api_key=new_api_key)

@api_manager_bp.route('/docs')
@login_required
def docs():
    """API Documentation page"""
    admin_id = session.get('admin_id')
    if not admin_id:
        flash('Authentication error. Please log in again.', 'error')
        return redirect(tenant_url_for('auth.login'))

    return render_template('api_docs.html')

@api_manager_bp.route('/create', methods=['POST'])
@login_required
def create_key():
    """Create a new API key"""
    admin_id = session.get('admin_id')
    if not admin_id:
        flash('Authentication error. Please log in again.', 'error')
        return redirect(tenant_url_for('auth.login'))

    name = request.form.get('name', '').strip()
    if not name:
        flash('API key name is required', 'error')
        return redirect(tenant_url_for('api_manager.index'))

    # Create new API key
    api_key, error = create_api_key(name, admin_id)

    if api_key:
        # Log activity
        mongo_db.add_activity_log(
            action="API Key Created",
            details=f"API key '{name}' created",
            admin_id=admin_id
        )

        # Store the API key in the session temporarily for display
        # It will be shown only once and then removed
        session['new_api_key'] = {
            'key': api_key,
            'name': name
        }

        flash('API key created successfully. Please copy your API key now - you will not be able to see it again!', 'warning')
    else:
        flash(f'Error creating API key: {error}', 'error')

    return redirect(tenant_url_for('api_manager.index'))

@api_manager_bp.route('/revoke/<api_key_id>', methods=['POST'])
@login_required
def revoke_key(api_key_id):
    """Revoke an API key"""
    admin_id = session.get('admin_id')
    if not admin_id:
        flash('Authentication error. Please log in again.', 'error')
        return redirect(tenant_url_for('auth.login'))

    # Update API key status
    if update_api_key_status(api_key_id, 'revoked'):
        flash('API key revoked successfully', 'success')
        # Log activity
        mongo_db.add_activity_log(
            action="API Key Revoked",
            details=f"API key ID {api_key_id} revoked",
            admin_id=admin_id
        )
    else:
        flash('Error revoking API key', 'error')

    return redirect(tenant_url_for('api_manager.index'))

@api_manager_bp.route('/activate/<api_key_id>', methods=['POST'])
@login_required
def activate_key(api_key_id):
    """Activate an API key"""
    admin_id = session.get('admin_id')
    if not admin_id:
        flash('Authentication error. Please log in again.', 'error')
        return redirect(tenant_url_for('auth.login'))

    # Update API key status
    if update_api_key_status(api_key_id, 'active'):
        flash('API key activated successfully', 'success')
        # Log activity
        mongo_db.add_activity_log(
            action="API Key Activated",
            details=f"API key ID {api_key_id} activated",
            admin_id=admin_id
        )
    else:
        flash('Error activating API key', 'error')

    return redirect(tenant_url_for('api_manager.index'))

@api_manager_bp.route('/delete/<api_key_id>', methods=['POST'])
@login_required
def delete_key(api_key_id):
    """Delete an API key"""
    admin_id = session.get('admin_id')
    if not admin_id:
        flash('Authentication error. Please log in again.', 'error')
        return redirect(tenant_url_for('auth.login'))

    # Delete API key
    if delete_api_key(api_key_id):
        flash('API key deleted successfully', 'success')
        # Log activity
        mongo_db.add_activity_log(
            action="API Key Deleted",
            details=f"API key ID {api_key_id} deleted",
            admin_id=admin_id
        )
    else:
        flash('Error deleting API key', 'error')

    return redirect(tenant_url_for('api_manager.index'))
