"""
API routes for the admin panel
"""
from flask import Blueprint, request, jsonify, session
from utils.api_auth import api_key_required
from utils.db_common import mongo_db
import logging
from datetime import datetime, timedelta, timezone

from utils.telegram_utils import kick_telegram_user, unban_telegram_user
from bson.objectid import ObjectId  # Used for MongoDB ID conversion

# Import the upload_access_codes function from dashboard_access_codes
from controllers.dashboard_access_codes import upload_access_codes as dashboard_upload_access_codes

logger = logging.getLogger(__name__)
api_bp = Blueprint('api', __name__, url_prefix='/api')

# Import necessary modules for the new implementation
import re

@api_bp.route('/access-codes/upload', methods=['POST'])
@api_key_required
def upload_access_codes():
    """
    API endpoint to upload access codes

    This endpoint uses the same function as the UI to ensure consistent behavior.
    It wraps the dashboard_upload_access_codes function and adapts it for API use.

    Accepts:
    - CSV or TXT file in the 'file' field
    - Optional 'expiration_days' parameter (default: 30)

    Returns:
    - JSON response with upload results
    """
    # Check if file is in the request
    if 'file' not in request.files:
        return jsonify({
            "success": False,
            "error": "No file provided",
            "message": "Please provide a file in the 'file' field"
        }), 400

    file = request.files['file']
    if file.filename == '':
        return jsonify({
            "success": False,
            "error": "Empty filename",
            "message": "Please select a file to upload"
        }), 400

    # Check file extension
    if not (file.filename.lower().endswith('.csv') or file.filename.lower().endswith('.txt')):
        return jsonify({
            "success": False,
            "error": "Invalid file type",
            "message": "Please upload a .csv or .txt file"
        }), 400

    # Create a response tracker to capture flash messages and results
    class ResponseTracker:
        def __init__(self):
            self.messages = []
            self.success = True
            self.added_count = 0
            self.updated_count = 0
            self.skipped_count = 0
            self.error_count = 0

        def add_message(self, message, category):
            self.messages.append({"message": message, "category": category})
            if category in ['error', 'danger']:
                self.success = False
            # Try to extract counts from success messages
            if category == 'success' or category == 'warning':
                try:
                    # Parse added count
                    added_match = re.search(r'Added (\d+)', message)
                    if added_match:
                        self.added_count = int(added_match.group(1))
                    # Parse updated count
                    updated_match = re.search(r'updated (\d+)', message)
                    if updated_match:
                        self.updated_count = int(updated_match.group(1))
                    # Parse skipped count
                    skipped_match = re.search(r'Skipped (\d+)', message)
                    if skipped_match:
                        self.skipped_count = int(skipped_match.group(1))
                except (ValueError, AttributeError) as e:
                    logger.warning(f"Error parsing counts from message: {e}")

    response_tracker = ResponseTracker()

    # Set up a mock session for the dashboard function
    # The dashboard function expects admin_id in the session
    original_session = session.copy() if 'admin_id' in session else {}
    session['admin_id'] = 'api'  # Set admin_id to 'api' for tracking
    session['api_request'] = True  # Flag to indicate this is an API request

    # Create a function to capture flash messages
    def capture_flash(message, category='message'):
        response_tracker.add_message(message, category)
        return None

    # Monkey patch the flash function
    from flask import flash as original_flash
    import sys
    sys.modules['flask'].flash = capture_flash

    try:
        # Call the dashboard function
        dashboard_upload_access_codes()

        # Prepare the response based on captured messages
        response_data = {
            "success": response_tracker.success,
            "messages": response_tracker.messages,
            "added": response_tracker.added_count,
            "updated": response_tracker.updated_count,
            "skipped": response_tracker.skipped_count
        }

        # Restore original functions and session
        sys.modules['flask'].flash = original_flash
        if original_session:
            session.update(original_session)
        else:
            session.pop('admin_id', None)

        return jsonify(response_data)

    except Exception as e:
        logger.error(f"Error calling dashboard_upload_access_codes: {e}", exc_info=True)
        # Restore original functions and session
        sys.modules['flask'].flash = original_flash
        if original_session:
            session.update(original_session)
        else:
            session.pop('admin_id', None)

        return jsonify({
            "success": False,
            "error": "Error processing upload",
            "message": str(e)
        }), 500




@api_bp.route('/access-codes/add', methods=['POST'])
@api_key_required
def add_access_code():
    """
    API endpoint to add a single access code

    Accepts:
    - JSON with 'code' field
    - Optional 'expiration_days' field (default: 30)

    Returns:
    - JSON response with result
    """
    try:
        # Get request data
        data = request.get_json()

        if not data:
            return jsonify({
                "success": False,
                "error": "No data provided",
                "message": "Please provide JSON data with 'code' field"
            }), 400

        code = data.get('code', '').strip()
        if not code:
            return jsonify({
                "success": False,
                "error": "No code provided",
                "message": "Please provide a valid access code"
            }), 400

        # Get expiration days (default: 30)
        try:
            expiration_days = int(data.get('expiration_days', 30))
            if expiration_days <= 0:
                expiration_days = 30  # Default to 30 days if invalid
        except (ValueError, TypeError):
            expiration_days = 30

        # Calculate expiration date
        expiration_date = datetime.now(timezone.utc) + timedelta(days=expiration_days)

        # Check if code already exists
        existing_code = mongo_db.db["access_code"].find_one({"code": code})
        if existing_code:
            return jsonify({
                "success": False,
                "error": "Code already exists",
                "message": f"Access code '{code}' already exists"
            }), 409

        # Add new code
        code_doc = {
            "code": code,
            "source": "api_added",
            "added_by": "api",
            "added_at": datetime.now(timezone.utc),
            "expiration_date": expiration_date
        }

        result = mongo_db.db["access_code"].insert_one(code_doc)

        if result.inserted_id:
            return jsonify({
                "success": True,
                "code": code,
                "expiration_days": expiration_days,
                "expiration_date": expiration_date.isoformat()
            })
        else:
            return jsonify({
                "success": False,
                "error": "Failed to add code",
                "message": "Database operation failed"
            }), 500

    except Exception as e:
        logger.error(f"Error adding access code via API: {e}", exc_info=True)
        return jsonify({
            "success": False,
            "error": "Server error",
            "message": str(e)
        }), 500

@api_bp.route('/telegram/kick/<int:user_id>', methods=['POST'])
@api_key_required
def kick_telegram_user_api(user_id):
    """
    API endpoint to kick (ban) a user from the Telegram channel

    Args:
        user_id (int): The Telegram user ID to kick

    Returns:
        JSON response with result
    """
    try:
        # Kick the user from the Telegram channel
        success, error = kick_telegram_user(user_id)

        if success:
            # Log activity
            mongo_db.add_activity_log(
                action="Kick User (API)",
                details=f"API kicked user ID {user_id} from Telegram channel",
                admin_id="api"
            )

            return jsonify({
                "success": True,
                "message": f"User {user_id} kicked successfully."
            })
        else:
            return jsonify({
                "success": False,
                "message": f"Failed to kick user {user_id}: {error}"
            }), 400
    except Exception as e:
        logger.error(f"Error kicking user {user_id} via API: {e}", exc_info=True)
        return jsonify({
            "success": False,
            "error": "Server error",
            "message": str(e)
        }), 500

@api_bp.route('/telegram/unban/<int:user_id>', methods=['POST'])
@api_key_required
def unban_telegram_user_api(user_id):
    """
    API endpoint to unban a user from the Telegram channel

    Args:
        user_id (int): The Telegram user ID to unban

    Returns:
        JSON response with result
    """
    try:
        # Unban the user from the Telegram channel
        success, error = unban_telegram_user(user_id)

        if success:
            # Log activity
            mongo_db.add_activity_log(
                action="Unban User (API)",
                details=f"API unbanned user ID {user_id} from Telegram channel",
                admin_id="api"
            )

            return jsonify({
                "success": True,
                "message": f"User {user_id} unbanned successfully."
            })
        else:
            # Provide specific error if user wasn't banned
            if error and "user is not banned" in error.lower():
                return jsonify({
                    "success": True,
                    "message": f"User {user_id} is already not banned."
                })
            else:
                return jsonify({
                    "success": False,
                    "message": f"Failed to unban user {user_id}: {error}"
                }), 400
    except Exception as e:
        logger.error(f"Error unbanning user {user_id} via API: {e}", exc_info=True)
        return jsonify({
            "success": False,
            "error": "Server error",
            "message": str(e)
        }), 500

@api_bp.route('/verification-requests/approve/<request_id>', methods=['POST'])
@api_key_required
def approve_verification_request_api(request_id):
    """
    API endpoint to approve a verification request

    Args:
        request_id (str): The verification request ID to approve

    Returns:
        JSON response with result
    """
    try:
        # Fetch verification request from MongoDB
        verification_requests_collection = mongo_db.db.verification_request
        request_details = verification_requests_collection.find_one({'request_id': request_id})

        if not request_details:
            return jsonify({
                'success': False,
                'error': 'Verification request not found'
            }), 404

        # Check if the access code exists in the access_code collection
        access_code = request_details.get('access_code')
        if access_code:
            access_code_exists = mongo_db.db["access_code"].find_one({"code": access_code})
            if not access_code_exists:
                error_message = f'Access code "{access_code}" not available. Please add the access code to the system before approving.'
                logger.warning(f"API approval failed: {error_message}")
                return jsonify({
                    'success': False,
                    'error': error_message
                }), 400

        # Update the request status to approved
        result = verification_requests_collection.update_one(
            {'request_id': request_id},
            {'$set': {
                'status': 'approved',
                'verified': True,
                'updated_at': datetime.now(timezone.utc)
            },
            '$push': {
                'verification_history': {
                    'status': 'approved',
                    'timestamp': datetime.now(timezone.utc),
                    'note': 'Approved via API',
                    'admin_id': 'api'
                }
            }}
        )

        if result.modified_count > 0:
            # Update master_user_data collection
            user_id = request_details.get('user_id')
            if user_id:
                # Update the user's verification status and subscription status in master_user_data
                # Also add subscribed_on field with current time when subscription is set to active
                current_time = datetime.now(timezone.utc)
                mongo_db.master_user_data.update_one(
                    {'user_id': user_id},
                    {'$set': {
                        'user_verify': True,
                        "user_verify_status": 'approved',
                        'subscription': 'active',
                        'broker_reg': True,
                        'user_status': 'active',  # Set the new user_status field to 'active'
                        'subscribed_on': current_time,  # Add subscribed_on field with current time
                        'last_updated': current_time
                    }}
                )

                # Send Telegram notification with invite link - same as in dashboard_requests.py
                telegram_message_sent = False
                telegram_error = None
                try:
                    # Import the necessary functions
                    from utils.telegram_utils import create_telegram_invite_link, send_telegram_message

                    # Create a one-time use invite link
                    invite_link, error = create_telegram_invite_link()

                    # Create the new message format
                    message = f"<b>Congratulations! Your profile is verified ✅</b>\n\n" \
                             f"You are an exclusive Member With us 🥷"

                    # Create buttons for the message
                    if invite_link:
                        # Create all the buttons
                        buttons = [
                            {
                                'text': 'Access Premium space 🏆',
                                'url': invite_link
                            },
                            {
                                'text': 'Explore my Benefits 🌍',
                                'callback_data': 'verify_explore_offerings'
                            },
                            {
                                'text': 'My Community Ranking 🏅',
                                'callback_data': 'community_ranking'
                            },
                            {
                                'text': 'Contact Withdrawals/Deposit Team 🔰',
                                'url': 'http://wa.me/+971585727623'
                            },
                            {
                                'text': 'Need More Support?',
                                'callback_data': 'submit_request_start'
                            }
                        ]

                        # Send message with multiple buttons
                        success, msg_error = send_telegram_message(user_id, message, inline_buttons=buttons)
                        telegram_message_sent = success
                        telegram_error = msg_error
                    else:
                        # If no invite link, still send the message without the first button
                        logger.error(f"Failed to create invite link: {error}")
                        buttons = [
                            {
                                'text': 'invite link creation failed, please contact admin using below button',
                                'callback_data': 'submit_request_start'
                            }
                        ]
                        success, msg_error = send_telegram_message(user_id, message, inline_buttons=buttons)
                        telegram_message_sent = success
                        telegram_error = msg_error

                    # Log the activity
                    mongo_db.add_activity_log(
                        action="Send Telegram Invite (API)",
                        details=f"System automatically sent Telegram invite link to user ID {user_id} after verification approval via API",
                        admin_id="api"
                    )
                except Exception as e:
                    logger.error(f"Failed to send Telegram notification with invite link via API: {e}")
                    telegram_error = str(e)

                # Log activity
                mongo_db.add_activity_log(
                    action="Verification Request Approved (API)",
                    details=f"API approved verification request ID {request_id} for user ID {user_id}",
                    admin_id="api"
                )

                return jsonify({
                    'success': True,
                    'message': 'Verification request approved successfully',
                    'user_id': user_id,
                    'telegram_message_sent': telegram_message_sent,
                    'telegram_error': telegram_error
                })
            else:
                return jsonify({
                    'success': True,
                    'message': 'Verification request approved, but no user ID found',
                    'request_id': request_id
                })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to update verification request'
            }), 500

    except Exception as e:
        logger.error(f"Error approving verification request {request_id} via API: {e}", exc_info=True)
        return jsonify({
            "success": False,
            "error": "Server error",
            "message": str(e)
        }), 500

@api_bp.route('/verification-requests/deny/<request_id>', methods=['POST'])
@api_key_required
def deny_verification_request_api(request_id):
    """
    API endpoint to deny a verification request

    Args:
        request_id (str): The verification request ID to deny

    Returns:
        JSON response with result
    """
    try:
        # Fetch verification request from MongoDB
        verification_requests_collection = mongo_db.db.verification_request
        request_details = verification_requests_collection.find_one({'request_id': request_id})

        if not request_details:
            return jsonify({
                'success': False,
                'error': 'Verification request not found'
            }), 404

        # Get the access code from the request details
        access_code = request_details.get('access_code', 'Unknown')

        # Update the request status to denied
        result = verification_requests_collection.update_one(
            {'request_id': request_id},
            {'$set': {
                'status': 'denied',
                'verified': True,  # This indicates the request has been processed, not that it was approved
                'updated_at': datetime.now(timezone.utc)
            },
            '$push': {
                'verification_history': {
                    'status': 'denied',
                    'timestamp': datetime.now(timezone.utc),
                    'note': 'Denied via API',
                    'admin_id': 'api'
                }
            }}
        )

        if result.modified_count > 0:
            # Update master_user_data collection
            user_id = request_details.get('user_id')
            if user_id:
                # Update the user's verification status in master_user_data
                # When a verification request is denied, user_verify should be false
                mongo_db.master_user_data.update_one(
                    {'user_id': user_id},
                    {'$set': {
                        'user_verify': False,  # Set to false when denied
                        'user_verify_status': 'denied',  # Add the new user_verify_status field
                        'subscription': 'inactive',  # Keep subscription inactive
                        'user_status': 'verification_denied',  # Set the user_status field to 'Verification denied'
                        'last_updated': datetime.now(timezone.utc)
                    }}
                )

                # Send Telegram notification with denial message - same as in dashboard_requests.py
                telegram_message_sent = False
                telegram_error = None
                try:
                    # Import the necessary function
                    from utils.telegram_utils import send_telegram_message

                    # Create the denial message
                    message = f"❌ <b>Verification Request Denied</b>\n\n" \
                             f"Your verification request for access code <b>{access_code}</b> has been denied.\n\n" \
                             f"If you believe this is an error or need assistance, please contact our support team.\n\n" \
                             f"<i>Thank you for your understanding.</i>"

                    # Create button for reverification
                    buttons = [
                        {
                            'text': 'Try Verification Again',
                            'callback_data': 'reg_start_verification'
                        }
                    ]

                    # Send message with reverification button
                    success, msg_error = send_telegram_message(user_id, message, inline_buttons=buttons)
                    telegram_message_sent = success
                    telegram_error = msg_error
                    logger.info(f"Sent denial notification to user {user_id} via API")

                    # Log the activity
                    mongo_db.add_activity_log(
                        action="Send Telegram Denial (API)",
                        details=f"System automatically sent Telegram denial message to user ID {user_id} after verification denial via API",
                        admin_id="api"
                    )
                except Exception as e:
                    logger.error(f"Failed to send Telegram denial notification via API: {e}")
                    telegram_error = str(e)

                # Log activity
                mongo_db.add_activity_log(
                    action="Verification Request Denied (API)",
                    details=f"API denied verification request ID {request_id} for user ID {user_id}",
                    admin_id="api"
                )

                return jsonify({
                    'success': True,
                    'message': 'Verification request denied successfully',
                    'user_id': user_id,
                    'telegram_message_sent': telegram_message_sent,
                    'telegram_error': telegram_error
                })
            else:
                return jsonify({
                    'success': True,
                    'message': 'Verification request denied, but no user ID found',
                    'request_id': request_id
                })
        else:
            return jsonify({
                'success': False,
                'error': 'Failed to update verification request'
            }), 500

    except Exception as e:
        logger.error(f"Error denying verification request {request_id} via API: {e}", exc_info=True)
        return jsonify({
            "success": False,
            "error": "Server error",
            "message": str(e)
        }), 500


@api_bp.route('/telegram/bot-info', methods=['GET'])
@api_key_required
def get_telegram_bot_info():
    """
    API endpoint to get Telegram bot token and username

    Returns:
        JSON response with bot token and username
    """
    try:
        # Get the active Telegram bot from the database
        bot_info = mongo_db.db["telegram_bots"].find_one({"is_active": True})

        if not bot_info:
            # If no active bot found, get the most recently updated one
            bot_info = mongo_db.db["telegram_bots"].find_one({}, sort=[("last_updated", -1)])

        if not bot_info:
            return jsonify({
                "success": False,
                "error": "No Telegram bot found",
                "message": "No Telegram bot configuration found in the database"
            }), 404

        # Extract the required information
        response_data = {
            "success": True,
            "bot_token": bot_info.get("token"),
            "bot_username": bot_info.get("telegram_username"),
            "name": bot_info.get("name"),
            "channel_id": bot_info.get("channel_id")
        }

        # Log the API access
        logger.info(f"Telegram bot info accessed via API")
        mongo_db.add_activity_log(
            action="Get Telegram Bot Info (API)",
            details=f"API retrieved Telegram bot information for {bot_info.get('name')}",
            admin_id="api"
        )

        return jsonify(response_data)

    except Exception as e:
        logger.error(f"Error retrieving Telegram bot info via API: {e}", exc_info=True)
        return jsonify({
            "success": False,
            "error": "Server error",
            "message": str(e)
        }), 500


@api_bp.route('/telegram/bot-info/<bot_id>', methods=['GET'])
@api_key_required
def get_telegram_bot_by_id(bot_id):
    """
    API endpoint to get Telegram bot token and username by bot ID

    Args:
        bot_id (str): The ID of the Telegram bot to retrieve

    Returns:
        JSON response with bot token and username
    """
    try:
        # Try to convert to ObjectId if it's a valid format
        try:
            if ObjectId.is_valid(bot_id):
                bot_id_query = ObjectId(bot_id)
                bot_info = mongo_db.db["telegram_bots"].find_one({"_id": bot_id_query})
            else:
                # If not a valid ObjectId, try as telegram_id (NumberLong)
                try:
                    telegram_id = int(bot_id)
                    bot_info = mongo_db.db["telegram_bots"].find_one({"telegram_id": telegram_id})
                except ValueError:
                    # If not a number, try as a string match on username
                    bot_info = mongo_db.db["telegram_bots"].find_one({"telegram_username": bot_id})
        except Exception as e:
            logger.error(f"Error parsing bot_id {bot_id}: {e}")
            bot_info = None

        if not bot_info:
            return jsonify({
                "success": False,
                "error": "Bot not found",
                "message": f"No Telegram bot found with ID {bot_id}"
            }), 404

        # Extract the required information
        response_data = {
            "success": True,
            "bot_token": bot_info.get("token"),
            "bot_username": bot_info.get("telegram_username"),
            "name": bot_info.get("name"),
            "channel_id": bot_info.get("channel_id"),
            "is_active": bot_info.get("is_active", False)
        }

        # Log the API access
        logger.info(f"Telegram bot info accessed via API for bot ID {bot_id}")
        mongo_db.add_activity_log(
            action="Get Telegram Bot Info (API)",
            details=f"API retrieved Telegram bot information for {bot_info.get('name')} (ID: {bot_id})",
            admin_id="api"
        )

        return jsonify(response_data)

    except Exception as e:
        logger.error(f"Error retrieving Telegram bot info for ID {bot_id} via API: {e}", exc_info=True)
        return jsonify({
            "success": False,
            "error": "Server error",
            "message": str(e)
        }), 500


@api_bp.route('/telegram/bots', methods=['GET'])
@api_key_required
def list_telegram_bots():
    """
    API endpoint to list all Telegram bots

    Returns:
        JSON response with list of bots
    """
    try:
        # Get all Telegram bots from the database
        bots = list(mongo_db.db["telegram_bots"].find({}, {
            "_id": 1,
            "name": 1,
            "telegram_username": 1,
            "telegram_id": 1,
            "is_active": 1,
            "channel_id": 1,
            "last_updated": 1
        }))

        # Format the response
        formatted_bots = []
        for bot in bots:
            # Convert ObjectId to string for JSON serialization
            bot_id = str(bot["_id"])

            # Format dates
            last_updated = bot.get("last_updated")
            if last_updated and isinstance(last_updated, datetime):
                last_updated = last_updated.isoformat()

            formatted_bots.append({
                "id": bot_id,
                "name": bot.get("name"),
                "username": bot.get("telegram_username"),
                "telegram_id": bot.get("telegram_id"),
                "is_active": bot.get("is_active", False),
                "channel_id": bot.get("channel_id"),
                "last_updated": last_updated
            })

        # Log the API access
        logger.info(f"Telegram bots list accessed via API")
        mongo_db.add_activity_log(
            action="List Telegram Bots (API)",
            details=f"API retrieved list of {len(formatted_bots)} Telegram bots",
            admin_id="api"
        )

        return jsonify({
            "success": True,
            "bots": formatted_bots,
            "count": len(formatted_bots)
        })

    except Exception as e:
        logger.error(f"Error retrieving Telegram bots list via API: {e}", exc_info=True)
        return jsonify({
            "success": False,
            "error": "Server error",
            "message": str(e)
        }), 500