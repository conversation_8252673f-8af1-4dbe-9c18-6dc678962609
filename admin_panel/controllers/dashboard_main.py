# admin_panel/controllers/dashboard_main.py
from flask import render_template, redirect, session, request, jsonify, flash, Response
from utils.db_common import mongo_db
from utils.url_helpers import tenant_url_for
import logging
from datetime import datetime, timedelta, timezone
import csv
import io

logger = logging.getLogger(__name__)
# Using mongo_db from utils.db_common

def home():
    # Get pagination parameters for active subscriptions
    page = request.args.get('page', 1, type=int)
    per_page = 6  # Show 6 users per page
    filter_query = request.args.get('filter', '')

    # Build the query for active subscriptions
    query = {"user_status": "active"}

    # Add text search if filter query provided
    if filter_query:
        # Create text search conditions
        text_conditions = [
            {"name": {"$regex": filter_query, "$options": "i"}},
            {"email": {"$regex": filter_query, "$options": "i"}},
            {"whatsapp": {"$regex": filter_query, "$options": "i"}},
            {"access_code": {"$regex": filter_query, "$options": "i"}}
        ]

        # Add user_id condition if query is numeric
        if filter_query.isdigit():
            text_conditions.append({"user_id": int(filter_query)})

        query["$or"] = text_conditions

    # Calculate skip value for pagination
    skip = (page - 1) * per_page

    # Get total count for pagination
    total_active_users = mongo_db.master_user_data.count_documents(query)
    total_pages = (total_active_users + per_page - 1) // per_page  # Ceiling division

    # Get active subscriptions with pagination
    subscriptions = list(mongo_db.master_user_data.find(query).sort("last_updated", -1).skip(skip).limit(per_page))

    # Calculate pagination metadata
    has_next = page < total_pages
    has_prev = page > 1
    start_item = skip + 1 if subscriptions else 0
    end_item = min(skip + per_page, total_active_users)

    # No need to enhance subscriptions as we're using fields directly from master_user_data

    # Get recent activity logs
    recent_logs = mongo_db.get_recent_logs(10)

    # Calculate subscription statistics
    total_subscriptions = mongo_db.master_user_data.count_documents({})
    active_subscriptions = mongo_db.master_user_data.count_documents({"user_status": "active"})

    # Get total access codes count
    total_access_codes = mongo_db.db["access_code"].count_documents({})

    # Calculate subscriptions expiring soon (within 30 days)
    now = datetime.now(timezone.utc)
    thirty_days_later = now + timedelta(days=30)

    # Get all active subscriptions
    active_subs = list(mongo_db.master_user_data.find({"user_status": "active"}))

    # Count subscriptions expiring soon based on access code expiration date
    expiring_soon = 0
    for sub in active_subs:
        if 'access_code' in sub:
            # Get the access code details
            access_code = sub['access_code']
            code_details = mongo_db.db["access_code"].find_one({"code": access_code})

            # Check if the access code has an expiration date
            if code_details and 'expiration_date' in code_details:
                # If expiration date is within 30 days, count it
                # Make sure both datetimes are timezone-aware for comparison
                expiration_date = code_details['expiration_date']
                if expiration_date.tzinfo is None:
                    expiration_date = expiration_date.replace(tzinfo=timezone.utc)

                # Extract date components only for comparison
                expiration_date_only = expiration_date.date()
                thirty_days_later_only = thirty_days_later.date()

                # If expiration date is within 30 days, count it
                if expiration_date_only <= thirty_days_later_only:
                    expiring_soon += 1
            else:
                # Fallback to registration time if no expiration date
                if sub.get('registration_time'):
                    # Make sure both datetimes are timezone-aware for comparison
                    registration_time = sub['registration_time']
                    if registration_time.tzinfo is None:
                        registration_time = registration_time.replace(tzinfo=timezone.utc)

                    # Extract date components only for comparison
                    registration_date_only = registration_time.date()
                    thirty_days_later_only = thirty_days_later.date()

                    # If registration date is within 30 days, count it
                    if registration_date_only <= thirty_days_later_only:
                        expiring_soon += 1

    return render_template(
        'dashboard.html',
        subscriptions=subscriptions,
        recent_logs=recent_logs,
        total_subscriptions=total_subscriptions,
        active_subscriptions=active_subscriptions,
        expiring_soon=expiring_soon,
        total_access_codes=total_access_codes,
        pagination={
            'page': page,
            'per_page': per_page,
            'total': total_active_users,
            'total_pages': total_pages,
            'has_next': has_next,
            'has_prev': has_prev
        },
        start_item=start_item,
        end_item=end_item,
        filter_query=filter_query
    )

def admin_profile():
    admin_id = session['admin_id'] # Assumes login_required decorator ensures this exists
    admin_activities = list(mongo_db.db.activity_logs.find(
        {"admin_id": admin_id}
    ).sort("timestamp", -1).limit(50))

    # Fetch admin details for the profile page
    admin_user = mongo_db.get_admin_by_id(admin_id)

    # Format the last login time if available
    if admin_user and 'last_login' in admin_user and admin_user['last_login']:
        if isinstance(admin_user['last_login'], datetime):
            admin_user['last_login'] = admin_user['last_login'].strftime('%Y-%m-%d %H:%M:%S')

    return render_template('admin_profile.html', admin=admin_user, admin_activities=admin_activities)

def change_password():
    from werkzeug.security import check_password_hash, generate_password_hash

    # Get current password, new password, confirmation from request.form
    current_password = request.form.get('current_password')
    new_password = request.form.get('new_password')
    confirm_password = request.form.get('confirm_password')
    admin_id = session.get('admin_id')

    # Validate input
    if not all([current_password, new_password, confirm_password]):
        flash('All password fields are required.', 'error')
        return redirect(tenant_url_for('dashboard.admin_profile_route'))

    # Check if new password and confirmation match
    if new_password != confirm_password:
        flash('New password and confirmation do not match.', 'error')
        return redirect(tenant_url_for('dashboard.admin_profile_route'))

    # Check password complexity (at least 8 characters)
    if len(new_password) < 8:
        flash('New password must be at least 8 characters long.', 'error')
        return redirect(tenant_url_for('dashboard.admin_profile_route'))

    # Get admin user from DB
    admin = mongo_db.get_admin_by_id(admin_id)
    if not admin:
        flash('Admin user not found.', 'error')
        return redirect(tenant_url_for('dashboard.admin_profile_route'))

    # Verify current password
    if not check_password_hash(admin['password_hash'], current_password):
        # Log failed attempt
        mongo_db.add_activity_log(
            action="Failed Password Change",
            details="Password change failed: incorrect current password",
            admin_id=admin_id
        )
        flash('Current password is incorrect.', 'error')
        return redirect(tenant_url_for('dashboard.admin_profile_route'))

    # Hash the new password
    new_password_hash = generate_password_hash(new_password)

    # Update the admin user's password hash in the DB
    success = mongo_db.update_admin_password(admin_id, new_password_hash)

    if success:
        # Log successful password change
        mongo_db.add_activity_log(
            action="Password Changed",
            details="Admin password was successfully changed",
            admin_id=admin_id
        )
        flash('Your password has been updated successfully.', 'success')
    else:
        # Log failed update
        mongo_db.add_activity_log(
            action="Failed Password Change",
            details="Password change failed: database update error",
            admin_id=admin_id
        )
        flash('An error occurred while updating your password. Please try again.', 'error')

    return redirect(tenant_url_for('dashboard.admin_profile_route'))

def users():
    """Display all users from master_user_data collection"""
    # Get pagination parameters
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 10, type=int)
    sort_field = request.args.get('sort', 'last_updated')

    # If sort_field is subscription_time (which is no longer displayed), use last_updated instead
    if sort_field == 'subscription_time':
        sort_field = 'last_updated'

    sort_order = request.args.get('order', 'desc')
    filter_query = request.args.get('filter', '')
    filter_status = request.args.get('status', 'all')

    # Build the query
    query = {}

    # Add status filter if not 'all'
    if filter_status != 'all':
        query['user_status'] = filter_status

    # Add text search if filter query provided
    if filter_query:
        # Create text search conditions
        text_conditions = [
            {"name": {"$regex": filter_query, "$options": "i"}},
            {"email": {"$regex": filter_query, "$options": "i"}},
            {"whatsapp": {"$regex": filter_query, "$options": "i"}},
            {"access_code": {"$regex": filter_query, "$options": "i"}}
        ]

        # Add user_id condition if query is numeric
        if filter_query.isdigit():
            text_conditions.append({"user_id": int(filter_query)})

        query["$or"] = text_conditions

    # Calculate skip value for pagination
    skip = (page - 1) * per_page

    # Determine sort direction
    sort_direction = -1 if sort_order == 'desc' else 1

    # Get total count for pagination
    total_users = mongo_db.db["master_user_data"].count_documents(query)
    total_pages = (total_users + per_page - 1) // per_page  # Ceiling division

    # Get users with pagination and sorting
    # If no specific sort is requested, prioritize users that are not (status="new" and broker_reg=false)
    if sort_field == 'last_updated' and sort_order == 'desc' and not filter_query and filter_status == 'all':
        # Use aggregation to sort users with status="new" and broker_reg=false to the bottom
        pipeline = [
            {"$match": query},
            {"$addFields": {
                "_sort_order": {
                    "$cond": [
                        {"$and": [
                            {"$eq": ["$user_status", "new"]},
                            {"$eq": ["$broker_reg", False]}
                        ]},
                        1,  # These users go to the bottom
                        0   # All other users go to the top
                    ]
                }
            }},
            {"$sort": {"_sort_order": 1, "last_updated": -1}},  # First by _sort_order, then by last_updated desc
            {"$skip": skip},
            {"$limit": per_page},
            {"$project": {"_sort_order": 0}}  # Remove the temporary sort field
        ]
        users_data = list(mongo_db.db["master_user_data"].aggregate(pipeline))
    else:
        # Use regular sorting for other cases
        users_data = list(mongo_db.db["master_user_data"].find(query)
                         .sort(sort_field, sort_direction)
                         .skip(skip)
                         .limit(per_page))

    # Calculate pagination metadata
    has_next = page < total_pages
    has_prev = page > 1
    start_item = skip + 1 if users_data else 0
    end_item = min(skip + per_page, total_users)

    # Get all possible status values for filter dropdown
    status_values = mongo_db.db["master_user_data"].distinct("user_status")

    # Get current time for display purposes
    now = datetime.now(timezone.utc)

    return render_template(
        'users.html',
        users=users_data,
        pagination={
            'page': page,
            'per_page': per_page,
            'total': total_users,
            'total_pages': total_pages,
            'has_next': has_next,
            'has_prev': has_prev,
            'allowed_per_page': [10, 25, 50, 100]
        },
        start_item=start_item,
        end_item=end_item,
        sort_field=sort_field,
        sort_order=sort_order,
        filter_status=filter_status,
        filter_query=filter_query,
        status_values=status_values,
        now=now
    )

def export_users_csv():
    """Export users data to CSV"""
    # Get filter parameters
    filter_query = request.args.get('filter', '')
    filter_status = request.args.get('status', 'all')

    # Build the query
    query = {}

    # Add status filter if not 'all'
    if filter_status != 'all':
        query['status'] = filter_status

    # Add text search if filter query provided
    if filter_query:
        # Create text search conditions
        text_conditions = [
            {"name": {"$regex": filter_query, "$options": "i"}},
            {"email": {"$regex": filter_query, "$options": "i"}},
            {"whatsapp": {"$regex": filter_query, "$options": "i"}},
            {"access_code": {"$regex": filter_query, "$options": "i"}}
        ]

        # Add user_id condition if query is numeric
        if filter_query.isdigit():
            text_conditions.append({"user_id": int(filter_query)})

        query["$or"] = text_conditions

    # Get all users matching the query
    # Prioritize users that are not (status="new" and broker_reg=false)
    if not filter_query and filter_status == 'all':
        # Use aggregation to sort users with status="new" and broker_reg=false to the bottom
        pipeline = [
            {"$match": query},
            {"$addFields": {
                "_sort_order": {
                    "$cond": [
                        {"$and": [
                            {"$eq": ["$user_status", "new"]},
                            {"$eq": ["$broker_reg", False]}
                        ]},
                        1,  # These users go to the bottom
                        0   # All other users go to the top
                    ]
                }
            }},
            {"$sort": {"_sort_order": 1, "last_updated": -1}},  # First by _sort_order, then by last_updated desc
            {"$project": {"_sort_order": 0}}  # Remove the temporary sort field
        ]
        users_data = list(mongo_db.db["master_user_data"].aggregate(pipeline))
    else:
        # Use regular sorting for other cases
        users_data = list(mongo_db.db["master_user_data"].find(query).sort("last_updated", -1))

    # Create a CSV in memory
    output = io.StringIO()
    fieldnames = ['user_id', 'name', 'email', 'whatsapp', 'access_code', 'status', 'subscription_time', 'last_updated', 'trading_experience']
    writer = csv.DictWriter(output, fieldnames=fieldnames, extrasaction='ignore')
    writer.writeheader()

    # Write user data to CSV
    for user in users_data:
        # Convert datetime objects to strings
        if 'subscription_time' in user and isinstance(user['subscription_time'], datetime):
            user['subscription_time'] = user['subscription_time'].strftime('%Y-%m-%d %H:%M:%S')
        if 'last_updated' in user and isinstance(user['last_updated'], datetime):
            user['last_updated'] = user['last_updated'].strftime('%Y-%m-%d %H:%M:%S')

        writer.writerow(user)

    # Create response with CSV file
    timestamp = datetime.now(timezone.utc).strftime('%Y%m%d_%H%M%S')
    response = Response(output.getvalue(), mimetype='text/csv')
    response.headers["Content-Disposition"] = f"attachment; filename=users_export_{timestamp}.csv"

    # Log the export activity
    admin_id = session.get('admin_id')
    if admin_id:
        admin = mongo_db.get_admin_by_id(admin_id)
        admin_username = admin.get('username', 'Unknown') if admin else 'Unknown'
        mongo_db.add_activity_log(
            action="Export Users CSV",
            details=f"Admin {admin_username} exported users data to CSV",
            admin_id=admin_id
        )

    return response

def search():
    query = request.args.get('q', '').strip()
    if not query:
        return jsonify({'results': []})

    try:
        # Create base query conditions for master_user_data
        base_conditions = [
            {"access_code": {"$regex": query, "$options": "i"}},
            {"name": {"$regex": query, "$options": "i"}},
            {"whatsapp": {"$regex": query, "$options": "i"}},
            {"email": {"$regex": query, "$options": "i"}}
        ]

        # Add user_id condition if query is numeric
        if query.isdigit():
            base_conditions.append({"user_id": int(query)})

        # Search in master_user_data collection
        results = mongo_db.db["master_user_data"].aggregate([
            {
                "$match": {
                    "$or": base_conditions
                }
            },
            {
                "$project": {
                    "_id": 0,
                    "access_code": 1,
                    "user_id": 1,
                    "status": "$user_status",  # Use the new user_status field directly
                    "subscription_time": "$registration_time",
                    "name": 1,
                    "email": 1,
                    "whatsapp": 1,
                    "user_status": 1,  # Include the new field directly
                    "broker_reg": 1,
                    "user_verify": 1
                }
            },
            {
                "$limit": 10 # Limit results for performance
            }
        ])

        # Convert cursor to list and handle date serialization
        results_list = []
        for doc in results:
            if doc.get('subscription_time') and isinstance(doc['subscription_time'], datetime):
                doc['subscription_time'] = doc['subscription_time'].isoformat() + 'Z' # ISO format with Z for UTC
            results_list.append(doc)

        return jsonify({'results': results_list})

    except Exception as e:
        logger.error(f"Error during search for query '{query}': {e}", exc_info=True)
        return jsonify({'results': [], 'error': 'An internal error occurred during search.'}), 500