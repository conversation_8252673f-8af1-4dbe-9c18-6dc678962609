<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% if tenant %}{{ tenant | title }} - {% endif %}Admin <PERSON></title>
    <!-- Favicon -->
    <link rel="apple-touch-icon" sizes="180x180" href="{{ tenant_url_for('static', filename='favicon/apple-touch-icon.png') }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ tenant_url_for('static', filename='favicon/favicon-32x32.png') }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ tenant_url_for('static', filename='favicon/favicon-16x16.png') }}">
    <link rel="manifest" href="{{ tenant_url_for('static', filename='site.webmanifest') }}">
    <link rel="shortcut icon" href="{{ tenant_url_for('static', filename='favicon/favicon.ico') }}">
    <meta name="theme-color" content="#4f46e5">
    <!-- Tailwind CSS from CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#4f46e5',
                        'primary-dark': '#4338ca',
                        'primary-light': '#eef2ff',
                        'secondary': '#0ea5e9',
                        'dark-blue': '#1e293b',
                        'light-blue': '#f0f9ff',
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif'],
                    },
                    boxShadow: {
                        'card': '0 10px 25px -5px rgba(0, 0, 0, 0.1)',
                        'input': '0 2px 4px rgba(0, 0, 0, 0.05)',
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f9fafb;
        }

        .bg-gradient {
            background: linear-gradient(135deg, #4f46e5 0%, #0ea5e9 100%);
        }

        .form-input:focus {
            border-color: #4f46e5;
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.15);
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(8px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .animate-fadeIn {
            animation: fadeIn 0.4s ease-out forwards;
        }

        .login-card {
            animation: fadeIn 0.4s ease-out forwards;
        }

        .flash-message {
            animation: fadeIn 0.3s ease-out forwards;
        }

        .login-container {
            background-color: #f9fafb;
        }

        /* Banner background button */
        .banner-bg-button {
            position: relative;
            overflow: hidden;
        }

        .banner-bg-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.3);
            z-index: 1;
        }

        .banner-bg-button:hover::before {
            background-color: rgba(0, 0, 0, 0.4);
        }
    </style>
</head>
<body class="login-container min-h-screen flex items-center justify-center p-4 md:p-6">
    <div class="w-full max-w-[1000px] flex flex-col md:flex-row items-stretch rounded-2xl overflow-hidden shadow-xl bg-white">
        <!-- Left Panel - Banner Image Background -->
        <div class="w-full md:w-5/12 p-8 md:p-10 flex items-center justify-center relative overflow-hidden" style="background-image: url('{{ tenant_url_for('static', filename='images/banner.webp') }}'); background-size: cover; background-position: center;">
            <!-- Dark overlay for better text readability -->
            <div class="absolute top-0 left-0 w-full h-full bg-black opacity-50"></div>

            <!-- Decorative pattern overlay -->
            <div class="absolute top-0 left-0 w-full h-full opacity-10">
                <svg width="100%" height="100%" viewBox="0 0 800 800" xmlns="http://www.w3.org/2000/svg">
                    <defs>
                        <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
                            <path d="M 40 0 L 0 0 0 40" fill="none" stroke="white" stroke-width="1" opacity="0.3"/>
                        </pattern>
                    </defs>
                    <rect width="100%" height="100%" fill="url(#grid)" />
                </svg>
            </div>

            <!-- Decorative circles -->
            <div class="absolute -top-20 -left-20 w-40 h-40 rounded-full bg-white opacity-5"></div>
            <div class="absolute -bottom-20 -right-20 w-60 h-60 rounded-full bg-white opacity-5"></div>

            <!-- Content -->
            <div class="relative z-10 text-white max-w-md mx-auto">
                <div class="flex items-center mb-8">
                    <div class="h-12 w-12 rounded-xl bg-white bg-opacity-10 flex items-center justify-center mr-4">
                        <i class="fas fa-chart-line text-xl text-white"></i>
                    </div>
                    <h1 class="text-2xl font-bold">Admin Dashboard</h1>
                </div>

                <h2 class="text-3xl font-bold mb-4 leading-tight">Welcome to your control center</h2>
                <p class="text-base opacity-90 leading-relaxed mb-8">Securely access your dashboard to manage users, monitor activity, and control access codes.</p>

                <div class="space-y-4">
                    <div class="flex items-center">
                        <div class="h-8 w-8 rounded-lg bg-white bg-opacity-10 flex items-center justify-center mr-3">
                            <i class="fas fa-shield-alt text-sm"></i>
                        </div>
                        <div class="text-left">
                            <h3 class="font-medium text-sm">Enterprise-grade security</h3>
                            <p class="text-xs text-white text-opacity-70">Advanced protection for your data</p>
                        </div>
                    </div>

                    <div class="flex items-center">
                        <div class="h-8 w-8 rounded-lg bg-white bg-opacity-10 flex items-center justify-center mr-3">
                            <i class="fas fa-tachometer-alt text-sm"></i>
                        </div>
                        <div class="text-left">
                            <h3 class="font-medium text-sm">Real-time analytics</h3>
                            <p class="text-xs text-white text-opacity-70">Monitor activity as it happens</p>
                        </div>
                    </div>

                    <div class="flex items-center">
                        <div class="h-8 w-8 rounded-lg bg-white bg-opacity-10 flex items-center justify-center mr-3">
                            <i class="fas fa-users text-sm"></i>
                        </div>
                        <div class="text-left">
                            <h3 class="font-medium text-sm">User management</h3>
                            <p class="text-xs text-white text-opacity-70">Complete control over access</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Panel - Login Form -->
        <div class="w-full md:w-7/12 p-8 md:p-12 flex items-center justify-center bg-white">
            <div class="w-full max-w-md">
                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        <div class="mb-6">
                            {% for category, message in messages %}
                                <div class="p-3 mb-3 rounded-lg {% if category == 'error' %}bg-red-50 text-red-700 border border-red-100{% elif category == 'success' %}bg-green-50 text-green-700 border border-green-100{% else %}bg-indigo-50 text-indigo-700 border border-indigo-100{% endif %} flash-message flex items-center justify-between">
                                    <div class="flex items-center">
                                        <i class="{% if category == 'error' %}fas fa-exclamation-circle text-red-500{% elif category == 'success' %}fas fa-check-circle text-green-500{% else %}fas fa-info-circle text-indigo-500{% endif %} mr-2 text-sm"></i>
                                        <span class="text-sm">{{ message }}</span>
                                    </div>
                                    <button class="text-gray-400 hover:text-gray-600" onclick="this.parentElement.style.display='none'">
                                        <i class="fas fa-times text-xs"></i>
                                    </button>
                                </div>
                            {% endfor %}
                        </div>
                    {% endif %}
                {% endwith %}

                <div class="mb-8">
                    <h2 class="text-2xl font-bold text-gray-900 mb-2">Sign in{% if tenant %} to {{ tenant | title }}{% endif %}</h2>
                    <p class="text-gray-600">Enter your credentials to access the dashboard</p>
                    {% if tenant %}
                    <div class="mt-2 text-xs text-indigo-600 bg-indigo-50 px-3 py-1 rounded-full inline-block">
                        <i class="fas fa-building mr-1"></i> {{ tenant | title }} Tenant
                    </div>
                    {% endif %}
                </div>

                <form method="POST" class="space-y-6">
                    {% if tenant %}
                    <!-- Hidden input to preserve tenant information -->
                    <input type="hidden" name="tenant" value="{{ tenant }}">
                    {% endif %}

                    <div>
                        <label for="username" class="block text-sm font-medium text-gray-700 mb-1.5">Username</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3.5 flex items-center pointer-events-none">
                                <i class="fas fa-user text-gray-400 text-sm"></i>
                            </div>
                            <input type="text" id="username" name="username" placeholder="Enter your username" required
                                   class="form-input pl-10 w-full px-4 py-3 border border-gray-300 rounded-lg shadow-input focus:outline-none transition-all duration-200 text-gray-800">
                        </div>
                    </div>

                    <div>
                        <div class="flex items-center justify-between mb-1.5">
                            <label for="password" class="block text-sm font-medium text-gray-700">Password</label>
                            <a href="#" class="text-xs text-primary hover:text-primary-dark transition-colors duration-200 font-medium">Forgot password?</a>
                        </div>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3.5 flex items-center pointer-events-none">
                                <i class="fas fa-lock text-gray-400 text-sm"></i>
                            </div>
                            <input type="password" id="password" name="password" placeholder="••••••••" required
                                   class="form-input pl-10 w-full px-4 py-3 border border-gray-300 rounded-lg shadow-input focus:outline-none transition-all duration-200 text-gray-800">
                            <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                                <button type="button" id="togglePassword" class="text-gray-400 hover:text-gray-600 focus:outline-none">
                                    <i class="fas fa-eye text-sm"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="flex items-center">
                        <input type="checkbox" id="remember" name="remember" class="h-4 w-4 text-primary focus:ring-primary-light border-gray-300 rounded transition-colors duration-200">
                        <label for="remember" class="ml-2 block text-sm text-gray-600">Remember me for 30 days</label>
                    </div>

                    <button type="submit" class="w-full relative overflow-hidden text-white py-3 px-4 rounded-lg font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-all duration-200 flex items-center justify-center shadow-md group">
                        <!-- Banner image background with zoom effect -->
                        <div class="absolute inset-0 transform transition-transform duration-700 group-hover:scale-110" style="background-image: url('{{ tenant_url_for('static', filename='images/banner.webp') }}'); background-size: cover; background-position: center;"></div>
                        <!-- Dark overlay -->
                        <div class="absolute inset-0 bg-black opacity-30 transition-opacity duration-200"></div>
                        <!-- Hover overlay -->
                        <div class="absolute inset-0 bg-primary opacity-0 group-hover:opacity-20 transition-opacity duration-200"></div>
                        <!-- Button text -->
                        <span class="relative z-10">Sign In <i class="fas fa-arrow-right ml-2"></i></span>
                    </button>
                </form>

                <div class="mt-8 text-center text-gray-500 text-xs">
                    <p>&copy; {{ now.year }} Admin Dashboard. All rights reserved.</p>
                </div>

                <!-- Debug Information (only visible in development) -->
                {% if false %}
                <div class="mt-6 p-4 bg-gray-100 rounded-lg text-xs text-left">
                    <h3 class="font-bold mb-2">Debug Information</h3>
                    <div class="mb-2">
                        <strong>Tenant:</strong> {{ tenant or 'None' }}
                    </div>
                    <div class="mb-2">
                        <strong>Session Cookie Name:</strong> {{ 'session_' + tenant if tenant else 'session' }}
                    </div>
                    <div class="mb-2">
                        <strong>Session Data:</strong>
                        <pre class="bg-gray-200 p-2 rounded mt-1 overflow-auto max-h-20">{{ session_data | tojson(indent=2) }}</pre>
                    </div>
                    <div>
                        <strong>Cookies:</strong>
                        <pre class="bg-gray-200 p-2 rounded mt-1 overflow-auto max-h-20">{{ cookies | tojson(indent=2) }}</pre>
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <script>
        // Toggle password visibility
        document.getElementById('togglePassword').addEventListener('click', function() {
            const passwordInput = document.getElementById('password');
            const icon = this.querySelector('i');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });

        // Add animation to form elements and auto-hide flash messages after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            // Add animation to form elements
            const formElements = document.querySelectorAll('form > div');
            formElements.forEach(function(element, index) {
                element.style.opacity = '0';
                element.style.transform = 'translateY(10px)';
                setTimeout(function() {
                    element.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                }, 100 + (index * 100));
            });

            // Auto-hide flash messages
            const flashMessages = document.querySelectorAll('.flash-message');
            flashMessages.forEach(function(message) {
                setTimeout(function() {
                    message.style.opacity = '0.8';
                    setTimeout(function() {
                        message.style.opacity = '0';
                        setTimeout(function() {
                            message.style.display = 'none';
                        }, 300);
                    }, 4700);
                }, 300);
            });

            // Focus on username field
            setTimeout(function() {
                document.getElementById('username').focus();
            }, 500);
        });
    </script>
</body>
</html>