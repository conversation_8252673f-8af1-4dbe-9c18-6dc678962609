{% extends 'base.html' %}

{% block title %}API Key Management{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold text-gray-800">API Key Management</h1>
        <div class="flex space-x-3">
            <a href="{{ tenant_url_for('api_manager.docs') }}" class="bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 flex items-center">
                <i class="fas fa-book mr-2"></i> API Documentation
            </a>
            <button id="createKeyBtn" class="bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 flex items-center">
                <i class="fas fa-plus mr-2"></i> Create API Key
            </button>
        </div>
    </div>

    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-8">
        <div class="p-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">API Documentation</h2>
            <p class="mb-4">Use these API keys to access the API endpoints programmatically. Each key should be included in the <code>X-API-Key</code> header of your requests.</p>

            <div class="bg-blue-50 border-l-4 border-blue-500 p-4 mb-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm text-blue-700">
                            <strong>Important:</strong> All API endpoints require a tenant prefix in the URL. Your current tenant is <code>{{ tenant }}</code>.
                        </p>
                    </div>
                </div>
            </div>

            <div class="bg-gray-50 p-4 rounded-lg mb-4">
                <h3 class="text-lg font-medium text-gray-800 mb-2">Upload Access Codes</h3>
                <p class="mb-2">Endpoint: <code>POST /{{ tenant }}/api/access-codes/upload</code></p>
                <p class="mb-2">Upload a CSV or TXT file containing access codes.</p>
                <pre class="bg-gray-100 p-3 rounded-lg text-sm overflow-x-auto"><code>curl -X POST http://your-server/{{ tenant }}/api/access-codes/upload \
  -H "X-API-Key: your_api_key" \
  -F "file=@codes.csv" \
  -F "expiration_days=30"</code></pre>
            </div>

            <div class="bg-gray-50 p-4 rounded-lg">
                <h3 class="text-lg font-medium text-gray-800 mb-2">Add Single Access Code</h3>
                <p class="mb-2">Endpoint: <code>POST /{{ tenant }}/api/access-codes/add</code></p>
                <p class="mb-2">Add a single access code.</p>
                <pre class="bg-gray-100 p-3 rounded-lg text-sm overflow-x-auto"><code>curl -X POST http://your-server/{{ tenant }}/api/access-codes/add \
  -H "X-API-Key: your_api_key" \
  -H "Content-Type: application/json" \
  -d '{"code": "ABC123", "expiration_days": 30}'</code></pre>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-xl shadow-md overflow-hidden">
        <div class="p-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">Your API Keys</h2>

            {% if api_keys %}
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead>
                        <tr>
                            <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                            <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">API Key</th>
                            <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created By</th>
                            <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created At</th>
                            <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Used</th>
                            <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for key in api_keys %}
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ key.get('name', 'Unnamed Key') }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                <div class="flex items-center">
                                    <span class="truncate w-32">{{ key.get('key_prefix', '') }}•••••••••••</span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ admins.get(key.get('created_by', ''), 'Unknown') }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ key.get('created_at').strftime('%Y-%m-%d %H:%M') if key.get('created_at') else 'N/A' }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ key.get('last_used').strftime('%Y-%m-%d %H:%M') if key.get('last_used') else 'Never' }}</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                {% if key.get('status') == 'active' %}
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Active</span>
                                {% else %}
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">{{ key.get('status', 'Inactive') }}</span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <div class="flex space-x-2">
                                    {% if key.get('status') == 'active' %}
                                    <form action="{{ tenant_url_for('api_manager.revoke_key', api_key_id=key.get('_id')) }}" method="POST" class="inline">
                                        <button type="submit" class="text-yellow-600 hover:text-yellow-900" title="Revoke">
                                            <i class="fas fa-ban"></i>
                                        </button>
                                    </form>
                                    {% else %}
                                    <form action="{{ tenant_url_for('api_manager.activate_key', api_key_id=key.get('_id')) }}" method="POST" class="inline">
                                        <button type="submit" class="text-green-600 hover:text-green-900" title="Activate">
                                            <i class="fas fa-check"></i>
                                        </button>
                                    </form>
                                    {% endif %}
                                    <form action="{{ tenant_url_for('api_manager.delete_key', api_key_id=key.get('_id')) }}" method="POST" class="inline" onsubmit="return confirm('Are you sure you want to delete this API key?');">
                                        <button type="submit" class="text-red-600 hover:text-red-900" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-8">
                <p class="text-gray-500">No API keys found. Create your first API key to get started.</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Create API Key Modal -->
<div id="createKeyModal" class="fixed inset-0 bg-gray-900 bg-opacity-50 backdrop-blur-sm flex items-center justify-center hidden z-50">
    <div class="bg-white rounded-xl shadow-xl p-6 w-full max-w-md transform transition-all duration-300 scale-100">
        <div class="flex justify-between items-center mb-5">
            <h3 class="text-xl font-semibold text-gray-800 flex items-center">
                <i class="fas fa-key text-indigo-500 mr-3"></i>
                Create API Key
            </h3>
            <button class="text-gray-400 hover:text-gray-600 transition-colors duration-200 closeModal rounded-full hover:bg-gray-100 p-2">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <form action="{{ tenant_url_for('api_manager.create_key') }}" method="POST">
            <div class="mb-5">
                <label for="name" class="block text-sm font-medium text-gray-700 mb-1">API Key Name</label>
                <input type="text" id="name" name="name" required
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
                    placeholder="Enter a descriptive name">
                <p class="mt-1 text-sm text-gray-500">Choose a name that helps you remember what this key is for.</p>
            </div>
            <div class="flex justify-end space-x-3">
                <button type="button" class="closeModal px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Cancel
                </button>
                <button type="submit" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Create Key
                </button>
            </div>
        </form>
    </div>
</div>

<!-- API Key Display Modal (shown only once after creation) -->
{% if new_api_key %}
<div id="apiKeyDisplayModal" class="fixed inset-0 bg-gray-900 bg-opacity-50 backdrop-blur-sm flex items-center justify-center z-50">
    <div class="bg-white rounded-xl shadow-xl p-6 w-full max-w-md transform transition-all duration-300 scale-100">
        <div class="flex justify-between items-center mb-5">
            <h3 class="text-xl font-semibold text-gray-800 flex items-center">
                <i class="fas fa-key text-indigo-500 mr-3"></i>
                Your New API Key
            </h3>
            <button id="closeKeyDisplayModal" class="text-gray-400 hover:text-gray-600 transition-colors duration-200 rounded-full hover:bg-gray-100 p-2">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="mb-5">
            <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm text-yellow-700">
                            <strong>Important:</strong> This API key will only be shown once. Please copy it and store it in a secure location. You will not be able to retrieve it again.
                        </p>
                    </div>
                </div>
            </div>
            <label class="block text-sm font-medium text-gray-700 mb-1">API Key for "{{ new_api_key.name }}"</label>
            <div class="flex items-center">
                <input type="text" id="apiKeyValue" value="{{ new_api_key.key }}" readonly
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                <button id="copyNewApiKey" class="ml-2 p-2 text-indigo-600 hover:text-indigo-800" title="Copy to clipboard">
                    <i class="fas fa-copy"></i>
                </button>
            </div>
        </div>
        <div class="flex justify-end">
            <button id="confirmKeyCopied" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                I've Saved My API Key
            </button>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
    function showCreateKeyModal() {
        document.getElementById('createKeyModal').classList.remove('hidden');
    }

    document.addEventListener('DOMContentLoaded', function() {
        // Handle modal close buttons
        document.querySelectorAll('.closeModal').forEach(button => {
            button.addEventListener('click', () => {
                document.getElementById('createKeyModal').classList.add('hidden');
            });
        });

        // Handle create API key button
        const createKeyBtn = document.getElementById('createKeyBtn');
        if (createKeyBtn) {
            createKeyBtn.addEventListener('click', function() {
                showCreateKeyModal();
            });
        }

        // Handle API key display modal
        const closeKeyDisplayModal = document.getElementById('closeKeyDisplayModal');
        const confirmKeyCopied = document.getElementById('confirmKeyCopied');
        const copyNewApiKey = document.getElementById('copyNewApiKey');
        const apiKeyValue = document.getElementById('apiKeyValue');

        if (closeKeyDisplayModal) {
            closeKeyDisplayModal.addEventListener('click', function() {
                document.getElementById('apiKeyDisplayModal').style.display = 'none';
            });
        }

        if (confirmKeyCopied) {
            confirmKeyCopied.addEventListener('click', function() {
                document.getElementById('apiKeyDisplayModal').style.display = 'none';
            });
        }

        if (copyNewApiKey && apiKeyValue) {
            copyNewApiKey.addEventListener('click', function() {
                apiKeyValue.select();
                copyToClipboard(apiKeyValue.value);
            });
        }

        // Auto-select the API key for easy copying
        if (apiKeyValue) {
            setTimeout(() => {
                apiKeyValue.select();
            }, 500);
        }
    });

    function copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(() => {
            // Show toast notification
            const toast = document.createElement('div');
            toast.className = 'fixed bottom-4 right-4 bg-indigo-600 text-white px-4 py-2 rounded-lg shadow-lg z-50';
            toast.textContent = 'API key copied to clipboard';
            document.body.appendChild(toast);

            // Remove toast after 3 seconds
            setTimeout(() => {
                toast.remove();
            }, 3000);
        }).catch(err => {
            console.error('Failed to copy: ', err);
        });
    }
</script>
{% endblock %}
