{% extends 'base.html' %}

{% block title %}Telegram Bots - Admin Panel{% endblock %}

{% block header_title %}Telegram Bots{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/telegram_bots_mobile.css') }}">
<style>
    /* Desktop-only scrollable table styles (min-width: 769px) */
    @media (min-width: 769px) {
        .overflow-x-auto {
            position: relative;
            width: 100%;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
        }

        /* Add shadow to indicate scrollable content */
        .overflow-x-auto::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            width: 5px;
            background: linear-gradient(to right, rgba(0,0,0,0), rgba(0,0,0,0.05));
            pointer-events: none;
        }

        /* Ensure table takes full width */
        .overflow-x-auto table {
            width: 100%;
            min-width: 1000px; /* Ensures horizontal scrolling on desktop */
        }

        /* Sticky header styles */
        .overflow-x-auto th {
            position: sticky;
            top: 0;
            z-index: 10;
            background-color: #f9fafb; /* bg-gray-50 */
            box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        }
    }

    /* Mobile styles for back button */
    @media (max-width: 640px) {
        .back-button-text {
            display: none;
        }

        .back-button {
            padding: 0.5rem;
            border-radius: 9999px;
        }

        .back-button i {
            margin-right: 0;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden mb-8">
    <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center bg-gradient-to-r from-indigo-50 to-white">
        <div class="flex items-center">
            <i class="fab fa-telegram text-indigo-500 text-xl mr-3"></i>
            <h2 class="text-lg font-semibold text-gray-800">Manage Telegram Bots</h2>
        </div>
        <div class="flex items-center space-x-3">
            <a href="{{ tenant_url_for('dashboard.home_route') }}" class="back-button px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-all duration-200 shadow-sm hover:shadow flex items-center transform hover:-translate-y-0.5">
                <i class="fas fa-arrow-left mr-2"></i><span class="back-button-text">Back</span>
            </a>
            <button type="button" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg shadow-sm hover:shadow transition-colors duration-200 flex items-center text-sm"
                    onclick="openAddBotModal()">
                <i class="fas fa-plus mr-2"></i>Add New Bot
            </button>
        </div>
    </div>

    {% if bots %}
    <div class="px-6 py-3 bg-gray-50 border-b border-gray-200 flex justify-between items-center">
        <div class="text-sm text-gray-600">
            <span class="font-medium">{{ bots|length }}</span> bots configured
        </div>
        <div class="flex space-x-2">
            <div class="relative max-w-xs w-full">
                <label for="searchInput" class="sr-only">Search</label>
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                    <input id="searchInput" type="text" class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors text-sm" placeholder="Search bots...">
                </div>
            </div>
        </div>
    </div>

    <div class="overflow-x-auto">

        <table class="min-w-full divide-y divide-gray-200">
            <thead>
                <tr class="bg-gray-50">
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Name</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Username</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Token</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Status</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for bot in bots %}
                <tr class="hover:bg-gray-50 transition-colors duration-150">
                    <td class="px-6 py-4 whitespace-nowrap" data-label="NAME">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-10 w-10 bg-indigo-100 rounded-full flex items-center justify-center shadow-sm">
                                <i class="fas fa-robot text-indigo-500"></i>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-900">
                                    <a href="{{ tenant_url_for('dashboard.telegram_bot_conf_route', bot_name=bot.telegram_username) }} " class="text-indigo-600 hover:text-indigo-800 hover:underline">
                                    {{ bot.name }}
                                </a>
                                </div>
                                <div class="text-sm text-gray-500 flex items-center">
                                    <i class="far fa-user text-gray-400 mr-1.5 text-xs"></i>
                                    {{ bot.telegram_first_name or 'N/A' }}
                                </div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap" data-label="USERNAME">
                        <div class="text-sm font-medium text-gray-900 flex items-center">
                            <span class="bg-indigo-50 text-indigo-700 px-2 py-1 rounded-md font-mono">@{{ bot.username }}</span>
                        </div>
                        <div class="text-sm text-gray-500 mt-1 flex items-center">
                            <i class="fab fa-telegram text-gray-400 mr-1.5 text-xs"></i>
                            {{ bot.telegram_username or 'N/A' }}
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap" data-label="TOKEN">
                        <div class="text-sm text-gray-900 token-display" data-token="{{ bot.token }}">
                            <div class="flex items-center bg-gray-50 px-3 py-1.5 rounded-lg border border-gray-200">
                                <span class="token-mask font-mono text-gray-700">••••••••:••••••••••••••••••••••••••••••</span>
                                <button class="text-indigo-500 hover:text-indigo-700 ml-2 toggle-token p-1 hover:bg-indigo-50 rounded-full transition-colors">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap" data-label="STATUS">
                        {% if bot.is_active is defined and bot.is_active %}
                            <span class="px-3 py-1 inline-flex items-center text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800 border border-green-200">
                                <span class="w-2 h-2 bg-green-500 rounded-full mr-1.5"></span>
                                Active
                            </span>
                        {% else %}
                            <span class="px-3 py-1 inline-flex items-center text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800 border border-red-200">
                                <span class="w-2 h-2 bg-red-500 rounded-full mr-1.5"></span>
                                Inactive
                            </span>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium" data-label="ACTIONS">
                        <div class="flex space-x-2 justify-end">
                            <button class="p-1.5 bg-indigo-50 text-indigo-600 rounded-lg hover:bg-indigo-100 transition-colors duration-200"
                                    onclick="testBot('{{ bot._id }}')" title="Test Bot Connection">
                                <i class="fas fa-vial"></i>
                            </button>
                            <button class="p-1.5 bg-indigo-50 text-indigo-600 rounded-lg hover:bg-indigo-100 transition-colors duration-200"
                                    onclick="openEditBotModal('{{ bot._id }}', '{{ bot.name }}', '{{ bot.username }}', '{{ bot.token }}', {{ (bot.is_active if bot.is_active is defined else False)|tojson }}, '{{ bot.channel_id if bot.channel_id is defined else "" }}')" title="Edit Bot">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="p-1.5 bg-red-50 text-red-600 rounded-lg hover:bg-red-100 transition-colors duration-200"
                                    onclick="confirmDeleteBot('{{ bot._id }}', '{{ bot.name }}')" title="Delete Bot">
                                <i class="fas fa-trash-alt"></i>
                            </button>
                            <a href="{{ tenant_url_for('dashboard.telegram_bot_users_route', bot_telegram_username=bot.telegram_username) }}"
                               class="p-1.5 bg-green-50 text-green-600 rounded-lg hover:bg-green-100 transition-colors duration-200" title="View Bot Users">
                                <i class="fas fa-users"></i>
                            </a>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% else %}
    <div class="px-6 py-12 text-center">
        <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-indigo-100 text-indigo-400 mb-4">
            <i class="fab fa-telegram text-2xl"></i>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">No Telegram Bots Added Yet</h3>
        <p class="text-gray-500 mb-6 max-w-md mx-auto">Connect your Telegram bots to enhance your communication with users</p>
        <button type="button" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg shadow-sm hover:shadow transition-colors duration-200 flex items-center mx-auto text-sm"
                onclick="openAddBotModal()">
            <i class="fas fa-plus mr-2"></i>Add Your First Bot
        </button>
    </div>
    {% endif %}
</div>

<!-- Add Bot Modal -->
<div id="addBotModal" class="fixed inset-0 bg-gray-900 bg-opacity-50 backdrop-blur-sm hidden overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-6 w-full max-w-md shadow-xl rounded-xl bg-white transform transition-all duration-300 scale-100 border border-gray-100">
        <div>
            <div class="flex justify-between items-center mb-5">
                <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                    <i class="fab fa-telegram text-indigo-500 mr-3"></i>
                    Add New Telegram Bot
                </h3>
                <button type="button" class="text-gray-400 hover:text-gray-600 transition-colors duration-200 rounded-full hover:bg-gray-100 p-2" onclick="closeAddBotModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form action="{{ tenant_url_for('dashboard.add_telegram_bot_route') }}" method="POST">
                <div class="space-y-5">
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Display Name</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-tag text-gray-400"></i>
                            </div>
                            <input type="text" name="name" id="name" required
                                   class="pl-10 block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 py-3"
                                   placeholder="My Awesome Bot">
                        </div>
                    </div>
                    <div>
                        <label for="username" class="block text-sm font-medium text-gray-700 mb-2">Bot Username</label>
                        <div class="mt-1 flex rounded-lg shadow-sm">
                            <span class="inline-flex items-center px-4 rounded-l-lg border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">
                                @
                            </span>
                            <input type="text" name="username" id="username" required
                                   class="flex-1 block w-full rounded-none rounded-r-lg border-gray-300 focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 py-3"
                                   placeholder="my_bot">
                        </div>
                    </div>
                    <div>
                        <label for="token" class="block text-sm font-medium text-gray-700 mb-2">Bot Token</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-key text-gray-400"></i>
                            </div>
                            <input type="text" name="token" id="token" required
                                   class="pl-10 block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 py-3 font-mono text-sm"
                                   placeholder="123456789:ABCDefGhIJKlmNoPQRsTUVwxyZ">
                        </div>
                        <p class="mt-2 text-sm text-gray-500 flex items-center">
                            <i class="fas fa-info-circle mr-1 text-indigo-400"></i>
                            Get this from @BotFather on Telegram
                        </p>
                    </div>
                    <div>
                        <label for="channel_id" class="block text-sm font-medium text-gray-700 mb-2">Channel ID</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-hashtag text-gray-400"></i>
                            </div>
                            <input type="text" name="channel_id" id="channel_id" required
                                   class="pl-10 block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 py-3 font-mono text-sm"
                                   placeholder="-1001234567890">
                        </div>
                        <p class="mt-2 text-sm text-gray-500 flex items-center">
                            <i class="fas fa-info-circle mr-1 text-indigo-400"></i>
                            The ID of the channel where users will be added
                        </p>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-100">
                    <button type="button" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors duration-200 text-sm" onclick="closeAddBotModal()">
                        Cancel
                    </button>
                    <button type="submit" class="px-4 py-2 bg-indigo-500 text-white rounded-lg hover:bg-indigo-600 transition-colors duration-200 shadow-sm hover:shadow focus:outline-none focus:ring-2 focus:ring-indigo-300 flex items-center text-sm">
                        <i class="fas fa-plus mr-2"></i>Add Bot
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Bot Modal -->
<div id="editBotModal" class="fixed inset-0 bg-gray-900 bg-opacity-50 backdrop-blur-sm hidden overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-6 w-full max-w-md shadow-xl rounded-xl bg-white transform transition-all duration-300 scale-100">
        <div>
            <div class="flex justify-between items-center mb-5">
                <h3 class="text-xl font-semibold text-gray-800 flex items-center">
                    <i class="fas fa-edit text-indigo-500 mr-3"></i>
                    Edit Telegram Bot
                </h3>
                <button type="button" class="text-gray-400 hover:text-gray-600 transition-colors duration-200 rounded-full hover:bg-gray-100 p-2" onclick="closeEditBotModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="editBotForm" action="" method="POST">
                <div class="space-y-5">
                    <div>
                        <label for="edit_name" class="block text-sm font-medium text-gray-700 mb-2">Display Name</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-tag text-gray-400"></i>
                            </div>
                            <input type="text" name="name" id="edit_name" required
                                   class="pl-10 block w-full rounded-lg border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-500 focus:ring-opacity-50 py-3">
                        </div>
                    </div>
                    <div>
                        <label for="edit_username" class="block text-sm font-medium text-gray-700 mb-2">Bot Username</label>
                        <div class="mt-1 flex rounded-lg shadow-sm">
                            <span class="inline-flex items-center px-4 rounded-l-lg border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">
                                @
                            </span>
                            <input type="text" name="username" id="edit_username" required
                                   class="flex-1 block w-full rounded-none rounded-r-lg border-gray-300 focus:border-indigo-500 focus:ring focus:ring-indigo-500 focus:ring-opacity-50 py-3">
                        </div>
                    </div>
                    <div>
                        <label for="edit_token" class="block text-sm font-medium text-gray-700 mb-2">Bot Token</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-key text-gray-400"></i>
                            </div>
                            <input type="text" name="token" id="edit_token" required
                                   class="pl-10 block w-full rounded-lg border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-500 focus:ring-opacity-50 py-3 font-mono text-sm">
                        </div>
                    </div>
                    <div>
                        <label for="edit_channel_id" class="block text-sm font-medium text-gray-700 mb-2">Channel ID</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-hashtag text-gray-400"></i>
                            </div>
                            <input type="text" name="channel_id" id="edit_channel_id" required
                                   class="pl-10 block w-full rounded-lg border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-500 focus:ring-opacity-50 py-3 font-mono text-sm">
                        </div>
                    </div>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <div class="flex items-center">
                            <input type="checkbox" name="is_active" id="edit_is_active"
                                   class="h-5 w-5 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                            <label for="edit_is_active" class="ml-2 block text-sm font-medium text-gray-900">
                                Active
                            </label>
                        </div>
                        <p class="mt-2 text-xs text-gray-500">When active, the bot will respond to user commands and messages</p>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 mt-6 pt-4 border-t border-gray-100">
                    <button type="button" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors duration-200 font-medium" onclick="closeEditBotModal()">
                        Cancel
                    </button>
                    <button type="submit" class="px-5 py-2 bg-indigo-500 text-white rounded-lg hover:bg-indigo-600 transition-colors duration-200 font-medium shadow-sm hover:shadow focus:outline-none focus:ring-2 focus:ring-indigo-300 flex items-center">
                        <i class="fas fa-save mr-2"></i>Save Changes
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteBotModal" class="fixed inset-0 bg-gray-900 bg-opacity-50 backdrop-blur-sm hidden overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-6 w-full max-w-md shadow-xl rounded-xl bg-white transform transition-all duration-300 scale-100">
        <div class="text-center">
            <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-4">
                <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
            </div>
            <h3 class="text-xl leading-6 font-semibold text-gray-900">Delete Bot</h3>
            <div class="mt-4 bg-red-50 p-4 rounded-lg border-l-4 border-red-500 text-left mb-5">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-circle text-red-500"></i>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800">Warning</h3>
                        <div class="mt-2 text-sm text-red-700">
                            <p>Are you sure you want to delete this bot? This action cannot be undone.</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 p-4 rounded-lg mb-5">
                <p class="text-sm text-gray-600 mb-2">Bot to be deleted:</p>
                <p id="deleteBotName" class="text-lg font-medium text-gray-900 flex items-center justify-center">
                    <i class="fab fa-telegram text-indigo-500 mr-2"></i>
                    <span></span>
                </p>
            </div>
            <div class="flex justify-center space-x-3 mt-6">
                <button type="button" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors duration-200 font-medium" onclick="closeDeleteBotModal()">
                    Cancel
                </button>
                <button type="button" id="confirmDeleteBtn" class="px-5 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors duration-200 font-medium shadow-sm hover:shadow focus:outline-none focus:ring-2 focus:ring-red-300 flex items-center">
                    <i class="fas fa-trash-alt mr-2"></i>Delete
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Test Bot Result Modal -->
<div id="testBotModal" class="fixed inset-0 bg-gray-900 bg-opacity-50 backdrop-blur-sm hidden overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-6 w-full max-w-md shadow-xl rounded-xl bg-white transform transition-all duration-300 scale-100">
        <div>
            <div class="flex justify-between items-center mb-5">
                <h3 class="text-xl font-semibold text-gray-800 flex items-center">
                    <i class="fas fa-vial text-indigo-500 mr-3"></i>
                    Bot Test Results
                </h3>
                <button type="button" class="text-gray-400 hover:text-gray-600 transition-colors duration-200 rounded-full hover:bg-gray-100 p-2" onclick="closeTestBotModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="testResultContent" class="bg-gray-50 p-6 rounded-lg mb-5">
                <div class="flex flex-col items-center justify-center py-4">
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"></div>
                    <p class="text-gray-600 font-medium">Testing bot connection...</p>
                    <p class="text-gray-500 text-sm mt-2">This may take a few seconds</p>
                </div>
            </div>
            <div class="flex justify-end">
                <button type="button" class="px-5 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200 font-medium shadow-sm hover:shadow focus:outline-none focus:ring-2 focus:ring-blue-300" onclick="closeTestBotModal()">
                    Close
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Modal animation functions
    function showModal(modalId) {
        const modal = document.getElementById(modalId);
        const modalContent = modal.querySelector('.bg-white');

        // Show modal with animation
        modalContent.classList.add('scale-95', 'opacity-0');
        modal.classList.remove('hidden');

        setTimeout(() => {
            modalContent.classList.remove('scale-95', 'opacity-0');
            modalContent.classList.add('scale-100', 'opacity-100');
        }, 10);
    }

    function closeModal(modalId) {
        const modal = document.getElementById(modalId);
        const modalContent = modal.querySelector('.bg-white');

        // Hide modal with animation
        modalContent.classList.remove('scale-100');
        modalContent.classList.add('scale-95', 'opacity-0');

        setTimeout(() => {
            modal.classList.add('hidden');
            modalContent.classList.remove('scale-95', 'opacity-0');
        }, 200);
    }

    // Add Bot Modal
    function openAddBotModal() {
        showModal('addBotModal');
    }

    function closeAddBotModal() {
        closeModal('addBotModal');
    }

    // Edit Bot Modal
    function openEditBotModal(id, name, username, token, isActive, channelId) {
        document.getElementById('edit_name').value = name;
        document.getElementById('edit_username').value = username;
        document.getElementById('edit_token').value = token;
        document.getElementById('edit_channel_id').value = channelId || '';
        document.getElementById('edit_is_active').checked = isActive;

        document.getElementById('editBotForm').action = "{{ tenant_url_for('dashboard.edit_telegram_bot_route', bot_id='') }}" + id;
        showModal('editBotModal');
    }

    function closeEditBotModal() {
        closeModal('editBotModal');
    }

    // Delete Bot Modal
    function confirmDeleteBot(id, name) {
        document.getElementById('deleteBotName').querySelector('span').textContent = name;
        document.getElementById('confirmDeleteBtn').onclick = function() {
            deleteBot(id);
        };
        showModal('deleteBotModal');
    }

    function closeDeleteBotModal() {
        closeModal('deleteBotModal');
    }

    function deleteBot(id) {
        fetch("{{ tenant_url_for('dashboard.delete_telegram_bot_route', bot_id='') }}" + id, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.reload();
            } else {
                alert('Error: ' + data.message);
                closeDeleteBotModal();
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while deleting the bot');
            closeDeleteBotModal();
        });
    }

    // Test Bot Modal
    function testBot(id) {
        document.getElementById('testResultContent').innerHTML = `
            <div class="flex flex-col items-center justify-center py-4">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mb-4"></div>
                <p class="text-gray-600 font-medium">Testing bot connection...</p>
                <p class="text-gray-500 text-sm mt-2">This may take a few seconds</p>
            </div>
        `;
        showModal('testBotModal');

        fetch("{{ tenant_url_for('dashboard.test_telegram_bot_route', bot_id='') }}" + id, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Determine channel status indicators
                let channelStatusIcon = '';
                let channelStatusClass = '';
                let channelStatusText = '';
                let adminStatusIcon = '';
                let adminStatusClass = '';
                let adminStatusText = '';

                if (data.channel_status) {
                    // Channel membership status
                    if (data.channel_status.is_member) {
                        channelStatusIcon = '<i class="fas fa-check-circle text-green-600 text-lg"></i>';
                        channelStatusClass = 'text-green-600';
                        channelStatusText = 'Bot is a member of the channel';
                    } else {
                        channelStatusIcon = '<i class="fas fa-times-circle text-red-600 text-lg"></i>';
                        channelStatusClass = 'text-red-600';
                        channelStatusText = 'Bot is NOT a member of the channel';
                    }

                    // Admin status
                    if (data.channel_status.is_admin) {
                        adminStatusIcon = '<i class="fas fa-check-circle text-green-600 text-lg"></i>';
                        adminStatusClass = 'text-green-600';
                        adminStatusText = 'Bot has admin privileges';
                    } else {
                        adminStatusIcon = '<i class="fas fa-times-circle text-red-600 text-lg"></i>';
                        adminStatusClass = 'text-red-600';
                        adminStatusText = 'Bot does NOT have admin privileges';
                    }
                }

                // Error message if any
                let errorMessage = '';
                if (data.channel_status && data.channel_status.error) {
                    // Truncate long error messages and format them better
                    let errorText = data.channel_status.error;

                    // Handle specific error cases
                    if (errorText.includes('403 Client Error: Forbidden')) {
                        errorText = 'Error: Bot cannot access the channel (403 Forbidden)';
                    }
                    // If it's a network error with a URL, extract just the main error part
                    else if (errorText.includes('Network error:') && errorText.includes('https://api.telegram.org/')) {
                        // Extract just the main error message
                        const mainError = errorText.split('https://')[0].trim();
                        errorText = mainError;
                    }

                    // Limit to 80 characters with ellipsis
                    if (errorText.length > 80) {
                        errorText = errorText.substring(0, 80) + '...';
                    }

                    // Add helpful suggestion for 403 errors
                    let helpText = '';
                    if (errorText.includes('403 Forbidden')) {
                        helpText = `<p class="text-xs text-gray-600 mt-2">
                            Make sure the bot has been added to the channel and has admin privileges.
                        </p>`;
                    }

                    errorMessage = `
                        <div class="bg-red-50 p-3 rounded-lg border-l-4 border-red-500 text-left mt-3">
                            <p class="text-sm text-red-700">${errorText}</p>
                            ${helpText}
                        </div>
                    `;
                }

                document.getElementById('testResultContent').innerHTML = `
                    <div class="text-center">
                        <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-5 shadow-sm">
                            <i class="fas fa-check text-green-600 text-2xl"></i>
                        </div>
                        <h4 class="text-xl font-medium text-gray-900 mb-3">Connection Successful</h4>
                        <div class="bg-white p-4 rounded-lg text-left border border-gray-200 shadow-sm">
                            <div class="flex py-2 border-b border-gray-100">
                                <div class="text-gray-600 font-medium w-1/3">Bot ID:</div>
                                <span class="font-mono text-indigo-600">${data.bot_info.id}</span>
                            </div>
                            <div class="flex py-2 border-b border-gray-100">
                                <div class="text-gray-600 font-medium w-1/3">Username:</div>
                                <span class="font-mono text-indigo-600">@${data.bot_info.username}</span>
                            </div>
                            <div class="flex py-2 border-b border-gray-100">
                                <div class="text-gray-600 font-medium w-1/3">Name:</div>
                                <span class="font-medium">${data.bot_info.first_name}</span>
                            </div>
                            <div class="flex py-2 border-b border-gray-100">
                                <div class="text-gray-600 font-medium w-1/3">Channel:</div>
                                <div class="flex flex-col">
                                    <span class="font-medium">${data.channel_info && data.channel_info.title ? data.channel_info.title : 'Unknown'}</span>
                                    <span class="text-xs text-gray-500 font-mono">${data.channel_id || 'Not set'}</span>
                                </div>
                            </div>
                            <div class="flex py-2 border-b border-gray-100">
                                <div class="text-gray-600 font-medium w-1/3">Channel Status:</div>
                                <div class="flex items-center">
                                    <div class="w-7 flex justify-center items-center">${channelStatusIcon}</div>
                                    <div class="font-medium ${channelStatusClass}">${channelStatusText}</div>
                                </div>
                            </div>
                            <div class="flex py-2">
                                <div class="text-gray-600 font-medium w-1/3">Admin Status:</div>
                                <div class="flex items-center">
                                    <div class="w-7 flex justify-center items-center">${adminStatusIcon}</div>
                                    <div class="font-medium ${adminStatusClass}">${adminStatusText}</div>
                                </div>
                            </div>
                        </div>
                        ${errorMessage}
                    </div>
                `;
            } else {
                document.getElementById('testResultContent').innerHTML = `
                    <div class="text-center">
                        <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-5 shadow-sm">
                            <i class="fas fa-times text-red-600 text-2xl"></i>
                        </div>
                        <h4 class="text-xl font-medium text-gray-900 mb-3">Connection Failed</h4>
                        <div class="bg-red-50 p-4 rounded-lg border-l-4 border-red-500 text-left mb-2">
                            <p class="text-sm text-red-700">${data.message}</p>
                        </div>
                        <p class="text-xs text-gray-500 mt-3">Please check your bot token and try again</p>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            document.getElementById('testResultContent').innerHTML = `
                <div class="text-center">
                    <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-5 shadow-sm">
                        <i class="fas fa-exclamation-triangle text-red-600 text-2xl"></i>
                    </div>
                    <h4 class="text-xl font-medium text-gray-900 mb-3">Error</h4>
                    <div class="bg-red-50 p-4 rounded-lg border-l-4 border-red-500 text-left mb-2">
                        <p class="text-sm text-red-700">An error occurred while testing the bot connection</p>
                    </div>
                    <p class="text-xs text-gray-500 mt-3">Please try again later or check your network connection</p>
                </div>
            `;
        });
    }

    function closeTestBotModal() {
        closeModal('testBotModal');
    }

    // Toggle token visibility
    document.addEventListener('DOMContentLoaded', function() {
        const toggleButtons = document.querySelectorAll('.toggle-token');
        toggleButtons.forEach(button => {
            button.addEventListener('click', function() {
                const tokenDisplay = this.closest('.token-display');
                const tokenMask = tokenDisplay.querySelector('.token-mask');
                const token = tokenDisplay.dataset.token;

                if (tokenMask.textContent.includes('•')) {
                    tokenMask.textContent = token;
                    this.innerHTML = '<i class="fas fa-eye-slash"></i>';
                } else {
                    tokenMask.textContent = '••••••••:••••••••••••••••••••••••••••••';
                    this.innerHTML = '<i class="fas fa-eye"></i>';
                }
            });
        });

        // Add mobile card click functionality
        if (window.innerWidth <= 768) {
            const tableRows = document.querySelectorAll('tbody tr');
            tableRows.forEach(row => {
                // Get the bot name link from the first cell
                const botNameLink = row.querySelector('td:first-child a');
                if (botNameLink) {
                    const href = botNameLink.getAttribute('href');
                    // Make the entire row clickable except for action buttons and token toggle
                    row.addEventListener('click', function(e) {
                        // Don't trigger if clicking on action buttons or token toggle
                        if (!e.target.closest('td:nth-child(5)') && !e.target.closest('.toggle-token')) {
                            window.location.href = href;
                        }
                    });
                    // Add cursor pointer to indicate clickable
                    row.style.cursor = 'pointer';
                }
            });
        }

        // Search functionality
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                const rows = document.querySelectorAll('tbody tr');

                rows.forEach(row => {
                    const nameCell = row.querySelector('td:nth-child(1)');
                    const usernameCell = row.querySelector('td:nth-child(2)');

                    if (nameCell && usernameCell) {
                        const nameText = nameCell.textContent.toLowerCase();
                        const usernameText = usernameCell.textContent.toLowerCase();

                        if (nameText.includes(searchTerm) || usernameText.includes(searchTerm)) {
                            row.style.display = '';
                        } else {
                            row.style.display = 'none';
                        }
                    }
                });
            });
        }

        // Close modals with escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const modals = ['addBotModal', 'editBotModal', 'deleteBotModal', 'testBotModal'];
                modals.forEach(modalId => {
                    const modal = document.getElementById(modalId);
                    if (modal && !modal.classList.contains('hidden')) {
                        closeModal(modalId);
                    }
                });
            }
        });

        // Close modals when clicking outside
        const modals = ['addBotModal', 'editBotModal', 'deleteBotModal', 'testBotModal'];
        modals.forEach(modalId => {
            const modal = document.getElementById(modalId);
            if (modal) {
                modal.addEventListener('click', function(e) {
                    if (e.target === this) {
                        closeModal(modalId);
                    }
                });
            }
        });
    });
</script>
{% endblock %}