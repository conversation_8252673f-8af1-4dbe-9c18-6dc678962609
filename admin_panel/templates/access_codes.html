{% extends 'base.html' %}

{% block title %}Access Codes{% endblock %}
{% block header_title %}Access Codes{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/access_codes_mobile.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/access_codes.css') }}">
<style>
    /* Additional custom styles can be added here */

    .litepicker .container__months .month-item-header {
        padding: 10px 0;
        font-weight: 600;
        color: #374151;
        transition: all 0.2s ease;
        border-bottom: 1px solid #F3F4F6;
        margin-bottom: 8px;
    }

    .litepicker .container__months .month-item-header div {
        transition: all 0.2s ease;
    }

    .litepicker .container__months .month-item-header div:hover {
        color: #4F46E5;
    }

    .litepicker .container__months .month-item-header .button-previous-month,
    .litepicker .container__months .month-item-header .button-next-month {
        color: #6B7280;
        padding: 4px 8px;
        border-radius: 0.25rem;
    }

    .litepicker .container__months .month-item-header .button-previous-month:hover,
    .litepicker .container__months .month-item-header .button-next-month:hover {
        background-color: #F9FAFB;
    }

    .litepicker .container__months .month-item-weekdays-row {
        color: #6B7280;
        font-weight: 500;
        font-size: 0.75rem;
        padding: 8px 0;
    }

    .litepicker .container__days .day-item {
        color: #4B5563;
        border-radius: 0;
        height: 36px;
        width: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.2s ease;
        position: relative;
        font-weight: 400;
        font-size: 0.875rem;
    }

    .litepicker .container__days .day-item:hover {
        color: #4F46E5;
        background-color: #F5F7FF;
        font-weight: 500;
        z-index: 1;
        outline: 1px solid #E0E7FF;
    }

    .litepicker .container__days .day-item.is-start-date,
    .litepicker .container__days .day-item.is-end-date {
        background-color: #4F46E5;
        color: white;
        font-weight: 500;
        z-index: 2;
    }

    .litepicker .container__days .day-item.is-in-range {
        background-color: #EEF2FF;
        color: #4F46E5;
        position: relative;
    }

    /* Special handling for first and last items in range */
    .litepicker .container__days .day-item.is-in-range.is-first-day-in-range {
        border-left: 2px solid #4F46E5;
    }

    .litepicker .container__days .day-item.is-in-range.is-last-day-in-range {
        border-right: 2px solid #4F46E5;
    }

    /* Remove pulsing animation for a more professional look */
    .litepicker .container__days .day-item.is-in-range::before {
        display: none;
    }

    /* Footer removed as we're using autoApply */

    .litepicker .container__tooltip {
        border-radius: 0.25rem;
        background-color: #F3F4F6;
        color: #4B5563;
        font-size: 0.75rem;
        padding: 2px 6px;
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    }

    .date-range-input {
        cursor: pointer;
        background-color: white;
        transition: all 0.2s ease;
        border-radius: 0.25rem;
        font-size: 0.875rem;
    }

    .date-range-input:hover {
        border-color: #818CF8;
    }

    .date-range-input:focus {
        border-color: #4F46E5;
        box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.1);
    }

    /* Make placeholder text smaller */
    .date-range-input::placeholder {
        font-size: 0.875rem;
        color: #9CA3AF;
    }

    /* Date restriction tooltip */
    #subscriptionDateTooltip {
        color: #DC2626;
        font-size: 0.75rem;
        margin-top: 0.25rem;
        transition: opacity 0.3s ease;
        animation: fadeInOut 3s ease;
    }

    @keyframes fadeInOut {
        0% { opacity: 0; }
        10% { opacity: 1; }
        90% { opacity: 1; }
        100% { opacity: 0; }
    }

    /* Today's date styling - Simple Professional */
    .litepicker .container__days .day-item.is-today {
        color: #DC2626; /* Subtle red color */
        font-weight: 500;
    }

    /* Today when selected or in range */
    .litepicker .container__days .day-item.is-today.is-start-date,
    .litepicker .container__days .day-item.is-today.is-end-date {
        color: white; /* Keep white text when selected */
    }

    .litepicker .container__days .day-item.is-today.is-in-range {
        color: #DC2626; /* Keep red when in range */
    }
</style>
{% endblock %}

{% block content %}
<div class="bg-white rounded-xl shadow-md mb-8 border border-gray-100 overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center bg-gradient-to-r from-indigo-50 to-white access-codes-header">
        <div class="flex items-center">
            <i class="fas fa-key text-indigo-500 mr-3"></i>
            <h2 class="text-lg font-semibold text-gray-800">Access Codes</h2>
        </div>
        <div class="flex space-x-3">
            <button id="addCodeBtn" class="px-4 py-2 bg-indigo-500 text-white rounded-lg hover:bg-indigo-600 transition-all duration-200 shadow-sm hover:shadow flex items-center transform hover:-translate-y-0.5">
                <i class="fas fa-plus mr-2"></i>Add Code
            </button>
            <button id="uploadFileBtn" class="px-4 py-2 bg-emerald-500 text-white rounded-lg hover:bg-emerald-600 transition-all duration-200 shadow-sm hover:shadow flex items-center transform hover:-translate-y-0.5">
                <i class="fas fa-upload mr-2"></i>Upload File
            </button>
            <!-- Add bulk delete button -->
            <button id="bulkDeleteBtn" class="px-4 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-all duration-200 shadow-sm hover:shadow flex items-center transform hover:-translate-y-0.5 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:hover:shadow-none" disabled>
                <i class="fas fa-trash-alt mr-2"></i>Delete Selected <span id="selectedCount" class="ml-1 bg-white bg-opacity-20 text-white text-xs px-1.5 py-0.5 rounded-full hidden">0</span>
            </button>
        </div>
    </div>

    <div id="addCodeModal" class="fixed inset-0 bg-gray-900 bg-opacity-50 backdrop-blur-sm flex items-center justify-center hidden z-50">
        <div class="bg-white rounded-xl shadow-xl p-6 w-full max-w-md transform transition-all duration-300 scale-100">
            <div class="flex justify-between items-center mb-5">
                <h3 class="text-xl font-semibold text-gray-800 flex items-center">
                    <i class="fas fa-plus-circle text-indigo-500 mr-3"></i>
                    Add New Access Code
                </h3>
                <button class="text-gray-400 hover:text-gray-600 transition-colors duration-200 closeModal rounded-full hover:bg-gray-100 p-2">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form action="{{ tenant_url_for('dashboard.add_access_code_route') }}" method="POST">
                <div class="mb-5">
                    <label for="accessCode" class="block text-sm font-medium text-gray-700 mb-2">Access Code</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-key text-gray-400"></i>
                        </div>
                        <input type="text" id="accessCode" name="code" class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors" required placeholder="Enter access code">
                    </div>
                    <p class="mt-2 text-sm text-gray-500">This code will be used by users to access the system.</p>
                </div>
                <div class="mb-5">
                    <label for="expirationDays" class="block text-sm font-medium text-gray-700 mb-2">Expiration Days</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-calendar-alt text-gray-400"></i>
                        </div>
                        <input type="number" id="expirationDays" name="expiration_days" class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors" min="1" placeholder="Enter number of days until expiration">
                    </div>
                    <p class="mt-2 text-sm text-gray-500">Number of days until this access code expires.</p>
                </div>
                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors duration-200 closeModal font-medium">Cancel</button>
                    <button type="submit" class="px-5 py-2 bg-indigo-500 text-white rounded-lg hover:bg-indigo-600 transition-colors duration-200 font-medium shadow-sm hover:shadow focus:outline-none focus:ring-2 focus:ring-indigo-300">Add Code</button>
                </div>
            </form>
        </div>
    </div>

    <div id="uploadFileModal" class="fixed inset-0 bg-gray-900 bg-opacity-50 backdrop-blur-sm flex items-center justify-center hidden z-50">
        <div class="bg-white rounded-xl shadow-xl p-6 w-full max-w-md transform transition-all duration-300 scale-100">
            <div class="flex justify-between items-center mb-5">
                <h3 class="text-xl font-semibold text-gray-800 flex items-center">
                    <i class="fas fa-upload text-emerald-500 mr-3"></i>
                    Upload Access Codes
                </h3>
                <button class="text-gray-400 hover:text-gray-600 transition-colors duration-200 closeModal rounded-full hover:bg-gray-100 p-2">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form action="{{ tenant_url_for('dashboard.upload_access_codes_route') }}" method="POST" enctype="multipart/form-data">
                <div class="mb-5">
                    <label for="codesFile" class="block text-sm font-medium text-gray-700 mb-2">Select File</label>
                    <div id="fileDropArea" class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-lg hover:border-emerald-400 transition-colors duration-200">
                        <div class="space-y-1 text-center">
                            <i id="fileIcon" class="fas fa-file-upload text-gray-400 text-3xl mb-3"></i>
                            <div id="uploadPrompt" class="flex text-sm text-gray-600">
                                <label for="codesFile" class="relative cursor-pointer bg-white rounded-md font-medium text-emerald-600 hover:text-emerald-500 focus-within:outline-none">
                                    <span>Upload a file</span>
                                    <input id="codesFile" name="file" type="file" class="sr-only" required accept=".txt,.csv">
                                </label>
                                <p class="pl-1">or drag and drop</p>
                            </div>
                            <div id="fileDetails" class="hidden">
                                <p id="fileName" class="text-sm font-medium text-emerald-600 break-all"></p>
                                <p id="fileSize" class="text-xs text-gray-500"></p>
                            </div>
                            <p class="text-xs text-gray-500">.TXT or .CSV files only</p>
                        </div>
                    </div>
                </div>
                <div class="mb-5 bg-gray-50 p-4 rounded-lg">
                    <h4 class="text-sm font-medium text-gray-700 mb-2">Supported Formats:</h4>
                    <ul class="list-disc pl-5 space-y-1 text-sm text-gray-600">
                        <li>Text file with one access code per line</li>
                        <li>CSV file with headers including: client_account, reg_date, volume_mln_usd, trade_fn</li>
                    </ul>
                </div>
                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors duration-200 closeModal font-medium">Cancel</button>
                    <button type="submit" class="px-5 py-2 bg-emerald-500 text-white rounded-lg hover:bg-emerald-600 transition-colors duration-200 font-medium shadow-sm hover:shadow focus:outline-none focus:ring-2 focus:ring-emerald-300 flex items-center">
                        <i class="fas fa-cloud-upload-alt mr-2"></i>Upload
                    </button>
                </div>
            </form>
        </div>
    </div>

    <div id="codeDetailsModal" class="fixed inset-0 bg-gray-900 bg-opacity-50 backdrop-blur-sm flex items-center justify-center hidden z-50">
        <div class="bg-white rounded-xl shadow-xl p-6 w-full max-w-md transform transition-all duration-300 scale-100">
            <div class="flex justify-between items-center mb-5">
                <h3 class="text-xl font-semibold text-gray-800 flex items-center">
                    <i class="fas fa-info-circle text-indigo-500 mr-3"></i>
                    Access Code Details
                </h3>
                <button class="text-gray-400 hover:text-gray-600 transition-colors duration-200 closeModal rounded-full hover:bg-gray-100 p-2">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div id="codeDetailsContent" class="space-y-4 bg-gray-50 p-4 rounded-lg mb-5">
                <div class="flex justify-between items-center border-b border-gray-200 pb-3">
                    <span class="text-gray-600 font-medium">Access Code:</span>
                    <span id="detailCode" class="font-mono font-medium text-indigo-600"></span>
                </div>
                <div class="flex justify-between items-center py-2">
                    <span class="text-gray-600">Registration Date:</span>
                    <span id="detailRegDate" class="font-medium text-gray-800 flex items-center">
                        <i class="far fa-calendar-alt mr-1 text-gray-400"></i>
                        <span></span>
                    </span>
                </div>
                <div class="flex justify-between items-center py-2">
                    <span class="text-gray-600">Volume (USD millions):</span>
                    <span id="detailVolume" class="font-medium text-gray-800 flex items-center">
                        <i class="fas fa-chart-line mr-1 text-gray-400"></i>
                        <span></span>
                    </span>
                </div>
                <div class="flex justify-between items-center py-2">
                    <span class="text-gray-600">Trade Function:</span>
                    <span id="detailTradeFn" class="font-medium text-gray-800 flex items-center">
                        <i class="fas fa-cogs mr-1 text-gray-400"></i>
                        <span></span>
                    </span>
                </div>
                <div class="flex justify-between items-center py-2">
                    <span class="text-gray-600">Added at:</span>
                    <span id="detailAddedAt" class="font-medium text-gray-800 flex items-center">
                        <i class="fas fa-calendar-plus mr-1 text-gray-400"></i>
                        <span></span>
                    </span>
                </div>
                <div class="flex justify-between items-center py-2">
                    <span class="text-gray-600">Expiration Date:</span>
                    <span id="detailExpirationDate" class="font-medium text-gray-800 flex items-center">
                        <i class="fas fa-clock mr-1 text-gray-400"></i>
                        <span></span>
                    </span>
                </div>
            </div>
            <div class="flex justify-end">
                <button type="button" class="px-5 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors duration-200 font-medium closeModal shadow-sm hover:shadow focus:outline-none focus:ring-2 focus:ring-indigo-300 flex items-center">
                    <i class="fas fa-check mr-2"></i>Close
                </button>
            </div>
        </div>
    </div>

    <div id="editCodeModal" class="fixed inset-0 bg-gray-900 bg-opacity-50 backdrop-blur-sm flex items-center justify-center hidden z-50">
        <div class="bg-white rounded-xl shadow-xl p-6 w-full max-w-md transform transition-all duration-300 scale-100">
            <div class="flex justify-between items-center mb-5">
                <h3 class="text-xl font-semibold text-gray-800 flex items-center">
                    <i class="fas fa-edit text-indigo-500 mr-3"></i>
                    Edit Access Code
                </h3>
                <button class="text-gray-400 hover:text-gray-600 transition-colors duration-200 closeModal rounded-full hover:bg-gray-100 p-2">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form action="{{ tenant_url_for('dashboard.edit_access_code_route') }}" method="POST">
                <input type="hidden" id="originalCode" name="original_code">
                <div class="mb-5">
                    <label for="editAccessCode" class="block text-sm font-medium text-gray-700 mb-2">Access Code</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-key text-gray-400"></i>
                        </div>
                        <input type="text" id="editAccessCode" name="new_code" class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors" required>
                    </div>
                    <p class="mt-2 text-sm text-gray-500">Edit the access code value.</p>
                </div>
                <div class="mb-5">
                    <label for="editExpirationDays" class="block text-sm font-medium text-gray-700 mb-2">Expiration Days</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-calendar-alt text-gray-400"></i>
                        </div>
                        <input type="number" id="editExpirationDays" name="expiration_days" class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors" min="1" placeholder="Enter number of days until expiration">
                    </div>
                    <p class="mt-2 text-sm text-gray-500">Number of days until this access code expires.</p>
                </div>
                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors duration-200 closeModal font-medium">Cancel</button>
                    <button type="submit" class="px-5 py-2 bg-indigo-500 text-white rounded-lg hover:bg-indigo-600 transition-colors duration-200 font-medium shadow-sm hover:shadow focus:outline-none focus:ring-2 focus:ring-indigo-300 flex items-center">
                        <i class="fas fa-save mr-2"></i>Update Code
                    </button>
                </div>
            </form>
        </div>
    </div>

    <div id="deleteCodeModal" class="fixed inset-0 bg-gray-900 bg-opacity-50 backdrop-blur-sm flex items-center justify-center hidden z-50">
        <div class="bg-white rounded-xl shadow-xl p-6 w-full max-w-md transform transition-all duration-300 scale-100">
            <div class="flex justify-between items-center mb-5">
                <h3 class="text-xl font-semibold text-gray-800 flex items-center">
                    <i class="fas fa-trash-alt text-red-500 mr-3"></i>
                    Delete Access Code
                </h3>
                <button class="text-gray-400 hover:text-gray-600 transition-colors duration-200 closeModal rounded-full hover:bg-gray-100 p-2">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="deleteCodeForm" action="{{ tenant_url_for('dashboard.delete_access_code_route') }}" method="POST">
                <input type="hidden" id="deleteCodeValue" name="code">
                <div class="bg-red-50 p-4 rounded-lg mb-5 border-l-4 border-red-500">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-triangle text-red-500"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-red-800">Warning</h3>
                            <div class="mt-2 text-sm text-red-700">
                                <p>Are you sure you want to delete this access code? This action cannot be undone.</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors duration-200 closeModal font-medium">Cancel</button>
                    <button type="submit" class="px-5 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors duration-200 font-medium shadow-sm hover:shadow focus:outline-none focus:ring-2 focus:ring-red-300 flex items-center">
                        <i class="fas fa-trash-alt mr-2"></i>Delete
                    </button>
                </div>
            </form>
        </div>
    </div>

    <div class="overflow-x-auto">
        <div class="px-6 py-4 border-b border-gray-200 access-codes-search-container">
            <!-- Basic Search and Status Summary -->
            <div class="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-4">
                <div class="relative max-w-md w-full">
                    <label for="searchInput" class="sr-only">Search</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                        <input id="searchInput" type="text" class="block w-full pl-10 pr-10 py-2.5 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors shadow-sm" placeholder="Search all access codes..." value="{{ filter_query }}">
                        {% if filter_query %}
                        <div id="clearSearch" class="absolute inset-y-0 right-0 pr-3 flex items-center cursor-pointer text-gray-400 hover:text-gray-600">
                            <i class="fas fa-times"></i>
                        </div>
                        {% endif %}
                    </div>
                    <p class="mt-1 text-xs text-gray-500 flex items-center">
                        <i class="fas fa-info-circle mr-1 text-indigo-400"></i>
                        Type at least 2 characters to search across all codes
                    </p>
                </div>
                <div class="flex flex-col md:flex-row md:items-center gap-2">
                    <button id="advancedFilterToggle" class="px-3 py-1.5 bg-indigo-50 text-indigo-700 rounded-lg text-sm flex items-center hover:bg-indigo-100 transition-colors">
                        <i class="fas fa-sliders-h mr-2"></i>
                        <span>Advanced Filters</span>
                    </button>
                    <span class="text-sm text-gray-600 whitespace-nowrap">Total: <span class="font-medium text-gray-900">{{ pagination.total }}</span> codes</span>
                </div>
            </div>

            <!-- Active Filters Display -->
            <div id="activeFilters" class="flex flex-wrap gap-2 mb-4 {% if not filter_query and filter_status == 'all' and not filter_user_id and not filter_subscribed_from and not filter_subscribed_to and not filter_expired_from and not filter_expired_to %}hidden{% endif %}">
                <div class="text-sm text-gray-700 flex items-center bg-gray-50 px-3 py-1 rounded-lg border border-gray-200">
                    <i class="fas fa-filter mr-2 text-gray-500"></i>
                    <span class="font-medium">Active filters:</span>
                </div>

                {% if filter_query %}
                <div class="bg-gray-100 text-gray-700 px-3 py-1 rounded-lg text-sm flex items-center border border-gray-200">
                    <span>Search: <span class="font-medium">"{{ filter_query }}"</span></span>
                    <a href="{{ tenant_url_for('dashboard.access_codes_route', page=1, per_page=pagination.per_page, sort=sort_field, order=sort_order, status=filter_status, user_id=filter_user_id, subscribed_from=filter_subscribed_from, subscribed_to=filter_subscribed_to, expired_from=filter_expired_from, expired_to=filter_expired_to) }}" class="ml-2 text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times-circle"></i>
                    </a>
                </div>
                {% endif %}

                {% if filter_status != 'all' %}
                <div class="bg-gray-100 text-gray-700 px-3 py-1 rounded-lg text-sm flex items-center border border-gray-200">
                    <span>Status: <span class="font-medium">{{ filter_status|capitalize }}</span></span>
                    <a href="{{ tenant_url_for('dashboard.access_codes_route', page=1, per_page=pagination.per_page, sort=sort_field, order=sort_order, status='all', q=filter_query, user_id=filter_user_id, subscribed_from=filter_subscribed_from, subscribed_to=filter_subscribed_to, expired_from=filter_expired_from, expired_to=filter_expired_to) }}" class="ml-2 text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times-circle"></i>
                    </a>
                </div>
                {% endif %}

                {% if filter_user_id %}
                <div class="bg-gray-100 text-gray-700 px-3 py-1 rounded-lg text-sm flex items-center border border-gray-200">
                    <span>User ID: <span class="font-medium">{{ filter_user_id }}</span></span>
                    <a href="{{ tenant_url_for('dashboard.access_codes_route', page=1, per_page=pagination.per_page, sort=sort_field, order=sort_order, status=filter_status, q=filter_query, subscribed_from=filter_subscribed_from, subscribed_to=filter_subscribed_to, expired_from=filter_expired_from, expired_to=filter_expired_to) }}" class="ml-2 text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times-circle"></i>
                    </a>
                </div>
                {% endif %}

                {% if filter_subscribed_from or filter_subscribed_to %}
                <div class="bg-gray-100 text-gray-700 px-3 py-1 rounded-lg text-sm flex items-center border border-gray-200">
                    <span>Subscribed:
                        {% if filter_subscribed_from and filter_subscribed_to %}
                            <span class="font-medium">{{ filter_subscribed_from }} to {{ filter_subscribed_to }}</span>
                        {% elif filter_subscribed_from %}
                            <span class="font-medium">From {{ filter_subscribed_from }}</span>
                        {% elif filter_subscribed_to %}
                            <span class="font-medium">Until {{ filter_subscribed_to }}</span>
                        {% endif %}
                    </span>
                    <a href="{{ tenant_url_for('dashboard.access_codes_route', page=1, per_page=pagination.per_page, sort=sort_field, order=sort_order, status=filter_status, q=filter_query, user_id=filter_user_id, expired_from=filter_expired_from, expired_to=filter_expired_to) }}" class="ml-2 text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times-circle"></i>
                    </a>
                </div>
                {% endif %}

                {% if filter_expired_from or filter_expired_to %}
                <div class="bg-gray-100 text-gray-700 px-3 py-1 rounded-lg text-sm flex items-center border border-gray-200">
                    <span>Expires:
                        {% if filter_expired_from and filter_expired_to %}
                            <span class="font-medium">{{ filter_expired_from }} to {{ filter_expired_to }}</span>
                        {% elif filter_expired_from %}
                            <span class="font-medium">From {{ filter_expired_from }}</span>
                        {% elif filter_expired_to %}
                            <span class="font-medium">Until {{ filter_expired_to }}</span>
                        {% endif %}
                    </span>
                    <a href="{{ tenant_url_for('dashboard.access_codes_route', page=1, per_page=pagination.per_page, sort=sort_field, order=sort_order, status=filter_status, q=filter_query, user_id=filter_user_id, subscribed_from=filter_subscribed_from, subscribed_to=filter_subscribed_to) }}" class="ml-2 text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times-circle"></i>
                    </a>
                </div>
                {% endif %}

                <a href="{{ tenant_url_for('dashboard.access_codes_route', page=1, per_page=pagination.per_page, sort=sort_field, order=sort_order) }}" class="bg-gray-200 text-gray-700 px-3 py-1 rounded-lg text-sm flex items-center border border-gray-300 hover:bg-gray-300 transition-colors duration-200">
                    <i class="fas fa-times mr-1.5"></i> Clear all
                </a>
            </div>

            <!-- Advanced Filter Section -->
            <div id="advancedFilterSection" class="bg-white p-5 rounded-lg border border-gray-200 mb-4 shadow-sm {% if not show_advanced_filters %}hidden{% endif %}">
                <div class="flex items-center mb-4 pb-2 border-b border-gray-100">
                    <i class="fas fa-filter text-indigo-500 mr-2"></i>
                    <h3 class="text-md font-semibold text-gray-800">Advanced Filters</h3>
                </div>
                <form id="advancedFilterForm" action="{{ tenant_url_for('dashboard.access_codes_route') }}" method="GET">
                    <!-- Preserve existing parameters -->
                    <input type="hidden" name="page" value="1">
                    <input type="hidden" name="per_page" value="{{ pagination.per_page }}">
                    <input type="hidden" name="sort" value="{{ sort_field }}">
                    <input type="hidden" name="order" value="{{ sort_order }}">
                    {% if filter_query %}
                    <input type="hidden" name="q" value="{{ filter_query }}">
                    {% endif %}

                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-5">
                        <!-- Status Filter -->
                        <div class="bg-gray-50 p-3 rounded-lg border border-gray-100">
                            <label for="statusFilter" class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                                <i class="fas fa-tag text-indigo-400 mr-2"></i>Status
                            </label>
                            <div class="relative">
                                <select id="statusFilter" name="status" class="w-full rounded-lg border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 pl-3 pr-8 py-2 appearance-none bg-white">
                                    <option value="all" {% if filter_status == 'all' %}selected{% endif %}>All Statuses</option>
                                    <option value="available" {% if filter_status == 'available' %}selected{% endif %}>Available</option>
                                    <option value="used" {% if filter_status == 'used' %}selected{% endif %}>Used (Any)</option>
                                    <option value="active" {% if filter_status == 'active' %}selected{% endif %}>Active</option>
                                    <option value="inactive" {% if filter_status == 'inactive' %}selected{% endif %}>Inactive</option>
                                </select>
                                <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-500">
                                    <i class="fas fa-chevron-down text-xs"></i>
                                </div>
                            </div>
                        </div>

                        <!-- User ID Filter -->
                        <div class="bg-gray-50 p-3 rounded-lg border border-gray-100">
                            <label for="userIdFilter" class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                                <i class="fas fa-user text-indigo-400 mr-2"></i>User ID
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-search text-gray-400"></i>
                                </div>
                                <input type="text" id="userIdFilter" name="user_id" class="w-full pl-10 pr-4 py-2 rounded-lg border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" placeholder="Filter by user ID" value="{{ filter_user_id }}">
                            </div>
                        </div>

                        <!-- Subscription Date Range -->
                        <div class="bg-gray-50 p-3 rounded-lg border border-gray-100">
                            <label class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                                <i class="fas fa-calendar-check text-indigo-400 mr-2"></i>Subscription Date
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-calendar-alt text-gray-400"></i>
                                </div>
                                <input type="text" id="subscriptionDateRange" class="w-full pl-10 pr-10 py-2 text-sm rounded-lg border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 date-range-input" placeholder="Select date range" readonly>
                                <div id="clearSubscriptionDate" class="absolute inset-y-0 right-0 pr-3 flex items-center cursor-pointer text-gray-400 hover:text-gray-600 transition-colors duration-200{% if not filter_subscribed_from and not filter_subscribed_to %} hidden{% endif %}">
                                    <i class="fas fa-times"></i>
                                </div>
                                <input type="hidden" id="subscribedFromFilter" name="subscribed_from" value="{{ filter_subscribed_from }}">
                                <input type="hidden" id="subscribedToFilter" name="subscribed_to" value="{{ filter_subscribed_to }}">
                            </div>
                        </div>

                        <!-- Expiration Date Range -->
                        <div class="bg-gray-50 p-3 rounded-lg border border-gray-100">
                            <label class="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                                <i class="fas fa-hourglass-end text-indigo-400 mr-2"></i>Expiration Date
                            </label>
                            <div class="relative">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <i class="fas fa-calendar-alt text-gray-400"></i>
                                </div>
                                <input type="text" id="expirationDateRange" class="w-full pl-10 pr-10 py-2 text-sm rounded-lg border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 date-range-input" placeholder="Select date range" readonly>
                                <div id="clearExpirationDate" class="absolute inset-y-0 right-0 pr-3 flex items-center cursor-pointer text-gray-400 hover:text-gray-600 transition-colors duration-200{% if not filter_expired_from and not filter_expired_to %} hidden{% endif %}">
                                    <i class="fas fa-times"></i>
                                </div>
                                <input type="hidden" id="expiredFromFilter" name="expired_from" value="{{ filter_expired_from }}">
                                <input type="hidden" id="expiredToFilter" name="expired_to" value="{{ filter_expired_to }}">
                            </div>
                        </div>
                    </div>

                    <div class="mt-5 flex justify-end space-x-3 border-t border-gray-100 pt-4">
                        <a href="{{ tenant_url_for('dashboard.access_codes_route', page=1, per_page=pagination.per_page, sort=sort_field, order=sort_order) }}" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors text-sm flex items-center justify-center shadow-sm">
                            <i class="fas fa-undo mr-2"></i>Reset
                        </a>
                        <button type="submit" class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors text-sm flex items-center shadow-sm hover:shadow">
                            <i class="fas fa-check mr-2"></i>Apply Filters
                        </button>
                    </div>
                </form>
            </div>
        </div>

        {% if access_codes %}
            <table class="min-w-full divide-y divide-gray-200 access-codes-table">
                <thead>
                    <tr class="bg-gray-50">
                        <!-- Add checkbox column for bulk selection -->
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                            <input type="checkbox" id="selectAllCheckbox" class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500">
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                            <a href="{{ tenant_url_for('dashboard.access_codes_route', sort='code', order='asc' if sort_field == 'code' and sort_order == 'desc' else 'desc', page=pagination.page, per_page=pagination.per_page, status=filter_status, q=filter_query) }}" class="flex items-center group hover:text-indigo-600 transition-colors">
                                Access Code
                                {% if sort_field == 'code' %}
                                    <i class="fas fa-sort-{{ 'down' if sort_order == 'desc' else 'up' }} ml-1 text-indigo-500"></i>
                                {% else %}
                                    <i class="fas fa-sort ml-1 text-gray-400 opacity-0 group-hover:opacity-100"></i>
                                {% endif %}
                            </a>
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                            <a href="{{ tenant_url_for('dashboard.access_codes_route', sort='status', order='asc' if sort_field == 'status' and sort_order == 'desc' else 'desc', page=pagination.page, per_page=pagination.per_page, status=filter_status, q=filter_query) }}" class="flex items-center group hover:text-indigo-600 transition-colors">
                                Status
                                {% if sort_field == 'status' %}
                                    <i class="fas fa-sort-{{ 'down' if sort_order == 'desc' else 'up' }} ml-1 text-indigo-500"></i>
                                {% else %}
                                    <i class="fas fa-sort ml-1 text-gray-400 opacity-0 group-hover:opacity-100"></i>
                                {% endif %}
                            </a>
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                            <a href="{{ tenant_url_for('dashboard.access_codes_route', sort='user_id', order='asc' if sort_field == 'user_id' and sort_order == 'desc' else 'desc', page=pagination.page, per_page=pagination.per_page, status=filter_status, q=filter_query) }}" class="flex items-center group hover:text-indigo-600 transition-colors">
                                User ID
                                {% if sort_field == 'user_id' %}
                                    <i class="fas fa-sort-{{ 'down' if sort_order == 'desc' else 'up' }} ml-1 text-indigo-500"></i>
                                {% else %}
                                    <i class="fas fa-sort ml-1 text-gray-400 opacity-0 group-hover:opacity-100"></i>
                                {% endif %}
                            </a>
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                            <a href="{{ tenant_url_for('dashboard.access_codes_route', sort='subscribed_on', order='asc' if sort_field == 'subscribed_on' and sort_order == 'desc' else 'desc', page=pagination.page, per_page=pagination.per_page, status=filter_status, q=filter_query) }}" class="flex items-center group hover:text-indigo-600 transition-colors">
                                Subscribed on
                                {% if sort_field == 'subscribed_on' %}
                                    <i class="fas fa-sort-{{ 'down' if sort_order == 'desc' else 'up' }} ml-1 text-indigo-500"></i>
                                {% else %}
                                    <i class="fas fa-sort ml-1 text-gray-400 opacity-0 group-hover:opacity-100"></i>
                                {% endif %}
                            </a>
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                            <a href="{{ tenant_url_for('dashboard.access_codes_route', sort='expiration_date', order='asc' if sort_field == 'expiration_date' and sort_order == 'desc' else 'desc', page=pagination.page, per_page=pagination.per_page, status=filter_status, q=filter_query) }}" class="flex items-center group hover:text-indigo-600 transition-colors">
                                Expired in
                                {% if sort_field == 'expiration_date' %}
                                    <i class="fas fa-sort-{{ 'down' if sort_order == 'desc' else 'up' }} ml-1 text-indigo-500"></i>
                                {% else %}
                                    <i class="fas fa-sort ml-1 text-gray-400 opacity-0 group-hover:opacity-100"></i>
                                {% endif %}
                            </a>
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for code in access_codes %}
                        {% set code_value = code.code %}
                        {% set is_used = code_value in used_codes %}
                        {# Use default filter for optional details passed to data attributes #}
                        {% set reg_date = code.reg_date | default('-', true) %}
                        {% set volume_usd = code.volume_mln_usd | default('-', true) %}
                        {% set trade_fn = code.trade_fn | default('-', true) %}

                        <tr class="{% if not is_used %}bg-gray-50{% endif %} hover:bg-indigo-50 transition-colors duration-150">
                            <!-- Add checkbox for each row -->
                            <td class="px-6 py-4 whitespace-nowrap">
                                <input type="checkbox" class="code-checkbox rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                                       value="{{ code_value }}"
                                       {% if is_used and (used_codes[code_value].user_status == 'active' or used_codes[code_value].status == 'active') %}disabled{% endif %}>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <a href="#"
                                   class="text-sm font-medium view-details-link font-mono {% if not is_used %}text-gray-500 hover:text-gray-700{% else %}text-indigo-600 hover:text-indigo-800 hover:underline{% endif %}"
                                   data-code="{{ code_value }}"
                                   data-reg-date="{{ reg_date }}"
                                   data-volume="{{ volume_usd }}"
                                   data-trade-fn="{{ trade_fn }}"
                                   data-expiration-date="{% if code.expiration_date %}{{ code.expiration_date.strftime('%Y-%m-%d') }}{% endif %}"
                                   data-added-at="{% if code.added_at %}{{ code.added_at.strftime('%Y-%m-%d %H:%M') }}{% endif %}">
                                    {{ code_value }}
                                </a>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                {% if is_used %}
                                    {% set user_status = used_codes[code_value].user_status|default(used_codes[code_value].status) %}
                                    {% if user_status == 'active' %}
                                        <span class="px-3 py-1 inline-flex items-center text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800 border border-green-200">
                                            <span class="w-2 h-2 bg-green-500 rounded-full mr-1.5"></span>
                                            ACTIVE
                                        </span>
                                    {% elif user_status == 'inactive' %}
                                        <span class="px-3 py-1 inline-flex items-center text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800 border border-red-200">
                                            <span class="w-2 h-2 bg-red-500 rounded-full mr-1.5"></span>
                                            INACTIVE
                                        </span>
                                    {% elif user_status == 'pending_verification' %}
                                        <span class="px-3 py-1 inline-flex items-center text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800 border border-yellow-200">
                                            <span class="w-2 h-2 bg-yellow-500 rounded-full mr-1.5"></span>
                                            PENDING VERIFICATION
                                        </span>
                                    {% elif user_status == 'incomplete' %}
                                        <span class="px-3 py-1 inline-flex items-center text-xs leading-5 font-semibold rounded-full bg-orange-100 text-orange-800 border border-orange-200">
                                            <span class="w-2 h-2 bg-orange-500 rounded-full mr-1.5"></span>
                                            INCOMPLETE
                                        </span>
                                    {% elif user_status == 'new' %}
                                        <span class="px-3 py-1 inline-flex items-center text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800 border border-blue-200">
                                            <span class="w-2 h-2 bg-blue-500 rounded-full mr-1.5"></span>
                                            NEW
                                        </span>
                                    {% else %}
                                        <span class="px-3 py-1 inline-flex items-center text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800 border border-gray-200">
                                            <span class="w-2 h-2 bg-gray-500 rounded-full mr-1.5"></span>
                                            {{ user_status|replace('_', ' ')|upper }}
                                        </span>
                                    {% endif %}
                                {% else %}
                                    <span class="px-3 py-1 inline-flex items-center text-xs leading-5 font-semibold rounded-full bg-indigo-100 text-indigo-800 border border-indigo-200">
                                        <span class="w-2 h-2 bg-indigo-500 rounded-full mr-1.5"></span>
                                        AVAILABLE
                                    </span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm {% if not is_used %}text-gray-500{% else %}text-gray-900{% endif %}">
                                    {% if is_used %}
                                        <a href="{{ tenant_url_for('dashboard.user_detail_route', user_id=used_codes[code_value].user_id) }}" class="text-indigo-600 hover:text-indigo-800 hover:underline font-medium flex items-center">
                                            <i class="fas fa-user text-gray-400 mr-1.5"></i>
                                            {{ used_codes[code_value].user_id }}
                                        </a>
                                    {% else %}
                                        <span class="text-gray-400 italic">-</span>
                                    {% endif %}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm {% if not is_used %}text-gray-500{% else %}text-gray-900{% endif %}">
                                {% if is_used %}
                                    <div class="flex items-center">
                                        <i class="far fa-calendar-alt text-gray-400 mr-1.5"></i>
                                        {{ used_codes[code_value].subscription_time.strftime('%Y-%m-%d %H:%M:%S') }}
                                    </div>
                                {% else %}
                                    <span class="text-gray-400 italic">-</span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                {% if code.expiration_date %}
                                    {% set expiration_date_aware = code.expiration_date.replace(tzinfo=now.tzinfo) if code.expiration_date.tzinfo is none else code.expiration_date %}
                                    {% set expiration_date_only = expiration_date_aware.date() %}
                                    {% set current_date_only = now.date() %}
                                    {% set delta = (expiration_date_only - current_date_only).days %}
                                    {% set days_remaining = delta if delta > 0 else 0 %}
                                    {% if days_remaining > 0 %}
                                        <span class="px-3 py-1 inline-flex items-center text-xs leading-5 font-semibold rounded-full {% if days_remaining <= 7 %}bg-red-100 text-red-800 border border-red-200{% elif days_remaining <= 30 %}bg-amber-100 text-amber-800 border border-amber-200{% else %}bg-green-100 text-green-800 border border-green-200{% endif %}">
                                            <i class="fas fa-clock text-gray-400 mr-1.5"></i>
                                            {{ days_remaining }} day{% if days_remaining != 1 %}s{% endif %}
                                        </span>
                                    {% else %}
                                        <span class="px-3 py-1 inline-flex items-center text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800 border border-red-200">
                                            <i class="fas fa-exclamation-circle text-red-500 mr-1.5"></i>
                                            Expired
                                        </span>
                                    {% endif %}
                                {% else %}
                                    <span class="text-gray-400 italic">-</span>
                                {% endif %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex space-x-2">
                                    {% if not is_used or (is_used and used_codes[code_value].user_status != 'active' and used_codes[code_value].status != 'active') %}
                                        <!-- Full edit for unused or inactive codes -->
                                        <button class="p-1.5 bg-indigo-50 text-indigo-600 rounded-lg hover:bg-indigo-100 transition-colors duration-200 edit-code"
                                                data-code="{{ code_value }}"
                                                data-expiration-days="{{ code.expiration_days|default('', true) }}"
                                                data-is-active="false"
                                                title="Edit Code">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    {% else %}
                                        <!-- Expiration-only edit for active codes -->
                                        <button class="p-1.5 bg-amber-50 text-amber-600 rounded-lg hover:bg-amber-100 transition-colors duration-200 edit-code"
                                                data-code="{{ code_value }}"
                                                data-expiration-days="{{ code.expiration_days|default('', true) }}"
                                                data-is-active="true"
                                                title="Edit Expiration Only">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                    {% endif %}
                                    <button class="p-1.5 bg-red-50 text-red-600 rounded-lg hover:bg-red-100 transition-colors duration-200 delete-code" data-code="{{ code_value }}" title="Delete Code">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                    <button class="p-1.5 bg-indigo-50 text-indigo-600 rounded-lg hover:bg-indigo-100 transition-colors duration-200 view-details-link"
                                            data-code="{{ code_value }}"
                                            data-reg-date="{{ reg_date }}"
                                            data-volume="{{ volume_usd }}"
                                            data-trade-fn="{{ trade_fn }}"
                                            data-expiration-date="{% if code.expiration_date %}{{ code.expiration_date.strftime('%Y-%m-%d') }}{% endif %}"
                                            data-added-at="{% if code.added_at %}{{ code.added_at.strftime('%Y-%m-%d %H:%M') }}{% endif %}"
                                            title="View Details">
                                        <i class="fas fa-info-circle"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>

            <!-- Pagination controls -->
            <div class="px-6 py-4 flex items-center justify-between border-t border-gray-200 bg-gray-50 access-codes-pagination">
                <div class="flex items-center">
                    <span class="text-sm text-gray-700">
                        Showing <span class="font-medium text-indigo-600">{{ (pagination.page - 1) * pagination.per_page + 1 }}</span> to
                        <span class="font-medium text-indigo-600">{{ end_item }}</span> of
                        <span class="font-medium text-indigo-600">{{ pagination.total }}</span> results
                    </span>

                    <div class="ml-4">
                        <label for="perPageSelect" class="sr-only">Items per page</label>
                        <div class="relative">
                            <select id="perPageSelect" class="appearance-none pl-3 pr-8 py-1.5 rounded-lg border border-gray-300 text-gray-700 bg-white hover:border-indigo-500 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm transition-colors">
                                {% for option in pagination.allowed_per_page %}
                                    <option value="{{ option }}" {% if pagination.per_page == option %}selected{% endif %}>
                                        {{ option }} per page
                                    </option>
                                {% endfor %}
                            </select>
                            <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-500">
                                <i class="fas fa-chevron-down text-xs"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Hidden form to preserve sorting parameters when changing per_page -->
                    <form id="perPageForm" method="get" action="{{ tenant_url_for('dashboard.access_codes_route') }}" class="hidden">
                        <input type="hidden" name="per_page" id="perPageInput" value="{{ pagination.per_page }}">
                        <input type="hidden" name="page" value="1"> <!-- Reset to page 1 when changing per_page -->
                        <input type="hidden" name="sort" value="{{ sort_field }}">
                        <input type="hidden" name="order" value="{{ sort_order }}">
                        <input type="hidden" name="status" value="{{ filter_status }}">
                        <input type="hidden" name="q" value="{{ filter_query }}">
                    </form>
                </div>

                <div class="flex items-center space-x-2">
                    <a href="{{ tenant_url_for('dashboard.access_codes_route', page=pagination.page-1, per_page=pagination.per_page, sort=sort_field, order=sort_order, status=filter_status, q=filter_query) }}"
                       class="px-4 py-2 rounded-lg flex items-center {% if pagination.has_prev %}border border-indigo-300 bg-white text-indigo-700 hover:bg-indigo-50 transition-colors duration-200{% else %}border border-gray-200 bg-gray-100 text-gray-400 cursor-not-allowed{% endif %}"
                       {% if not pagination.has_prev %}disabled{% endif %}>
                        <i class="fas fa-chevron-left mr-1 text-xs"></i> Previous
                    </a>

                    <div class="px-3 py-1 bg-white border border-gray-300 rounded-lg text-sm font-medium text-gray-700">
                        Page {{ pagination.page }} of {{ pagination.total_pages }}
                    </div>

                    <a href="{{ tenant_url_for('dashboard.access_codes_route', page=pagination.page+1, per_page=pagination.per_page, sort=sort_field, order=sort_order, status=filter_status, q=filter_query) }}"
                       class="px-4 py-2 rounded-lg flex items-center {% if pagination.has_next %}border border-indigo-300 bg-white text-indigo-700 hover:bg-indigo-50 transition-colors duration-200{% else %}border border-gray-200 bg-gray-100 text-gray-400 cursor-not-allowed{% endif %}"
                       {% if not pagination.has_next %}disabled{% endif %}>
                        Next <i class="fas fa-chevron-right ml-1 text-xs"></i>
                    </a>
                </div>
            </div>
        {% else %}
            <div class="px-6 py-12 text-center access-codes-empty">
                <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-indigo-100 text-indigo-400 mb-4">
                    <i class="fas fa-key text-2xl"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No access codes found</h3>
                <p class="text-gray-500 max-w-md mx-auto mb-6">Get started by adding a new access code or uploading a file with multiple codes.</p>
                <div class="flex justify-center space-x-4">
                    <button id="emptyAddCodeBtn" class="px-4 py-2 bg-indigo-500 text-white rounded-lg hover:bg-indigo-600 transition-all duration-200 shadow-sm hover:shadow flex items-center transform hover:-translate-y-0.5">
                        <i class="fas fa-plus mr-2"></i>Add Code
                    </button>
                    <button id="emptyUploadFileBtn" class="px-4 py-2 bg-emerald-500 text-white rounded-lg hover:bg-emerald-600 transition-all duration-200 shadow-sm hover:shadow flex items-center transform hover:-translate-y-0.5">
                        <i class="fas fa-upload mr-2"></i>Upload File
                    </button>
                </div>
            </div>
        {% endif %}
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteConfirmModal" class="fixed inset-0 bg-gray-900 bg-opacity-50 backdrop-blur-sm flex items-center justify-center hidden z-50">
    <div class="bg-white rounded-xl shadow-xl p-6 w-full max-w-md transform transition-all duration-300 scale-100">
        <div class="flex justify-between items-center mb-5">
            <h3 class="text-xl font-semibold text-gray-800 flex items-center">
                <i class="fas fa-trash-alt text-red-500 mr-3"></i>
                <span id="deleteModalTitle">Confirm Deletion</span>
            </h3>
            <button class="text-gray-400 hover:text-gray-600 transition-colors duration-200 closeModal rounded-full hover:bg-gray-100 p-2">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <div class="mb-6">
            <div class="flex items-center mb-4 text-red-600">
                <div class="bg-red-100 p-2 rounded-full mr-3">
                    <i class="fas fa-exclamation-triangle text-xl"></i>
                </div>
                <h4 class="text-lg font-medium">Delete Access Code</h4>
            </div>

            <p id="deleteConfirmMessage" class="mb-4 text-gray-700">Are you sure you want to delete this access code?</p>

            <div id="deleteUserInfo" class="mb-4 p-3 bg-amber-50 text-amber-800 rounded-lg border border-amber-200 hidden">
                <div class="flex items-start">
                    <i class="fas fa-info-circle mt-0.5 mr-2"></i>
                    <div>
                        <p class="font-medium">Active Code Warning</p>
                        <p class="text-sm">This code is currently in use. Deleting it will revoke access for the user.</p>
                        <p id="deleteUserDetails" class="text-sm mt-1 font-medium"></p>
                    </div>
                </div>
            </div>

            <div id="deleteSelectedCount" class="mb-4 p-3 bg-indigo-50 text-indigo-800 rounded-lg border border-indigo-200 hidden">
                <div class="flex items-start">
                    <i class="fas fa-info-circle mt-0.5 mr-2"></i>
                    <div>
                        <p class="font-medium">Bulk Delete</p>
                        <p class="text-sm">You are about to delete <span id="selectedCodeCount" class="font-medium">0</span> access codes.</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="flex justify-end space-x-3">
            <button type="button" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors duration-200 closeModal">Cancel</button>
            <button type="button" id="confirmDeleteBtn" class="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors duration-200 flex items-center">
                <i class="fas fa-trash-alt mr-2"></i> Delete
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Litepicker Date Range Picker Library -->
<script src="https://cdn.jsdelivr.net/npm/litepicker/dist/litepicker.js"></script>
<script src="https://cdn.jsdelivr.net/npm/litepicker/dist/plugins/ranges.js"></script>
<script>

    document.addEventListener('DOMContentLoaded', function() {
        // Helper function to show code details modal
        function showCodeDetails(code, regDate, volume, tradeFn, expirationDate, addedAt) {
            // Populate modal with details
            document.getElementById('detailCode').textContent = code || 'N/A';
            document.getElementById('detailRegDate').textContent = regDate || 'N/A';
            document.getElementById('detailVolume').textContent = volume || 'N/A';
            document.getElementById('detailTradeFn').textContent = tradeFn || 'N/A';
            document.getElementById('detailAddedAt').querySelector('span').textContent = addedAt || 'Not available';
            document.getElementById('detailExpirationDate').querySelector('span').textContent = expirationDate || 'Not set';

            // Show the modal
            const codeDetailsModal = document.getElementById('codeDetailsModal');
            codeDetailsModal.classList.remove('hidden');
        }

        // Add mobile-specific functionality
        if (window.innerWidth <= 768) {
            // Make rows clickable on mobile (except for checkboxes and action buttons)
            const tableRows = document.querySelectorAll('.access-codes-table tbody tr');
            tableRows.forEach(row => {
                row.addEventListener('click', function(e) {
                    // Don't trigger if clicking on checkbox, action buttons, or links
                    if (!e.target.closest('td:first-child') &&
                        !e.target.closest('td:last-child') &&
                        !e.target.closest('a') &&
                        !e.target.closest('button') &&
                        !e.target.closest('input[type="checkbox"]')) {

                        // Find the view details link and trigger it
                        const viewDetailsLink = this.querySelector('.view-details-link');
                        if (viewDetailsLink) {
                            // Extract data attributes
                            const code = viewDetailsLink.getAttribute('data-code');
                            const regDate = viewDetailsLink.getAttribute('data-reg-date');
                            const volume = viewDetailsLink.getAttribute('data-volume');
                            const tradeFn = viewDetailsLink.getAttribute('data-trade-fn');
                            const expirationDate = viewDetailsLink.getAttribute('data-expiration-date');
                            const addedAt = viewDetailsLink.getAttribute('data-added-at');

                            // Show details modal
                            showCodeDetails(code, regDate, volume, tradeFn, expirationDate, addedAt);
                        }
                    }
                });

                // Add cursor pointer to indicate clickable
                row.style.cursor = 'pointer';
            });
        }

        // Modal buttons
        const addCodeBtn = document.getElementById('addCodeBtn');
        const uploadFileBtn = document.getElementById('uploadFileBtn');
        const emptyAddCodeBtn = document.getElementById('emptyAddCodeBtn');
        const emptyUploadFileBtn = document.getElementById('emptyUploadFileBtn');
        const addCodeModal = document.getElementById('addCodeModal');
        const uploadFileModal = document.getElementById('uploadFileModal');
        const searchInput = document.getElementById('searchInput');

        // Add event listeners for modal buttons
        if (addCodeBtn) {
            addCodeBtn.addEventListener('click', function() {
                showAddCodeModal();
            });
        }

        if (emptyAddCodeBtn) {
            emptyAddCodeBtn.addEventListener('click', function() {
                showAddCodeModal();
            });
        }

        if (uploadFileBtn) {
            uploadFileBtn.addEventListener('click', function() {
                showUploadFileModal();
            });
        }

        if (emptyUploadFileBtn) {
            emptyUploadFileBtn.addEventListener('click', function() {
                showUploadFileModal();
            });
        }

        function showAddCodeModal() {
            // Add entrance animation
            const modalContent = addCodeModal.querySelector('.bg-white');
            modalContent.classList.add('scale-95', 'opacity-0');
            addCodeModal.classList.remove('hidden');

            setTimeout(() => {
                modalContent.classList.remove('scale-95', 'opacity-0');
                modalContent.classList.add('scale-100', 'opacity-100');
            }, 10);
        }

        function showUploadFileModal() {
            // Add entrance animation
            const modalContent = uploadFileModal.querySelector('.bg-white');
            modalContent.classList.add('scale-95', 'opacity-0');
            uploadFileModal.classList.remove('hidden');

            setTimeout(() => {
                modalContent.classList.remove('scale-95', 'opacity-0');
                modalContent.classList.add('scale-100', 'opacity-100');
            }, 10);
        }

        // File upload handling
        const fileInput = document.getElementById('codesFile');
        const fileDropArea = document.getElementById('fileDropArea');
        const fileIcon = document.getElementById('fileIcon');
        const uploadPrompt = document.getElementById('uploadPrompt');
        const fileDetails = document.getElementById('fileDetails');
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');

        if (fileInput) {
            // Handle file selection
            fileInput.addEventListener('change', function() {
                if (this.files && this.files[0]) {
                    const file = this.files[0];
                    displayFileDetails(file);
                }
            });

            // Handle drag and drop
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                fileDropArea.addEventListener(eventName, preventDefaults, false);
            });

            function preventDefaults(e) {
                e.preventDefault();
                e.stopPropagation();
            }

            ['dragenter', 'dragover'].forEach(eventName => {
                fileDropArea.addEventListener(eventName, highlight, false);
            });

            ['dragleave', 'drop'].forEach(eventName => {
                fileDropArea.addEventListener(eventName, unhighlight, false);
            });

            function highlight() {
                fileDropArea.classList.add('border-emerald-400', 'bg-emerald-50');
            }

            function unhighlight() {
                fileDropArea.classList.remove('border-emerald-400', 'bg-emerald-50');
            }

            fileDropArea.addEventListener('drop', handleDrop, false);

            function handleDrop(e) {
                const dt = e.dataTransfer;
                const file = dt.files[0];
                fileInput.files = dt.files;
                displayFileDetails(file);
            }

            function displayFileDetails(file) {
                // Update UI to show file details
                uploadPrompt.classList.add('hidden');
                fileDetails.classList.remove('hidden');

                // Update file icon based on type
                if (file.name.endsWith('.csv')) {
                    fileIcon.className = 'fas fa-file-csv text-emerald-500 text-3xl mb-3';
                } else if (file.name.endsWith('.txt')) {
                    fileIcon.className = 'fas fa-file-alt text-emerald-500 text-3xl mb-3';
                } else {
                    fileIcon.className = 'fas fa-file text-emerald-500 text-3xl mb-3';
                }

                // Display file name and size
                fileName.textContent = file.name;

                // Format file size
                let size = file.size;
                let sizeDisplay = '';
                if (size < 1024) {
                    sizeDisplay = size + ' bytes';
                } else if (size < 1024 * 1024) {
                    sizeDisplay = (size / 1024).toFixed(1) + ' KB';
                } else {
                    sizeDisplay = (size / (1024 * 1024)).toFixed(1) + ' MB';
                }

                fileSize.textContent = sizeDisplay;

                // Add a reset button
                const resetButton = document.createElement('button');
                resetButton.type = 'button';
                resetButton.className = 'text-xs text-red-500 hover:text-red-700 mt-2';
                resetButton.textContent = 'Remove file';
                resetButton.onclick = function() {
                    // Reset the file input
                    fileInput.value = '';

                    // Reset the UI
                    fileIcon.className = 'fas fa-file-upload text-gray-400 text-3xl mb-3';
                    uploadPrompt.classList.remove('hidden');
                    fileDetails.classList.add('hidden');

                    // Remove the reset button
                    if (this.parentNode) {
                        this.parentNode.removeChild(this);
                    }
                };

                // Add the reset button if it doesn't exist
                if (!document.querySelector('#fileDetails button')) {
                    fileDetails.appendChild(resetButton);
                }
            }
        }

        // Server-side search functionality with debounce
        if (searchInput) {
            let debounceTimer;

            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.trim();

                // Clear any existing timer
                clearTimeout(debounceTimer);

                // Set a new timer to delay the search
                debounceTimer = setTimeout(() => {
                    // If search term is empty or very short, use client-side filtering
                    if (searchTerm.length < 2) {
                        // Reset to current page without search query
                        if (window.location.search.includes('q=')) {
                            const url = new URL(window.location.href);
                            url.searchParams.delete('q');
                            window.location.href = url.toString();
                        } else {
                            // Just do client-side filtering for empty/short queries
                            const rows = document.querySelectorAll('tbody tr');
                            rows.forEach(row => row.style.display = '');
                        }
                        return;
                    }

                    // For longer search terms, perform server-side search
                    const url = new URL(window.location.href);
                    url.searchParams.set('q', searchTerm);
                    url.searchParams.set('page', '1'); // Reset to first page for new search

                    // Show loading indicator
                    const tableBody = document.querySelector('tbody');
                    if (tableBody) {
                        const loadingRow = document.createElement('tr');
                        loadingRow.id = 'searchLoadingIndicator';
                        loadingRow.innerHTML = `
                            <td colspan="6" class="px-6 py-8 text-center">
                                <div class="flex justify-center items-center">
                                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-500 mr-3"></div>
                                    <span class="text-gray-600">Searching...</span>
                                </div>
                            </td>
                        `;

                        // Only add if not already present
                        if (!document.getElementById('searchLoadingIndicator')) {
                            tableBody.appendChild(loadingRow);
                        }
                    }

                    // Navigate to the search URL
                    window.location.href = url.toString();
                }, 500); // 500ms debounce delay
            });

            // If there's already a search query in the URL, highlight it in the input
            const urlParams = new URLSearchParams(window.location.search);
            const queryParam = urlParams.get('q');
            if (queryParam) {
                searchInput.value = queryParam;
                searchInput.classList.add('bg-yellow-50', 'border-yellow-300');
                setTimeout(() => {
                    searchInput.classList.remove('bg-yellow-50', 'border-yellow-300');
                }, 1500);

                // Add event listener for the clear search button
                const clearSearchBtn = document.getElementById('clearSearch');
                if (clearSearchBtn) {
                    clearSearchBtn.addEventListener('click', function() {
                        // Clear the search input
                        searchInput.value = '';

                        // Remove the search parameter from the URL and reload
                        const url = new URL(window.location.href);
                        url.searchParams.delete('q');
                        url.searchParams.set('page', '1'); // Reset to first page
                        window.location.href = url.toString();
                    });
                }
            }
        }

        // Add event listener for view details links
        const viewDetailsLinks = document.querySelectorAll('.view-details-link');
        const codeDetailsModal = document.getElementById('codeDetailsModal');

        viewDetailsLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();

                // Get data attributes
                const code = this.getAttribute('data-code');
                const regDate = this.getAttribute('data-reg-date');
                const volume = this.getAttribute('data-volume');
                const tradeFn = this.getAttribute('data-trade-fn');
                const expirationDate = this.getAttribute('data-expiration-date');
                const addedAt = this.getAttribute('data-added-at');

                // Use the showCodeDetails helper function
                showCodeDetails(code, regDate, volume, tradeFn, expirationDate, addedAt);
            });
        });

        // Delete button functionality
        const deleteButtons = document.querySelectorAll('.delete-code');
        const editButtons = document.querySelectorAll('.edit-code');
        const editCodeModal = document.getElementById('editCodeModal');
        const originalCodeInput = document.getElementById('originalCode');
        const editAccessCodeInput = document.getElementById('editAccessCode');

        // Delete button functionality
        deleteButtons.forEach(button => {
            button.addEventListener('click', function() {
                const code = this.getAttribute('data-code');
                deleteCode(code, false);
            });
        });

        // Edit button functionality
        editButtons.forEach(button => {
            button.addEventListener('click', function() {
                const code = this.getAttribute('data-code');
                const expirationDays = this.getAttribute('data-expiration-days');
                const isActive = this.getAttribute('data-is-active') === 'true';
                console.log('Edit button clicked for code:', code, 'isActive:', isActive);

                // Set the original code value in the form
                originalCodeInput.value = code;
                editAccessCodeInput.value = code;

                // Set expiration days if available
                const editExpirationDaysInput = document.getElementById('editExpirationDays');
                if (editExpirationDaysInput && expirationDays) {
                    editExpirationDaysInput.value = expirationDays;
                }

                // If code is active, disable the code field and show a message
                if (isActive) {
                    // Instead of disabling the field (which prevents form submission),
                    // make it read-only and add a hidden field to ensure the form submits properly
                    editAccessCodeInput.readOnly = true;
                    editAccessCodeInput.classList.add('bg-gray-100');

                    // Create or update a hidden input to ensure the form submits with the original code
                    let hiddenCodeInput = document.getElementById('hiddenNewCode');
                    if (!hiddenCodeInput) {
                        hiddenCodeInput = document.createElement('input');
                        hiddenCodeInput.type = 'hidden';
                        hiddenCodeInput.id = 'hiddenNewCode';
                        hiddenCodeInput.name = 'new_code';
                        editAccessCodeInput.parentNode.appendChild(hiddenCodeInput);

                        // Change the visible input's name so it doesn't conflict
                        editAccessCodeInput.name = 'display_code';
                    }
                    hiddenCodeInput.value = code;

                    // Add a note about active codes if it doesn't exist
                    if (!document.getElementById('activeCodeNote')) {
                        const noteDiv = document.createElement('div');
                        noteDiv.id = 'activeCodeNote';
                        noteDiv.className = 'mb-4 p-3 bg-amber-50 text-amber-800 rounded-lg border border-amber-200';
                        noteDiv.innerHTML = `
                            <div class="flex items-start">
                                <i class="fas fa-info-circle mt-0.5 mr-2"></i>
                                <div>
                                    <p class="font-medium">Active Code</p>
                                    <p class="text-sm">This code is currently in use. You can only modify the expiration date.</p>
                                </div>
                            </div>
                        `;

                        // Insert after the title
                        const modalTitle = editCodeModal.querySelector('h3').parentNode;
                        modalTitle.parentNode.insertBefore(noteDiv, modalTitle.nextSibling);
                    }
                } else {
                    // For inactive codes, ensure the code field is enabled and properly configured
                    editAccessCodeInput.readOnly = false;
                    editAccessCodeInput.disabled = false;
                    editAccessCodeInput.classList.remove('bg-gray-100');
                    editAccessCodeInput.name = 'new_code'; // Ensure correct name

                    // Remove the hidden input if it exists
                    const hiddenCodeInput = document.getElementById('hiddenNewCode');
                    if (hiddenCodeInput) {
                        hiddenCodeInput.remove();
                    }

                    // Remove the note if it exists
                    const activeCodeNote = document.getElementById('activeCodeNote');
                    if (activeCodeNote) {
                        activeCodeNote.remove();
                    }
                }

                // Show the edit modal
                editCodeModal.classList.remove('hidden');
            });
        });

        // Pagination per-page selector
        const perPageSelect = document.getElementById('perPageSelect');
        if (perPageSelect) {
            perPageSelect.addEventListener('change', function() {
                const perPage = this.value;
                const currentUrl = new URL(window.location.href);

                // Update the per_page parameter and reset to page 1
                currentUrl.searchParams.set('per_page', perPage);
                currentUrl.searchParams.set('page', 1);

                // Navigate to the new URL
                window.location.href = currentUrl.toString();
            });
        }

        // Bulk delete functionality
        const bulkDeleteBtn = document.getElementById('bulkDeleteBtn');
        const selectAllCheckbox = document.getElementById('selectAllCheckbox');
        const codeCheckboxes = document.querySelectorAll('.code-checkbox');

        // Enable/disable bulk delete button based on checkbox selection
        function updateBulkDeleteButton() {
            const checkedBoxes = document.querySelectorAll('.code-checkbox:checked');
            const selectedCount = document.getElementById('selectedCount');
            const count = checkedBoxes.length;

            // Update button state
            bulkDeleteBtn.disabled = count === 0;

            // Update count display
            if (count > 0) {
                selectedCount.textContent = count;
                selectedCount.classList.remove('hidden');
            } else {
                selectedCount.classList.add('hidden');
            }

            // Update "select all" checkbox state
            const allCheckboxes = document.querySelectorAll('.code-checkbox:not([disabled])');
            selectAllCheckbox.checked = allCheckboxes.length > 0 && count === allCheckboxes.length;
            selectAllCheckbox.indeterminate = count > 0 && count < allCheckboxes.length;
        }

        // Add event listeners to checkboxes
        codeCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', updateBulkDeleteButton);
        });

        // Select all checkbox functionality
        selectAllCheckbox.addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.code-checkbox:not([disabled])');
            checkboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateBulkDeleteButton();
        });

        // Delete confirmation modal elements
        const deleteConfirmModal = document.getElementById('deleteConfirmModal');
        const deleteConfirmMessage = document.getElementById('deleteConfirmMessage');
        const deleteUserInfo = document.getElementById('deleteUserInfo');
        const deleteUserDetails = document.getElementById('deleteUserDetails');
        const deleteSelectedCount = document.getElementById('deleteSelectedCount');
        const selectedCodeCount = document.getElementById('selectedCodeCount');
        const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
        const deleteModalTitle = document.getElementById('deleteModalTitle');

        // Variables to store current delete operation
        let currentDeleteCode = null;
        let currentDeleteForce = false;
        let currentDeleteBulk = false;
        let selectedCodesToDelete = [];

        // Show delete confirmation modal
        function showDeleteConfirmModal(options) {
            // Reset modal state
            deleteUserInfo.classList.add('hidden');
            deleteSelectedCount.classList.add('hidden');
            confirmDeleteBtn.disabled = false;

            if (options.bulk) {
                // Bulk delete mode
                currentDeleteBulk = true;
                currentDeleteCode = null;
                selectedCodesToDelete = options.codes;

                // Update modal content for bulk delete
                deleteModalTitle.textContent = 'Confirm Bulk Deletion';
                deleteConfirmMessage.textContent = 'Are you sure you want to delete the selected access codes?';
                selectedCodeCount.textContent = options.codes.length;
                deleteSelectedCount.classList.remove('hidden');
            } else {
                // Single delete mode
                currentDeleteBulk = false;
                currentDeleteCode = options.code;
                currentDeleteForce = options.force || false;
                selectedCodesToDelete = [];

                // Update modal content for single delete
                deleteModalTitle.textContent = 'Confirm Deletion';
                deleteConfirmMessage.textContent = currentDeleteForce
                    ? `Are you sure you want to forcibly delete the access code '${options.code}', even though it might be in use?`
                    : `Are you sure you want to delete the access code '${options.code}'?`;

                // Show user info if provided
                if (options.userInfo) {
                    deleteUserDetails.textContent = options.userInfo;
                    deleteUserInfo.classList.remove('hidden');
                }
            }

            // Show the modal with animation
            const modalContent = deleteConfirmModal.querySelector('.bg-white');
            modalContent.classList.add('scale-95', 'opacity-0');
            deleteConfirmModal.classList.remove('hidden');

            setTimeout(() => {
                modalContent.classList.remove('scale-95', 'opacity-0');
                modalContent.classList.add('scale-100', 'opacity-100');
            }, 10);
        }

        // Bulk delete button functionality
        bulkDeleteBtn.addEventListener('click', function() {
            const selectedCodes = Array.from(document.querySelectorAll('.code-checkbox:checked')).map(cb => cb.value);

            if (selectedCodes.length === 0) {
                alert('Please select at least one code to delete.');
                return;
            }

            // Show delete confirmation modal
            showDeleteConfirmModal({
                bulk: true,
                codes: selectedCodes
            });
        });

        // Close modal buttons with animation
        function closeModal(modal) {
            const modalContent = modal.querySelector('.bg-white');
            modalContent.classList.remove('scale-100');
            modalContent.classList.add('scale-95', 'opacity-0');

            setTimeout(() => {
                modal.classList.add('hidden');
                modalContent.classList.remove('scale-95', 'opacity-0');

                // If this is the edit modal, reset it
                if (modal.id === 'editCodeModal') {
                    // Remove the active code note if it exists
                    const activeCodeNote = document.getElementById('activeCodeNote');
                    if (activeCodeNote) {
                        activeCodeNote.remove();
                    }

                    // Reset the access code input
                    const editAccessCodeInput = document.getElementById('editAccessCode');
                    if (editAccessCodeInput) {
                        editAccessCodeInput.readOnly = false;
                        editAccessCodeInput.disabled = false;
                        editAccessCodeInput.classList.remove('bg-gray-100');
                        editAccessCodeInput.name = 'new_code'; // Restore original name
                    }

                    // Remove the hidden input if it exists
                    const hiddenCodeInput = document.getElementById('hiddenNewCode');
                    if (hiddenCodeInput) {
                        hiddenCodeInput.remove();
                    }
                }
            }, 200);
        }

        document.querySelectorAll('.closeModal').forEach(button => {
            button.addEventListener('click', function() {
                // Find the parent modal
                const modal = this.closest('.fixed');
                if (modal) {
                    closeModal(modal);
                }
            });
        });

        // Close modals when clicking outside
        document.querySelectorAll('.fixed').forEach(modal => {
            modal.addEventListener('click', function(e) {
                if (e.target === this) {
                    closeModal(this);
                }
            });
        });

        // Close modals with escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const visibleModals = document.querySelectorAll('.fixed:not(.hidden)');
                visibleModals.forEach(modal => {
                    closeModal(modal);
                });
            }
        });

        // Add event listeners to delete buttons
        document.querySelectorAll('.delete-code').forEach(button => {
            button.addEventListener('click', function() {
                const code = this.getAttribute('data-code');
                if (code) {
                    // Show delete confirmation modal
                    showDeleteConfirmModal({
                        bulk: false,
                        code: code,
                        force: false
                    });
                }
            });
        });

        // Confirm delete button event listener
        confirmDeleteBtn.addEventListener('click', function() {
            if (currentDeleteBulk) {
                // Handle bulk delete
                executeBulkDelete(selectedCodesToDelete);
            } else {
                // Handle single delete
                executeDeleteCode(currentDeleteCode, currentDeleteForce);
            }
        });

        // Execute single delete
        function executeDeleteCode(code, force = false) {
            // Disable the confirm button to prevent multiple clicks
            confirmDeleteBtn.disabled = true;
            confirmDeleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Deleting...';

            fetch("{{ tenant_url_for('dashboard.delete_access_code_route') }}", {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ code, force })
            })
            .then(response => {
                // Check if response is okay before parsing JSON
                if (!response.ok && response.status !== 200) {
                    // Try to get error message from response body if possible
                    return response.json().then(errData => {
                        throw new Error(errData.message || `HTTP error! status: ${response.status}`);
                    }).catch(() => {
                        // Fallback if response body is not JSON or empty
                        throw new Error(`HTTP error! status: ${response.status}`);
                    });
                }
                return response.json();
            })
            .then(data => {
                if (data.requires_confirmation) {
                    // Code is in use, close current modal and show new one with force option
                    closeModal(deleteConfirmModal);

                    // Wait for modal to close before showing new one
                    setTimeout(() => {
                        const userMsg = data.user_id ? `by user ${data.user_id}` : '';
                        showDeleteConfirmModal({
                            bulk: false,
                            code: code,
                            force: true,
                            userInfo: `${data.message} ${userMsg}`
                        });
                    }, 300);
                } else {
                    // Show result message and reload on success
                    closeModal(deleteConfirmModal);

                    setTimeout(() => {
                        if (data.success) {
                            showToast('success', 'Success', data.message);
                            // Reload after a short delay to allow the user to see the toast
                            setTimeout(() => location.reload(), 1500);
                        } else {
                            showToast('error', 'Error', data.message);
                        }
                    }, 300);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                closeModal(deleteConfirmModal);

                setTimeout(() => {
                    showToast('error', 'Error', error.toString());
                }, 300);
            });
        }

        // Execute bulk delete
        function executeBulkDelete(codes) {
            // Disable the confirm button to prevent multiple clicks
            confirmDeleteBtn.disabled = true;
            confirmDeleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Deleting...';

            // Send bulk delete request
            fetch("{{ tenant_url_for('dashboard.bulk_delete_access_codes_route') }}", {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ codes: codes })
            })
            .then(response => response.json())
            .then(data => {
                closeModal(deleteConfirmModal);

                setTimeout(() => {
                    if (data.success) {
                        showToast('success', 'Success', data.message);
                        // Reload after a short delay to allow the user to see the toast
                        setTimeout(() => location.reload(), 1500);
                    } else {
                        showToast('error', 'Error', data.message);
                    }
                }, 300);
            })
            .catch(error => {
                console.error('Error:', error);
                closeModal(deleteConfirmModal);

                setTimeout(() => {
                    showToast('error', 'Error', 'Error deleting selected codes: ' + error.toString());
                }, 300);
            });
        }

        // Per page selector
        document.getElementById('perPageSelect').addEventListener('change', function() {
            // Update the hidden form input and submit the form to preserve all parameters
            document.getElementById('perPageInput').value = this.value;
            document.getElementById('perPageForm').submit();
        });

        // Advanced Filter Toggle
        const advancedFilterToggle = document.getElementById('advancedFilterToggle');
        const advancedFilterSection = document.getElementById('advancedFilterSection');

        if (advancedFilterToggle && advancedFilterSection) {
            advancedFilterToggle.addEventListener('click', function() {
                advancedFilterSection.classList.toggle('hidden');

                // Change button text based on visibility
                const buttonText = advancedFilterSection.classList.contains('hidden') ? 'Show Filters' : 'Hide Filters';
                this.innerHTML = `<i class="fas fa-sliders-h mr-2"></i><span>${buttonText}</span>`;

                // Store preference in localStorage
                localStorage.setItem('showAdvancedFilters', !advancedFilterSection.classList.contains('hidden'));
            });

            // Check localStorage for user preference
            const showFilters = localStorage.getItem('showAdvancedFilters') === 'true';
            if (showFilters) {
                advancedFilterSection.classList.remove('hidden');
                advancedFilterToggle.innerHTML = `<i class="fas fa-sliders-h mr-2"></i><span>Hide Filters</span>`;
            }
        }

        // All filter buttons are now direct links, no JavaScript needed

        // Initialize Subscription Date Range Picker
        const subscriptionDateInput = document.getElementById('subscriptionDateRange');
        const subscribedFromInput = document.getElementById('subscribedFromFilter');
        const subscribedToInput = document.getElementById('subscribedToFilter');
        const clearSubscriptionDateBtn = document.getElementById('clearSubscriptionDate');

        // Helper function to format display date
        function formatDisplayDate(dateStr) {
            if (!dateStr) return '';
            const date = new Date(dateStr);
            const day = date.getDate();
            const month = date.toLocaleString('en-US', { month: 'short' });
            const year = date.getFullYear();
            return `${day} ${month}, ${year}`;
        }

        // Update display value for date range inputs
        function updateDateRangeDisplay(inputElement, startDate, endDate) {
            if (startDate && endDate) {
                inputElement.value = `${formatDisplayDate(startDate)} – ${formatDisplayDate(endDate)}`;
            } else if (startDate) {
                inputElement.value = `From ${formatDisplayDate(startDate)}`;
            } else if (endDate) {
                inputElement.value = `Until ${formatDisplayDate(endDate)}`;
            } else {
                inputElement.value = '';
                inputElement.placeholder = 'Select date range';
            }
        }

        if (subscriptionDateInput && subscribedFromInput && subscribedToInput) {
            // Set initial display value
            updateDateRangeDisplay(subscriptionDateInput, subscribedFromInput.value, subscribedToInput.value);

            const subscriptionPicker = new Litepicker({
                element: subscriptionDateInput,
                format: 'YYYY-MM-DD',
                singleMode: false,
                numberOfMonths: 1,
                numberOfColumns: 1,
                showTooltip: true,
                tooltipText: {one: 'day', other: 'days'},
                startDate: subscribedFromInput.value || null,
                endDate: subscribedToInput.value || null,
                autoApply: true,
                showWeekNumbers: false,
                mobileFriendly: true,
                resetButton: false,
                autoRefresh: true,
                moveByOneMonth: true,
                animationDuration: 200, // Faster animation for more professional feel
                firstDay: 1, // Start with Monday for business calendar
                splitView: false,
                useResetButton: false,
                inlineMode: false,
                singleMode: false,
                highlightedDaysWeekdays: [],
                showToday: true, // Show today's date
                maxDate: new Date(), // Restrict selection to today and earlier
                setup: (picker) => {
                    // Add tooltip for date restriction
                    const tooltipElement = document.createElement('div');
                    tooltipElement.className = 'hidden';
                    tooltipElement.id = 'subscriptionDateTooltip';
                    tooltipElement.innerHTML = '<i class="fas fa-exclamation-circle mr-1"></i>Subscription date cannot be in the future';
                    subscriptionDateInput.parentNode.appendChild(tooltipElement);

                    picker.on('selected', (startDate, endDate) => {
                        if (startDate) {
                            subscribedFromInput.value = startDate.format('YYYY-MM-DD');
                        }
                        if (endDate) {
                            subscribedToInput.value = endDate.format('YYYY-MM-DD');
                        }

                        // Update display value
                        updateDateRangeDisplay(
                            subscriptionDateInput,
                            subscribedFromInput.value,
                            subscribedToInput.value
                        );
                    });
                    // No clear button, but we'll keep this for programmatic clearing if needed
                    picker.on('clear', () => {
                        subscribedFromInput.value = '';
                        subscribedToInput.value = '';
                        subscriptionDateInput.value = '';
                        subscriptionDateInput.placeholder = 'Select date range';
                    });
                    picker.on('show', () => {
                        subscriptionDateInput.classList.add('ring-2', 'ring-indigo-100', 'border-indigo-400');
                    });
                    picker.on('hide', () => {
                        subscriptionDateInput.classList.remove('ring-2', 'ring-indigo-100', 'border-indigo-400');
                    });

                    // Show tooltip when trying to select a future date
                    picker.on('error:date', () => {
                        const tooltip = document.getElementById('subscriptionDateTooltip');
                        if (tooltip) {
                            tooltip.classList.remove('hidden');
                            setTimeout(() => {
                                tooltip.classList.add('hidden');
                            }, 3000); // Hide after 3 seconds
                        }
                    });
                }
            });

            // Add focus effect
            subscriptionDateInput.addEventListener('focus', () => {
                subscriptionDateInput.classList.add('ring-2', 'ring-indigo-100', 'border-indigo-400');
            });

            subscriptionDateInput.addEventListener('blur', () => {
                setTimeout(() => {
                    if (!subscriptionPicker.isShowing()) {
                        subscriptionDateInput.classList.remove('ring-2', 'ring-indigo-100', 'border-indigo-400');
                    }
                }, 100);
            });

            // Add clear button functionality
            if (clearSubscriptionDateBtn) {
                clearSubscriptionDateBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();

                    // Clear the inputs
                    subscribedFromInput.value = '';
                    subscribedToInput.value = '';
                    subscriptionDateInput.value = '';
                    subscriptionDateInput.placeholder = 'Select date range';

                    // Hide the clear button
                    clearSubscriptionDateBtn.classList.add('hidden');

                    // Clear the picker
                    if (subscriptionPicker) {
                        subscriptionPicker.clearSelection();
                    }
                });
            }

            // Show/hide clear button based on input value
            subscriptionPicker.on('selected', () => {
                if (subscribedFromInput.value || subscribedToInput.value) {
                    clearSubscriptionDateBtn.classList.remove('hidden');
                } else {
                    clearSubscriptionDateBtn.classList.add('hidden');
                }
            });
        }

        // Initialize Expiration Date Range Picker
        const expirationDateInput = document.getElementById('expirationDateRange');
        const expiredFromInput = document.getElementById('expiredFromFilter');
        const expiredToInput = document.getElementById('expiredToFilter');
        const clearExpirationDateBtn = document.getElementById('clearExpirationDate');

        if (expirationDateInput && expiredFromInput && expiredToInput) {
            // Set initial display value
            updateDateRangeDisplay(expirationDateInput, expiredFromInput.value, expiredToInput.value);

            const expirationPicker = new Litepicker({
                element: expirationDateInput,
                format: 'YYYY-MM-DD',
                singleMode: false,
                numberOfMonths: 1,
                numberOfColumns: 1,
                showTooltip: true,
                tooltipText: {one: 'day', other: 'days'},
                startDate: expiredFromInput.value || null,
                endDate: expiredToInput.value || null,
                autoApply: true,
                showWeekNumbers: false,
                mobileFriendly: true,
                resetButton: false,
                autoRefresh: true,
                moveByOneMonth: true,
                animationDuration: 200, // Faster animation for more professional feel
                firstDay: 1, // Start with Monday for business calendar
                splitView: false,
                useResetButton: false,
                inlineMode: false,
                singleMode: false,
                highlightedDaysWeekdays: [],
                showToday: true, // Show today's date
                setup: (picker) => {
                    picker.on('selected', (startDate, endDate) => {
                        if (startDate) {
                            expiredFromInput.value = startDate.format('YYYY-MM-DD');
                        }
                        if (endDate) {
                            expiredToInput.value = endDate.format('YYYY-MM-DD');
                        }

                        // Update display value
                        updateDateRangeDisplay(
                            expirationDateInput,
                            expiredFromInput.value,
                            expiredToInput.value
                        );
                    });
                    // No clear button, but we'll keep this for programmatic clearing if needed
                    picker.on('clear', () => {
                        expiredFromInput.value = '';
                        expiredToInput.value = '';
                        expirationDateInput.value = '';
                        expirationDateInput.placeholder = 'Select date range';
                    });
                    picker.on('show', () => {
                        expirationDateInput.classList.add('ring-2', 'ring-indigo-100', 'border-indigo-400');
                    });
                    picker.on('hide', () => {
                        expirationDateInput.classList.remove('ring-2', 'ring-indigo-100', 'border-indigo-400');
                    });
                }
            });

            // Add focus effect
            expirationDateInput.addEventListener('focus', () => {
                expirationDateInput.classList.add('ring-2', 'ring-indigo-100', 'border-indigo-400');
            });

            expirationDateInput.addEventListener('blur', () => {
                setTimeout(() => {
                    if (!expirationPicker.isShowing()) {
                        expirationDateInput.classList.remove('ring-2', 'ring-indigo-100', 'border-indigo-400');
                    }
                }, 100);
            });

            // Add clear button functionality
            if (clearExpirationDateBtn) {
                clearExpirationDateBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    e.stopPropagation();

                    // Clear the inputs
                    expiredFromInput.value = '';
                    expiredToInput.value = '';
                    expirationDateInput.value = '';
                    expirationDateInput.placeholder = 'Select date range';

                    // Hide the clear button
                    clearExpirationDateBtn.classList.add('hidden');

                    // Clear the picker
                    if (expirationPicker) {
                        expirationPicker.clearSelection();
                    }
                });
            }

            // Show/hide clear button based on input value
            expirationPicker.on('selected', () => {
                if (expiredFromInput.value || expiredToInput.value) {
                    clearExpirationDateBtn.classList.remove('hidden');
                } else {
                    clearExpirationDateBtn.classList.add('hidden');
                }
            });
        }
    });
</script>
{% endblock %}
