{% extends 'base.html' %}

{% block title %}Dashboard{% endblock %}
{% block header_title %}Dashboard{% endblock %}

{% block content %}
<!-- Stats Cards -->
<div class="grid grid-cols-2 lg:grid-cols-4 gap-3 md:gap-6 mb-6 md:mb-8 px-2 md:px-0">
    <!-- Total Subscriptions Card -->
    <div class="bg-white rounded-xl shadow-md p-3 md:p-6 flex items-center border border-gray-100 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 card">
        <div class="rounded-full bg-indigo-100 p-2 md:p-4 mr-2 md:mr-5 flex items-center justify-center">
            <i class="fas fa-users text-indigo-600 text-lg md:text-xl"></i>
        </div>
        <div>
            <h3 class="text-xl md:text-3xl font-bold text-gray-800">{{ total_subscriptions }}</h3>
            <p class="text-xs md:text-sm text-gray-500 mt-1">Total Subscriptions</p>
        </div>
        <div class="ml-auto text-indigo-100 hover:text-indigo-200 transition-colors hidden md:block">
            <i class="fas fa-chart-line text-4xl"></i>
        </div>
    </div>

    <!-- Active Subscriptions Card -->
    <div class="bg-white rounded-xl shadow-md p-4 md:p-6 flex items-center border border-gray-100 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 card">
        <div class="rounded-full bg-green-100 p-3 md:p-4 mr-3 md:mr-5 flex items-center justify-center">
            <i class="fas fa-check-circle text-green-600 text-lg md:text-xl"></i>
        </div>
        <div>
            <h3 class="text-2xl md:text-3xl font-bold text-gray-800">{{ active_subscriptions }}</h3>
            <p class="text-xs md:text-sm text-gray-500 mt-1">Active Subscriptions</p>
        </div>
        <div class="ml-auto text-green-100 hover:text-green-200 transition-colors hidden md:block">
            <i class="fas fa-user-check text-4xl"></i>
        </div>
    </div>

    <!-- Access Codes Card -->
    <div class="bg-white rounded-xl shadow-md p-4 md:p-6 flex items-center border border-gray-100 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 card">
        <div class="rounded-full bg-indigo-100 p-3 md:p-4 mr-3 md:mr-5 flex items-center justify-center">
            <i class="fas fa-key text-indigo-600 text-lg md:text-xl"></i>
        </div>
        <div>
            <h3 class="text-2xl md:text-3xl font-bold text-gray-800">{{ total_access_codes }}</h3>
            <p class="text-xs md:text-sm text-gray-500 mt-1">Access Codes</p>
        </div>
        <div class="ml-auto text-indigo-100 hover:text-indigo-200 transition-colors hidden md:block">
            <i class="fas fa-lock text-4xl"></i>
        </div>
    </div>

    <div class="bg-white rounded-xl shadow-md p-4 md:p-6 flex items-center border border-gray-100 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 card">
        <div class="rounded-full bg-amber-100 p-3 md:p-4 mr-3 md:mr-5 flex items-center justify-center">
            <i class="fas fa-clock text-amber-600 text-lg md:text-xl"></i>
        </div>
        <div>
            <h3 class="text-2xl md:text-3xl font-bold text-gray-800">{{ expiring_soon }}</h3>
            <p class="text-xs md:text-sm text-gray-500 mt-1">Expiring Soon</p>
        </div>
        <div class="ml-auto text-amber-100 hover:text-amber-200 transition-colors hidden md:block">
            <i class="fas fa-hourglass-half text-4xl"></i>
        </div>
    </div>
</div>

<!-- Subscriptions Table -->
<div class="bg-white rounded-xl shadow-md mb-6 md:mb-8 border border-gray-100 overflow-hidden mx-2 md:mx-0">
    <div class="px-3 md:px-6 py-3 md:py-4 border-b border-gray-200 flex flex-col md:flex-row justify-between items-start md:items-center bg-gradient-to-r from-indigo-50 to-white">
        <div class="flex items-center mb-4 md:mb-0">
            <i class="fas fa-users text-indigo-500 mr-3"></i>
            <h2 class="text-base md:text-lg font-semibold text-gray-800">Active Subscriptions</h2>
        </div>
        <div class="w-full md:w-auto">
            <form method="GET" action="{{ tenant_url_for('dashboard.home_route') }}" class="flex flex-col md:flex-row items-start md:items-center space-y-2 md:space-y-0 md:space-x-2 w-full">
                <div class="relative w-full md:w-auto">
                    <input type="text" name="filter" value="{{ filter_query }}" placeholder="Search users..." class="w-full md:w-48 pl-8 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-200 focus:border-indigo-400 transition-colors text-sm">
                    <div class="absolute inset-y-0 left-0 pl-2 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400 text-sm"></i>
                    </div>
                </div>
                <div class="flex space-x-2 w-full md:w-auto justify-between md:justify-start">
                    <button type="submit" class="flex-1 md:flex-none text-sm px-3 py-2 bg-indigo-50 text-indigo-600 rounded-md hover:bg-indigo-100 transition-colors duration-200 flex items-center justify-center">
                        <i class="fas fa-filter mr-1"></i> Filter
                    </button>
                    {% if filter_query %}
                    <a href="{{ tenant_url_for('dashboard.home_route') }}" class="flex-1 md:flex-none text-sm px-3 py-2 bg-gray-50 text-gray-600 rounded-md hover:bg-gray-100 transition-colors duration-200 flex items-center justify-center">
                        <i class="fas fa-times mr-1"></i> Clear
                    </a>
                    {% endif %}
                </div>
            </form>
        </div>
    </div>

    <div class="overflow-x-auto max-w-full">
        {% if subscriptions %}
        <!-- Desktop table view -->
        <div class="hidden md:block">
            <table class="min-w-full divide-y divide-gray-200">
                <thead>
                    <tr class="bg-gray-50">
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">User ID
                        </th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Name</th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Access
                            Code</th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Status
                        </th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                            Registered On</th>
                    </tr>
                </thead>
                <tbody>
                    {% for sub in subscriptions %}
                    <tr class="hover:bg-gray-50 transition-colors duration-150">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <a href="{{ tenant_url_for('dashboard.user_detail_route', user_id=sub.user_id) }}"
                                class="text-indigo-600 hover:text-indigo-800 hover:underline font-medium">
                                {{ sub.user_id }}
                            </a>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap font-medium text-gray-800">
                            {{ sub.name or 'User ' + sub.user_id|string }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-gray-600 font-mono text-sm">
                            {{ sub.access_code }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200">
                                {{ sub.user_status|upper }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {% if sub.registration_time %}
                            <div class="flex items-center">
                                <i class="far fa-calendar-alt mr-2 text-gray-400"></i>
                                {{ sub.registration_time.strftime('%Y-%m-%d %H:%M:%S') }}
                            </div>
                            {% else %}
                            N/A
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Mobile card view -->
        <div class="md:hidden w-full">
            <div class="divide-y divide-gray-200 w-full bg-white rounded-b-xl">
                {% for sub in subscriptions %}
                <div class="p-4 hover:bg-gray-50 transition-colors duration-150 relative">
                    <div class="flex justify-between items-start mb-3">
                        <a href="{{ tenant_url_for('dashboard.user_detail_route', user_id=sub.user_id) }}"
                            class="text-indigo-600 hover:text-indigo-800 hover:underline font-medium text-base">
                            {{ sub.user_id }}
                        </a>
                        <span class="px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200">
                            {{ sub.user_status|upper }}
                        </span>
                    </div>
                    <div class="text-sm text-gray-800 font-medium mb-2">{{ sub.name or 'User ' + sub.user_id|string }}</div>
                    <div class="flex flex-col space-y-2">
                        <div class="flex items-center">
                            <span class="w-6 h-6 rounded-full bg-indigo-100 flex items-center justify-center mr-2">
                                <i class="fas fa-key text-indigo-600 text-xs"></i>
                            </span>
                            <div class="text-xs text-gray-600 font-mono">{{ sub.access_code }}</div>
                        </div>
                        <div class="flex items-center">
                            <span class="w-6 h-6 rounded-full bg-gray-100 flex items-center justify-center mr-2">
                                <i class="far fa-calendar-alt text-gray-500 text-xs"></i>
                            </span>
                            <div class="text-xs text-gray-500">
                                {% if sub.registration_time %}
                                {{ sub.registration_time.strftime('%Y-%m-%d') }}
                                {% else %}
                                N/A
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% else %}
        <!-- Empty state for both desktop and mobile -->
        <div class="px-3 md:px-6 py-8 md:py-12 text-center">
            <div class="inline-flex items-center justify-center w-14 h-14 md:w-16 md:h-16 rounded-full bg-indigo-100 text-indigo-400 mb-3 md:mb-4">
                <i class="fas fa-users text-xl md:text-2xl"></i>
            </div>
            <h3 class="text-base md:text-lg font-medium text-gray-900 mb-2">No active subscriptions</h3>
            <p class="text-sm md:text-base text-gray-500 max-w-md mx-auto">There are currently no active subscriptions in the system.</p>
        </div>
        {% endif %}
    </div>

    <!-- Pagination for Active Subscriptions -->
    {% if subscriptions %}
    <div class="px-4 md:px-6 py-4 bg-gray-50 border-t border-gray-200 flex flex-col md:flex-row items-center justify-center md:justify-between space-y-3 md:space-y-0">
        <div class="text-xs md:text-sm text-gray-500 order-2 md:order-1">
            Showing <span class="font-medium text-gray-700">{{ start_item }}</span> to <span class="font-medium text-gray-700">{{ end_item }}</span> of <span class="font-medium text-gray-700">{{ pagination.total }}</span> active users
        </div>
        <div class="flex space-x-1 order-1 md:order-2">
            {% if pagination.has_prev %}
            <a href="{{ tenant_url_for('dashboard.home_route', page=pagination.page-1, filter=filter_query) }}" class="px-2 md:px-3 py-1 bg-white border border-gray-300 rounded-md text-xs md:text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                <i class="fas fa-chevron-left"></i>
            </a>
            {% else %}
            <span class="px-2 md:px-3 py-1 bg-gray-100 border border-gray-300 rounded-md text-xs md:text-sm text-gray-400 cursor-not-allowed">
                <i class="fas fa-chevron-left"></i>
            </span>
            {% endif %}

            {% set window_size = 3 %}
            {% set window_start = [1, pagination.page - (window_size // 2)]|max %}
            {% set window_end = [window_start + window_size - 1, pagination.total_pages]|min %}

            <!-- Desktop pagination with more options -->
            <div class="hidden md:flex space-x-1">
                {% if window_start > 1 %}
                <a href="{{ tenant_url_for('dashboard.home_route', page=1, filter=filter_query) }}" class="px-3 py-1 bg-white border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                    1
                </a>
                {% if window_start > 2 %}
                <span class="px-2 py-1 text-gray-500">...</span>
                {% endif %}
                {% endif %}

                {% for p in range(window_start, window_end + 1) %}
                {% if p == pagination.page %}
                <span class="px-3 py-1 bg-indigo-600 border border-indigo-600 rounded-md text-sm text-white">
                    {{ p }}
                </span>
                {% else %}
                <a href="{{ tenant_url_for('dashboard.home_route', page=p, filter=filter_query) }}" class="px-3 py-1 bg-white border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                    {{ p }}
                </a>
                {% endif %}
                {% endfor %}

                {% if window_end < pagination.total_pages %}
                {% if window_end < pagination.total_pages - 1 %}
                <span class="px-2 py-1 text-gray-500">...</span>
                {% endif %}
                <a href="{{ tenant_url_for('dashboard.home_route', page=pagination.total_pages, filter=filter_query) }}" class="px-3 py-1 bg-white border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                    {{ pagination.total_pages }}
                </a>
                {% endif %}
            </div>

            <!-- Mobile pagination (simplified) -->
            <div class="flex md:hidden items-center justify-between w-full px-3 py-2">
                <a href="{{ tenant_url_for('dashboard.home_route', page=pagination.prev_page, filter=filter_query) }}" class="px-3 py-1 bg-white border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 transition-colors {% if pagination.page == 1 %}opacity-50 pointer-events-none{% endif %}">
                    <i class="fas fa-chevron-left text-xs"></i>
                </a>
                <span class="px-2 py-1 text-sm text-gray-700 font-medium">
                    {{ pagination.page }} / {{ pagination.total_pages }}
                </span>
                <a href="{{ tenant_url_for('dashboard.home_route', page=pagination.next_page, filter=filter_query) }}" class="px-3 py-1 bg-white border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 transition-colors {% if pagination.page == pagination.total_pages %}opacity-50 pointer-events-none{% endif %}">
                    <i class="fas fa-chevron-right text-xs"></i>
                </a>
            </div>

            {% if pagination.has_next %}
            <a href="{{ tenant_url_for('dashboard.home_route', page=pagination.page+1, filter=filter_query) }}" class="px-2 md:px-3 py-1 bg-white border border-gray-300 rounded-md text-xs md:text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                <i class="fas fa-chevron-right"></i>
            </a>
            {% else %}
            <span class="px-2 md:px-3 py-1 bg-gray-100 border border-gray-300 rounded-md text-xs md:text-sm text-gray-400 cursor-not-allowed">
                <i class="fas fa-chevron-right"></i>
            </span>
            {% endif %}
        </div>
    </div>
    {% endif %}
</div>

<!-- Activity Logs -->
<div class="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden mx-2 md:mx-0 mb-6 md:mb-8">
    <div class="px-3 md:px-6 py-3 md:py-4 border-b border-gray-200 flex flex-col md:flex-row justify-between items-start md:items-center bg-gradient-to-r from-indigo-50 to-white">
        <div class="flex items-center mb-3 md:mb-0">
            <i class="fas fa-history text-indigo-500 mr-3"></i>
            <h2 class="text-base md:text-lg font-semibold text-gray-800">Recent Activity</h2>
        </div>
        <div>
            <button class="text-sm px-3 py-2 bg-indigo-50 text-indigo-600 rounded-md hover:bg-indigo-100 transition-colors duration-200 flex items-center justify-center w-full md:w-auto">
                <i class="fas fa-sync-alt mr-1"></i> Refresh
            </button>
        </div>
    </div>

    <div class="overflow-x-auto max-w-full">
        {% if recent_logs %}
        <!-- Desktop view for activity logs -->
        <div class="hidden md:block">
            <table class="min-w-full divide-y divide-gray-200">
                <thead>
                    <tr class="bg-gray-50">
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Action
                        </th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Details
                        </th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Timestamp
                        </th>
                    </tr>
                </thead>
                <tbody>
                {% for log in recent_logs %}
                <tr class="hover:bg-gray-50 transition-colors duration-150">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            {% if 'add' in log.action.lower() %}
                                <span class="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center mr-3">
                                    <i class="fas fa-plus text-green-600"></i>
                                </span>
                            {% elif 'delete' in log.action.lower() or 'remove' in log.action.lower() %}
                                <span class="w-8 h-8 rounded-full bg-red-100 flex items-center justify-center mr-3">
                                    <i class="fas fa-trash-alt text-red-600"></i>
                                </span>
                            {% elif 'update' in log.action.lower() or 'edit' in log.action.lower() %}
                                <span class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                                    <i class="fas fa-edit text-blue-600"></i>
                                </span>
                            {% elif 'login' in log.action.lower() %}
                                <span class="w-8 h-8 rounded-full bg-indigo-100 flex items-center justify-center mr-3">
                                    <i class="fas fa-sign-in-alt text-indigo-600"></i>
                                </span>
                            {% else %}
                                <span class="w-8 h-8 rounded-full bg-gray-100 flex items-center justify-center mr-3">
                                    <i class="fas fa-info text-gray-600"></i>
                                </span>
                            {% endif %}
                            <div class="text-sm font-medium text-gray-900">{{ log.action }}</div>
                        </div>
                    </td>
                    <td class="px-6 py-4">
                        <div class="text-sm text-gray-700">{{ log.details }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center text-sm text-gray-500">
                            <i class="far fa-clock mr-2 text-gray-400"></i>
                            {% if log.timestamp is defined %}
                            {% if log.timestamp is string %}
                            {{ log.timestamp }}
                            {% else %}
                            {{ log.timestamp.strftime('%Y-%m-%d %H:%M:%S') }}
                            {% endif %}
                            {% else %}
                            {{ log.get('timestamp', 'N/A') }}
                            {% endif %}
                        </div>
                    </td>
                </tr>
                {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Mobile view for activity logs -->
        <div class="md:hidden w-full">
            <div class="divide-y divide-gray-200 w-full bg-white rounded-b-xl">
                {% for log in recent_logs %}
                <div class="p-4 hover:bg-gray-50 transition-colors duration-150 relative">
                    <div class="flex items-start mb-2">
                        {% if 'add' in log.action.lower() %}
                            <span class="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center mr-3">
                                <i class="fas fa-plus text-green-600"></i>
                            </span>
                        {% elif 'delete' in log.action.lower() or 'remove' in log.action.lower() %}
                            <span class="w-8 h-8 rounded-full bg-red-100 flex items-center justify-center mr-3">
                                <i class="fas fa-trash-alt text-red-600"></i>
                            </span>
                        {% elif 'update' in log.action.lower() or 'edit' in log.action.lower() %}
                            <span class="w-8 h-8 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                                <i class="fas fa-edit text-blue-600"></i>
                            </span>
                        {% else %}
                            <span class="w-8 h-8 rounded-full bg-indigo-100 flex items-center justify-center mr-3">
                                <i class="fas fa-info text-indigo-600"></i>
                            </span>
                        {% endif %}
                        <div class="flex-1">
                            <div class="font-medium text-gray-800 text-base">{{ log.action }}</div>
                            <div class="text-xs text-gray-500 mt-1">
                                {% if log.timestamp is defined %}
                                {% if log.timestamp is string %}
                                {{ log.timestamp }}
                                {% else %}
                                {{ log.timestamp.strftime('%Y-%m-%d %H:%M') }}
                                {% endif %}
                                {% else %}
                                {{ log.get('timestamp', 'N/A') }}
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="text-sm text-gray-600 ml-11 mt-2 bg-gray-50 p-2 rounded-md">{{ log.details }}</div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% else %}
        <div class="px-3 md:px-6 py-8 md:py-12 text-center">
            <div class="inline-flex items-center justify-center w-14 h-14 md:w-16 md:h-16 rounded-full bg-blue-100 text-indigo-400 mb-3 md:mb-4">
                <i class="fas fa-history text-xl md:text-2xl"></i>
            </div>
            <h3 class="text-base md:text-lg font-medium text-gray-900 mb-2">No activity logs</h3>
            <p class="text-sm md:text-base text-gray-500 max-w-md mx-auto">There are no recent activities recorded in the system.</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- Access Code Details Modal -->
<div id="accessCodeDetailsModal"
    class="fixed inset-0 bg-gray-900 bg-opacity-50 backdrop-blur-sm flex items-center justify-center hidden z-50">
    <div class="bg-white rounded-xl shadow-xl p-4 md:p-6 w-full max-w-md mx-4 md:mx-auto transform transition-all duration-300 scale-100">
        <div class="flex justify-between items-center mb-5">
            <h3 class="text-xl font-semibold text-gray-800 flex items-center">
                <i class="fas fa-key text-indigo-500 mr-3"></i>
                Access Code Details
            </h3>
            <button class="text-gray-400 hover:text-gray-600 transition-colors duration-200 close-modal rounded-full hover:bg-gray-100 p-2">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div id="accessCodeDetailsContent" class="space-y-4 bg-gray-50 p-3 md:p-4 rounded-lg mb-5 text-sm md:text-base">
            <div class="flex justify-between items-center border-b border-gray-200 pb-3">
                <span class="text-gray-600 font-medium">Access Code:</span>
                <span id="detailCode" class="font-mono font-medium text-indigo-600 break-all"></span>
            </div>
            <div class="flex justify-between items-center py-2">
                <span class="text-gray-600">Registration Date:</span>
                <span id="detailRegDate" class="font-medium text-gray-800"></span>
            </div>
            <div class="flex justify-between items-center py-2">
                <span class="text-gray-600">Volume (USD millions):</span>
                <span id="detailVolume" class="font-medium text-gray-800"></span>
            </div>
            <div class="flex justify-between items-center py-2">
                <span class="text-gray-600">Trade Function:</span>
                <span id="detailTradeFn" class="font-medium text-gray-800"></span>
            </div>
            <div class="flex justify-between items-center py-2">
                <span class="text-gray-600">Source:</span>
                <span id="detailSource" class="font-medium text-gray-800"></span>
            </div>
            <div class="flex justify-between items-center pt-2 border-t border-gray-200">
                <span class="text-gray-600">Added At:</span>
                <span id="detailAddedAt" class="font-medium text-gray-800 flex items-center">
                    <i class="far fa-calendar-alt mr-1 text-gray-400"></i>
                    <span></span>
                </span>
            </div>
        </div>
        <div class="flex justify-end">
            <button type="button"
                class="px-4 md:px-5 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors duration-200 text-sm md:text-base font-medium close-modal shadow-sm hover:shadow focus:outline-none focus:ring-2 focus:ring-indigo-300">
                <i class="fas fa-check mr-2"></i>Close
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Access code details functionality
        const accessCodeLinks = document.querySelectorAll('.view-access-code');
        const accessCodeDetailsModal = document.getElementById('accessCodeDetailsModal');

        accessCodeLinks.forEach(link => {
            link.addEventListener('click', function (e) {
                e.preventDefault();
                const code = this.getAttribute('data-code');

                // Show loading state
                document.getElementById('accessCodeDetailsContent').innerHTML = `
                    <div class="flex justify-center items-center py-8">
                        <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-indigo-500"></div>
                    </div>
                    <div class="text-center text-gray-500 text-sm">Loading details...</div>
                `;

                // Show modal immediately with loading state
                accessCodeDetailsModal.classList.remove('hidden');

                // Add entrance animation
                const modalContent = accessCodeDetailsModal.querySelector('.bg-white');
                modalContent.classList.add('scale-95', 'opacity-0');
                setTimeout(() => {
                    modalContent.classList.remove('scale-95', 'opacity-0');
                    modalContent.classList.add('scale-100', 'opacity-100');
                }, 10);

                // Updated route path to use access-codes endpoint
                fetch(`/dashboard/access-code-details/${code}`)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.success) {
                            const details = data.data;

                            // Format date if it exists
                            let addedAt = details.added_at || 'N/A';
                            if (addedAt && addedAt !== 'N/A' && !isNaN(new Date(addedAt).getTime())) {
                                addedAt = new Date(addedAt).toLocaleString();
                            }

                            // Rebuild the content
                            document.getElementById('accessCodeDetailsContent').innerHTML = `
                                <div class="flex justify-between items-center border-b border-gray-200 pb-3">
                                    <span class="text-gray-600 font-medium">Access Code:</span>
                                    <span id="detailCode" class="font-mono font-medium text-indigo-600">${details.code || 'N/A'}</span>
                                </div>
                                <div class="flex justify-between items-center py-2">
                                    <span class="text-gray-600">Registration Date:</span>
                                    <span id="detailRegDate" class="font-medium text-gray-800">${details.reg_date || 'N/A'}</span>
                                </div>
                                <div class="flex justify-between items-center py-2">
                                    <span class="text-gray-600">Volume (USD millions):</span>
                                    <span id="detailVolume" class="font-medium text-gray-800">${details.volume_mln_usd || 'N/A'}</span>
                                </div>
                                <div class="flex justify-between items-center py-2">
                                    <span class="text-gray-600">Trade Function:</span>
                                    <span id="detailTradeFn" class="font-medium text-gray-800">${details.trade_fn || 'N/A'}</span>
                                </div>
                                <div class="flex justify-between items-center py-2">
                                    <span class="text-gray-600">Source:</span>
                                    <span id="detailSource" class="font-medium text-gray-800">${details.source || 'N/A'}</span>
                                </div>
                                <div class="flex justify-between items-center pt-2 border-t border-gray-200">
                                    <span class="text-gray-600">Added At:</span>
                                    <span id="detailAddedAt" class="font-medium text-gray-800 flex items-center">
                                        <i class="far fa-calendar-alt mr-1 text-gray-400"></i>
                                        <span>${addedAt}</span>
                                    </span>
                                </div>
                            `;
                        } else {
                            document.getElementById('accessCodeDetailsContent').innerHTML = `
                                <div class="text-center py-6">
                                    <div class="inline-flex items-center justify-center w-12 h-12 rounded-full bg-red-100 text-red-500 mb-4">
                                        <i class="fas fa-exclamation-triangle"></i>
                                    </div>
                                    <h4 class="text-lg font-medium text-gray-900 mb-2">Error</h4>
                                    <p class="text-gray-500">${data.message || 'Could not load access code details'}</p>
                                </div>
                            `;
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        document.getElementById('accessCodeDetailsContent').innerHTML = `
                            <div class="text-center py-6">
                                <div class="inline-flex items-center justify-center w-12 h-12 rounded-full bg-red-100 text-red-500 mb-4">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <h4 class="text-lg font-medium text-gray-900 mb-2">Error</h4>
                                <p class="text-gray-500">Could not fetch access code details. Please try again.</p>
                            </div>
                        `;
                    });
            });
        });

        // Close modal functionality with animation
        function closeModal() {
            const modalContent = accessCodeDetailsModal.querySelector('.bg-white');
            modalContent.classList.remove('scale-100');
            modalContent.classList.add('scale-95', 'opacity-0');

            setTimeout(() => {
                accessCodeDetailsModal.classList.add('hidden');
                modalContent.classList.remove('scale-95', 'opacity-0');
            }, 200);
        }

        document.querySelectorAll('.close-modal').forEach(button => {
            button.addEventListener('click', closeModal);
        });

        // Also close modal when clicking outside
        accessCodeDetailsModal.addEventListener('click', function (e) {
            if (e.target === accessCodeDetailsModal) {
                closeModal();
            }
        });

        // Close modal on escape key
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape' && !accessCodeDetailsModal.classList.contains('hidden')) {
                closeModal();
            }
        });

        // Add hover effect to table rows
        const tableRows = document.querySelectorAll('tbody tr');
        tableRows.forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.classList.add('bg-gray-50');
            });
            row.addEventListener('mouseleave', function() {
                this.classList.remove('bg-gray-50');
            });
        });
    });
</script>
{% endblock %}