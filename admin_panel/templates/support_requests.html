{% extends 'base.html' %}

{% block title %}Support Requests{% endblock %}
{% block header_title %}Support Requests{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/support_requests_mobile.css') }}">
{% endblock %}

{% block content %}

    <!-- Support Requests Table Card -->
    <div class="bg-white rounded-xl shadow-md mb-8 border border-gray-100 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center bg-gradient-to-r from-indigo-50 to-white">
            <div class="flex items-center">
                <i class="fas fa-headset text-indigo-500 mr-3"></i>
                <h2 class="text-lg font-semibold text-gray-800">Support Requests</h2>
            </div>
        </div>

        {% if requests %}
        <table class="min-w-full divide-y divide-gray-200">
            <thead>
                <tr class="bg-gray-50">
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Request ID</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User Name</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User Email</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created At</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for request in requests %}
                <tr class="{% if loop.index is odd %}bg-white{% else %}bg-gray-50{% endif %} hover:bg-gray-100 transition-colors duration-150" data-request-id="{{ request.request_id }}">
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900" data-label="REQUEST ID">{{ request.request_id }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700" data-label="NAME">{{ request.user_details.name }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700" data-label="EMAIL">{{ request.user_details.email }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700" data-label="STATUS">
                        {% if request.status == 'pending' %}
                        <span class="px-2.5 py-0.5 inline-flex text-xs leading-5 font-medium rounded-full bg-yellow-100 text-yellow-800">Pending</span>
                        {% elif request.status == 'in_progress' %}
                        <span class="px-2.5 py-0.5 inline-flex text-xs leading-5 font-medium rounded-full bg-indigo-100 text-indigo-800">In Progress</span>
                        {% elif request.status == 'resolved' %}
                        <span class="px-2.5 py-0.5 inline-flex text-xs leading-5 font-medium rounded-full bg-green-100 text-green-800">Resolved</span>
                        {% else %}
                        <span class="px-2.5 py-0.5 inline-flex text-xs leading-5 font-medium rounded-full bg-gray-100 text-gray-800">{{ request.status }}</span>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700" data-label="CREATED AT">{{ request.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium" data-label="ACTIONS">
                        <button onclick="viewRequestDetails('{{ request.request_id }}')" class="text-indigo-600 hover:text-indigo-900 focus:outline-none transition-colors duration-150 flex items-center">
                            <i class="fas fa-eye mr-1.5 text-xs"></i> View Details
                        </button>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        {% else %}
        <div class="p-8">
            <div class="flex flex-col items-center justify-center py-12">
                <div class="rounded-full bg-gray-50 p-6 mb-4">
                    <i class="fas fa-headset text-4xl text-gray-300"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No Support Requests</h3>
                <p class="text-gray-500 text-center max-w-md">Support requests will appear here when users submit them.</p>
            </div>
        </div>
        {% endif %}
    </div>
</div>
<!-- Support Request Details Modal -->
<div id="requestDetailsModal" class="fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white rounded-xl shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto border border-gray-200">

        <!-- Modal Header -->
        <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center sticky top-0 bg-gradient-to-r from-indigo-50 to-white z-10">
            <div class="flex items-center">
                <i class="fas fa-headset text-indigo-500 mr-3"></i>
                <h3 class="text-lg font-semibold text-gray-800">Support Request Details</h3>
            </div>
            <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600 focus:outline-none transition-colors duration-150">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <!-- Modal Content -->
        <div class="px-6 py-4">
            <!-- Request Info -->
            <div class="mb-6">
                <div class="flex items-center mb-3">
                    <i class="fas fa-info-circle text-indigo-500 mr-2"></i>
                    <h4 class="text-md font-semibold text-gray-700">Request Information</h4>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 bg-gray-50 p-4 rounded-lg">
                    <div>
                        <p class="text-sm text-gray-500">Request ID</p>
                        <p id="requestId" class="text-sm font-medium text-gray-800"></p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">Status</p>
                        <p id="requestStatus" class="text-sm font-medium"></p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">Created At</p>
                        <p id="requestCreatedAt" class="text-sm font-medium text-gray-800"></p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">Updated At</p>
                        <p id="requestUpdatedAt" class="text-sm font-medium text-gray-800"></p>
                    </div>
                </div>
            </div>

            <!-- User Info -->
            <div class="mb-6">
                <div class="flex items-center mb-3">
                    <i class="fas fa-user text-indigo-500 mr-2"></i>
                    <h4 class="text-md font-semibold text-gray-700">User Information</h4>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 bg-gray-50 p-4 rounded-lg">
                    <div>
                        <p class="text-sm text-gray-500">User ID</p>
                        <p id="userId" class="text-sm font-medium text-gray-800"></p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">Name</p>
                        <p id="userName" class="text-sm font-medium text-gray-800"></p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">Email</p>
                        <p id="userEmail" class="text-sm font-medium text-gray-800"></p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500">WhatsApp</p>
                        <p id="userWhatsapp" class="text-sm font-medium text-gray-800"></p>
                    </div>
                </div>
            </div>

            <!-- Messages -->
            <div>
                <div class="flex items-center mb-3">
                    <i class="fas fa-comments text-indigo-500 mr-2"></i>
                    <h4 class="text-md font-semibold text-gray-700">Messages</h4>
                </div>
                <div id="messagesList" class="space-y-3 bg-gray-50 p-4 rounded-lg max-h-[300px] overflow-y-auto">
                    <!-- Messages will be populated here -->
                </div>
            </div>

            <!-- Send Notification Form -->
            <div class="mt-6">
                <div class="flex items-center mb-3">
                    <i class="fas fa-paper-plane text-indigo-500 mr-2"></i>
                    <h4 class="text-md font-semibold text-gray-700">Send Notification</h4>
                </div>
                <div class="bg-gray-50 p-4 rounded-lg">
                    <div id="notificationResponse" class="mb-4 hidden"></div>
                    <form id="sendNotificationForm" class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Notification Template</label>
                            <div class="bg-white p-3 border border-gray-200 rounded-md text-sm text-gray-700 mb-2">
                                <p><strong>Support Request #:</strong> <span id="templateRequestId"></span></p>
                                <p><strong>Your Query:</strong> <span id="templateQuerySummary"></span></p>
                                <p><strong>Status:</strong> <span id="templateStatus"></span></p>
                                <p>-----------------------------------------</p>
                                <p id="templateMessage"></p>
                            </div>
                        </div>
                        <div>
                            <label for="notificationStatus" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                            <select id="notificationStatus" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                                <option value="Received">Received</option>
                                <option value="In Progress">In Progress</option>
                                <option value="Action Required">Action Required</option>
                                <option value="Resolved">Resolved</option>
                            </select>
                        </div>
                        <div>
                            <label for="notificationMessage" class="block text-sm font-medium text-gray-700 mb-1">Message</label>
                            <textarea id="notificationMessage" rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="Enter your message here...">Thanks for reaching out! We've received your request and our team is looking into it. We'll update you soon.</textarea>
                        </div>
                        <div class="flex justify-end">
                            <button type="button" id="sendNotificationButton" class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 focus:outline-none transition-colors duration-150 shadow-sm">
                                <i class="fas fa-paper-plane mr-1.5"></i> Send Notification
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Modal Footer -->
        <div class="px-6 py-4 border-t border-gray-200 flex justify-end sticky bottom-0 bg-white z-10">
            <button onclick="closeModal()" class="px-4 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 focus:outline-none mr-2 transition-colors duration-150 shadow-sm">
                <i class="fas fa-times mr-1.5"></i> Close
            </button>
            <button id="resolveButton" onclick="resolveRequest()" class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 focus:outline-none transition-colors duration-150 shadow-sm">
                <i class="fas fa-check mr-1.5"></i> Mark as Resolved
            </button>
        </div>
    </div>
</div>

<script>
    // Add mobile-specific functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Make entire row clickable on mobile
        if (window.innerWidth <= 768) {
            const tableRows = document.querySelectorAll('tbody tr');
            tableRows.forEach(row => {
                row.addEventListener('click', function(e) {
                    // Don't trigger if clicking on a button or link
                    if (!e.target.closest('button') && !e.target.closest('a')) {
                        const requestId = this.getAttribute('data-request-id');
                        if (requestId) {
                            viewRequestDetails(requestId);
                        }
                    }
                });
                // Add cursor pointer to indicate clickable
                row.style.cursor = 'pointer';
            });
        }
    });

    // Store request details for use in notification
    let currentRequestDetails = {};

    function viewRequestDetails(requestId) {
        // Set the current request ID
        currentRequestId = requestId;

        // Show loading state
        document.getElementById('requestDetailsModal').classList.remove('hidden');

        // Add loading indicator
        const messagesListElement = document.getElementById('messagesList');
        messagesListElement.innerHTML = '<div class="flex justify-center items-center h-20"><i class="fas fa-spinner fa-spin text-indigo-500 text-2xl"></i></div>';

        // Reset notification form
        document.getElementById('notificationResponse').classList.add('hidden');
        document.getElementById('notificationMessage').value = "Thanks for reaching out! We've received your request and our team is looking into it. We'll update you soon.";
        document.getElementById('notificationStatus').value = "Received";

        // Fetch request details
        fetch(`{{ tenant_prefix }}dashboard/support-request-details/${requestId}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`Request failed with status ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Received data:', data); // Debug log
                // Store the current request details for use in notification
                currentRequestDetails = data;

                // Populate modal with data
                document.getElementById('requestId').textContent = data.request_id;

                // Set status with appropriate badge
                const statusElement = document.getElementById('requestStatus');
                const formattedStatus = data.status.charAt(0).toUpperCase() + data.status.slice(1).replace('_', ' ');
                statusElement.textContent = formattedStatus;

                // Apply appropriate status color
                statusElement.className = 'text-sm font-medium';
                if (data.status === 'pending') {
                    statusElement.classList.add('text-yellow-800');
                } else if (data.status === 'in_progress') {
                    statusElement.classList.add('text-indigo-800');
                } else if (data.status === 'resolved') {
                    statusElement.classList.add('text-green-800');
                } else {
                    statusElement.classList.add('text-gray-800');
                }

                document.getElementById('requestCreatedAt').textContent = data.created_at;
                document.getElementById('requestUpdatedAt').textContent = data.updated_at || 'N/A';

                document.getElementById('userId').textContent = data.user_id || 'N/A';
                document.getElementById('userName').textContent = data.user_name || 'N/A';
                document.getElementById('userEmail').textContent = data.user_email || 'N/A';
                document.getElementById('userWhatsapp').textContent = data.user_whatsapp || 'N/A';

                // Populate notification template
                document.getElementById('templateRequestId').textContent = data.request_id;

                // Get the first message as query summary if available
                let querySummary = 'N/A';
                if (data.messages && data.messages.length > 0) {
                    // Get the first message text and truncate if too long
                    const firstMessage = data.messages[0].text;
                    querySummary = firstMessage.length > 50 ? firstMessage.substring(0, 47) + '...' : firstMessage;
                }
                document.getElementById('templateQuerySummary').textContent = querySummary;

                // Set initial status in the notification template
                updateNotificationPreview();

                // Populate messages
                const messagesListElement = document.getElementById('messagesList');
                messagesListElement.innerHTML = '';

                if (data.messages && data.messages.length > 0) {
                    data.messages.forEach(message => {
                        const messageElement = document.createElement('div');
                        messageElement.className = 'p-3 rounded-lg';

                        // Style based on message type
                        if (message.type === 'initial') {
                            messageElement.classList.add('bg-blue-50', 'border-l-4', 'border-blue-500');
                        } else if (message.type === 'additional') {
                            messageElement.classList.add('bg-gray-100');
                        } else if (message.type === 'admin') {
                            messageElement.classList.add('bg-green-50', 'border-l-4', 'border-green-500');
                        }

                        messageElement.innerHTML = `
                            <div class="flex justify-between items-start">
                                <p class="text-sm font-medium text-gray-800">${message.text}</p>
                                <span class="text-xs text-gray-500">${message.timestamp}</span>
                            </div>
                            <p class="text-xs text-gray-500 mt-1">${message.type.charAt(0).toUpperCase() + message.type.slice(1)}</p>
                        `;

                        messagesListElement.appendChild(messageElement);
                    });
                } else {
                    messagesListElement.innerHTML = '<p class="text-sm text-gray-500">No messages found.</p>';
                }

                // Update resolve button state
                const resolveButton = document.getElementById('resolveButton');
                if (data.status === 'resolved') {
                    resolveButton.textContent = 'Already Resolved';
                    resolveButton.disabled = true;
                    resolveButton.classList.add('opacity-50', 'cursor-not-allowed');
                    resolveButton.classList.remove('hover:bg-blue-700');
                } else {
                    resolveButton.textContent = 'Mark as Resolved';
                    resolveButton.disabled = false;
                    resolveButton.classList.remove('opacity-50', 'cursor-not-allowed');
                    resolveButton.classList.add('hover:bg-blue-700');
                }
            })
            .catch(error => {
                console.error('Error fetching request details:', error);

                // Show error in the modal instead of closing it
                const messagesListElement = document.getElementById('messagesList');
                messagesListElement.innerHTML = `
                    <div class="p-4 bg-red-50 border-l-4 border-red-500 rounded-md">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-circle text-red-500 mr-2"></i>
                            <p class="text-sm font-medium text-red-800">Error loading request details</p>
                        </div>
                        <p class="text-sm text-red-700 mt-1">${error.message}</p>
                        <p class="text-sm text-red-700 mt-2">Please try again or contact the system administrator.</p>
                    </div>
                `;

                // Disable the resolve button
                const resolveButton = document.getElementById('resolveButton');
                resolveButton.disabled = true;
                resolveButton.classList.add('opacity-50', 'cursor-not-allowed');
                resolveButton.classList.remove('hover:bg-blue-700');
            });
    }

    // Store the current request ID for use in the resolve function
    let currentRequestId = '';

    function closeModal() {
        document.getElementById('requestDetailsModal').classList.add('hidden');
        currentRequestId = ''; // Clear the current request ID
    }

    function resolveRequest() {
        if (!currentRequestId) {
            console.error('No request ID available');
            return;
        }

        // Disable the button to prevent multiple clicks
        const resolveButton = document.getElementById('resolveButton');
        resolveButton.disabled = true;
        resolveButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Processing...';
        resolveButton.classList.add('opacity-75');

        // Send request to mark as resolved
        fetch(`{{ tenant_prefix }}dashboard/resolve-support-request/${currentRequestId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`Request failed with status ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                // Show success message
                const messagesListElement = document.getElementById('messagesList');
                messagesListElement.innerHTML += `
                    <div class="p-3 mt-3 bg-green-50 border-l-4 border-green-500 rounded-md">
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-green-500 mr-2"></i>
                            <p class="text-sm font-medium text-green-800">${data.message}</p>
                        </div>
                    </div>
                `;

                // Update the status in the modal
                const statusElement = document.getElementById('requestStatus');
                statusElement.textContent = 'Resolved';
                statusElement.className = 'text-sm font-medium text-green-800';

                // Update the button
                resolveButton.innerHTML = 'Already Resolved';
                resolveButton.disabled = true;
                resolveButton.classList.add('opacity-50', 'cursor-not-allowed');
                resolveButton.classList.remove('hover:bg-indigo-700');

                // Update the status in the table
                const tableRow = document.querySelector(`tr[data-request-id="${currentRequestId}"]`);
                if (tableRow) {
                    const statusCell = tableRow.querySelector('td:nth-child(4)');
                    if (statusCell) {
                        statusCell.innerHTML = '<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Resolved</span>';
                    }
                }
            } else {
                // Show error message
                alert(data.message || 'Failed to resolve the request');

                // Re-enable the button
                resolveButton.disabled = false;
                resolveButton.innerHTML = 'Mark as Resolved';
                resolveButton.classList.remove('opacity-75');
            }
        })
        .catch(error => {
            console.error('Error resolving request:', error);
            alert('Failed to resolve the request. Please try again.');

            // Re-enable the button
            resolveButton.disabled = false;
            resolveButton.innerHTML = 'Mark as Resolved';
            resolveButton.classList.remove('opacity-75');
        });
    }

    // Close modal when clicking outside of it
    document.getElementById('requestDetailsModal').addEventListener('click', function(event) {
        if (event.target === this) {
            closeModal();
        }
    });

    // Function to update the notification preview
    function updateNotificationPreview() {
        const status = document.getElementById('notificationStatus').value;
        const message = document.getElementById('notificationMessage').value;

        document.getElementById('templateStatus').textContent = status;
        document.getElementById('templateMessage').textContent = message;
    }

    // Add event listeners for the notification form
    document.getElementById('notificationStatus').addEventListener('change', updateNotificationPreview);
    document.getElementById('notificationMessage').addEventListener('input', updateNotificationPreview);

    // Handle send notification button click
    document.getElementById('sendNotificationButton').addEventListener('click', function() {
        // Get form values
        const status = document.getElementById('notificationStatus').value;
        const message = document.getElementById('notificationMessage').value;

        if (!message.trim()) {
            alert('Please enter a message');
            return;
        }

        // Disable the button to prevent multiple clicks
        const sendButton = document.getElementById('sendNotificationButton');
        sendButton.disabled = true;
        sendButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Sending...';

        // Prepare the notification data
        const notificationData = {
            request_id: currentRequestId,
            user_id: currentRequestDetails.user_id,
            status: status,
            message: message,
            query_summary: document.getElementById('templateQuerySummary').textContent
        };

        // Send the notification
        fetch('{{ tenant_prefix }}dashboard/send-support-notification', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(notificationData)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`Request failed with status ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            // Show success message
            const responseContainer = document.getElementById('notificationResponse');
            responseContainer.className = 'mb-4 p-3 bg-green-50 border-l-4 border-green-500 rounded-md';
            responseContainer.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-check-circle text-green-500 mr-2"></i>
                    <p class="text-sm font-medium text-green-800">${data.message || 'Notification sent successfully'}</p>
                </div>
            `;
            responseContainer.classList.remove('hidden');

            // Reset button
            sendButton.disabled = false;
            sendButton.innerHTML = '<i class="fas fa-paper-plane mr-1.5"></i> Send Notification';

            // Add the notification to the messages list
            const messagesListElement = document.getElementById('messagesList');
            const messageElement = document.createElement('div');
            messageElement.className = 'p-3 rounded-lg bg-green-50 border-l-4 border-green-500';

            const currentTime = new Date().toLocaleString();
            messageElement.innerHTML = `
                <div class="flex justify-between items-start">
                    <p class="text-sm font-medium text-gray-800">${message}</p>
                    <span class="text-xs text-gray-500">${currentTime}</span>
                </div>
                <p class="text-xs text-gray-500 mt-1">Admin Notification</p>
            `;

            messagesListElement.appendChild(messageElement);
            messagesListElement.scrollTop = messagesListElement.scrollHeight;
        })
        .catch(error => {
            console.error('Error sending notification:', error);

            // Show error message
            const responseContainer = document.getElementById('notificationResponse');
            responseContainer.className = 'mb-4 p-3 bg-red-50 border-l-4 border-red-500 rounded-md';
            responseContainer.innerHTML = `
                <div class="flex items-center">
                    <i class="fas fa-exclamation-circle text-red-500 mr-2"></i>
                    <p class="text-sm font-medium text-red-800">Failed to send notification</p>
                </div>
                <p class="text-sm text-red-700 mt-1">${error.message}</p>
            `;
            responseContainer.classList.remove('hidden');

            // Reset button
            sendButton.disabled = false;
            sendButton.innerHTML = '<i class="fas fa-paper-plane mr-1.5"></i> Send Notification';
        });
    });
</script>
{% endblock %}