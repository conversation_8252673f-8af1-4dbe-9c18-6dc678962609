{% extends 'base.html' %}

{% block title %}Users{% endblock %}
{% block header_title %}Users{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/users_mobile.css') }}">
{% endblock %}

{% block content %}
<!-- Users Table Card -->
<div class="bg-white rounded-xl shadow-md mb-8 border border-gray-100 overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center bg-gradient-to-r from-indigo-50 to-white">
        <div class="flex items-center">
            <i class="fas fa-users text-indigo-500 mr-3"></i>
            <h2 class="text-lg font-semibold text-gray-800">User Management</h2>
        </div>
        <div class="flex space-x-2">
            <a href="{{ tenant_url_for('dashboard.export_users_csv_route', filter=filter_query, status=filter_status) }}" class="text-sm px-3 py-1.5 bg-green-50 text-green-600 rounded-md hover:bg-green-100 transition-colors duration-200 flex items-center">
                <i class="fas fa-file-csv mr-1.5"></i> Export CSV
            </a>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="px-6 py-3 bg-gray-50 border-b border-gray-200">
        <form method="GET" action="{{ tenant_url_for('dashboard.users_route') }}" class="flex flex-wrap items-center gap-3">
            <div class="flex-1 min-w-[200px]">
                <div class="relative">
                    <input type="text" name="filter" value="{{ filter_query }}" placeholder="Search by name, email, phone, access code..." class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-200 focus:border-indigo-400 transition-colors text-sm">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                </div>
            </div>
            <div class="w-auto">
                <select name="status" class="pl-3 pr-8 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-200 focus:border-indigo-400 transition-colors text-sm appearance-none bg-white">
                    <option value="all" {% if filter_status == 'all' %}selected{% endif %}>All Status</option>
                    {% for status in status_values %}
                    <option value="{{ status }}" {% if filter_status == status %}selected{% endif %}>{{ status|title }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="w-auto">
                <select name="per_page" class="pl-3 pr-8 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-200 focus:border-indigo-400 transition-colors text-sm appearance-none bg-white">
                    {% for pp in pagination.allowed_per_page %}
                    <option value="{{ pp }}" {% if pagination.per_page == pp %}selected{% endif %}>{{ pp }} per page</option>
                    {% endfor %}
                </select>
            </div>
            <div class="w-auto">
                <button type="submit" class="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors duration-200 text-sm flex items-center">
                    <i class="fas fa-filter mr-1.5"></i> Apply Filters
                </button>
            </div>
        </form>
    </div>

    <!-- Table Section -->
    <div class="overflow-x-auto">
        {% if users %}
        <table class="min-w-full divide-y divide-gray-200">
            <thead>
                <tr class="bg-gray-50">
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                        <a href="{{ tenant_url_for('dashboard.users_route', sort='user_id', order='asc' if sort_field == 'user_id' and sort_order == 'desc' else 'desc', filter=filter_query, status=filter_status, per_page=pagination.per_page) }}" class="flex items-center hover:text-gray-700">
                            User ID
                            {% if sort_field == 'user_id' %}
                                <i class="fas fa-sort-{{ 'up' if sort_order == 'asc' else 'down' }} ml-1 text-indigo-500"></i>
                            {% else %}
                                <i class="fas fa-sort ml-1 text-gray-400"></i>
                            {% endif %}
                        </a>
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                        <a href="{{ tenant_url_for('dashboard.users_route', sort='name', order='asc' if sort_field == 'name' and sort_order == 'desc' else 'desc', filter=filter_query, status=filter_status, per_page=pagination.per_page) }}" class="flex items-center hover:text-gray-700">
                            Name
                            {% if sort_field == 'name' %}
                                <i class="fas fa-sort-{{ 'up' if sort_order == 'asc' else 'down' }} ml-1 text-indigo-500"></i>
                            {% else %}
                                <i class="fas fa-sort ml-1 text-gray-400"></i>
                            {% endif %}
                        </a>
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                        Contact Info
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                        <a href="{{ tenant_url_for('dashboard.users_route', sort='access_code', order='asc' if sort_field == 'access_code' and sort_order == 'desc' else 'desc', filter=filter_query, status=filter_status, per_page=pagination.per_page) }}" class="flex items-center hover:text-gray-700">
                            Access Code
                            {% if sort_field == 'access_code' %}
                                <i class="fas fa-sort-{{ 'up' if sort_order == 'asc' else 'down' }} ml-1 text-indigo-500"></i>
                            {% else %}
                                <i class="fas fa-sort ml-1 text-gray-400"></i>
                            {% endif %}
                        </a>
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                        <a href="{{ tenant_url_for('dashboard.users_route', sort='user_status', order='asc' if sort_field == 'user_status' and sort_order == 'desc' else 'desc', filter=filter_query, status=filter_status, per_page=pagination.per_page) }}" class="flex items-center hover:text-gray-700">
                            Status
                            {% if sort_field == 'user_status' %}
                                <i class="fas fa-sort-{{ 'up' if sort_order == 'asc' else 'down' }} ml-1 text-indigo-500"></i>
                            {% else %}
                                <i class="fas fa-sort ml-1 text-gray-400"></i>
                            {% endif %}
                        </a>
                    </th>

                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                        <a href="{{ tenant_url_for('dashboard.users_route', sort='trading_experience', order='asc' if sort_field == 'trading_experience' and sort_order == 'desc' else 'desc', filter=filter_query, status=filter_status, per_page=pagination.per_page) }}" class="flex items-center hover:text-gray-700">
                            Experience
                            {% if sort_field == 'trading_experience' %}
                                <i class="fas fa-sort-{{ 'up' if sort_order == 'asc' else 'down' }} ml-1 text-indigo-500"></i>
                            {% else %}
                                <i class="fas fa-sort ml-1 text-gray-400"></i>
                            {% endif %}
                        </a>
                    </th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                        Actions
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for user in users %}
                <tr class="hover:bg-gray-50 transition-colors duration-150">
                    <td class="px-6 py-4 whitespace-nowrap" data-label="USER ID">
                        <a href="{{ tenant_url_for('dashboard.user_detail_route', user_id=user.user_id) }}" class="text-indigo-600 hover:text-indigo-800 hover:underline font-medium">
                            {{ user.user_id }}
                        </a>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap" data-label="NAME">
                        <div class="font-medium text-gray-800">{{ user.name or 'N/A' }}</div>
                    </td>
                    <td class="px-6 py-4" data-label="EMAIL">
                        <div class="text-sm text-gray-700">
                            {% if user.email %}
                            <div class="flex items-center mb-1">
                                <i class="fas fa-envelope text-gray-400 w-4 mr-1.5"></i>
                                <span>{{ user.email }}</span>
                            </div>
                            {% endif %}
                            {% if user.whatsapp %}
                            <div class="flex items-center">
                                <i class="fab fa-whatsapp text-gray-400 w-4 mr-1.5"></i>
                                <span>{{ user.whatsapp }}</span>
                            </div>
                            {% endif %}
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap" data-label="ACCESS">
                        <span class="font-mono text-sm">{{ user.access_code or 'N/A' }}</span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap" data-label="STATUS">
                        {% if user.user_status == 'active' %}
                        <span class="px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200">
                            ACTIVE
                        </span>
                        {% elif user.user_status == 'inactive' %}
                        <span class="px-3 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 border border-red-200">
                            INACTIVE
                        </span>
                        {% elif user.user_status == 'pending_verification' %}
                        <span class="px-3 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 border border-yellow-200">
                            PENDING VERIFICATION
                        </span>
                        {% elif user.user_status == 'incomplete' %}
                        <span class="px-3 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800 border border-orange-200">
                            INCOMPLETE
                        </span>
                        {% elif user.user_status == 'new' %}
                        <span class="px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 border border-blue-200">
                            NEW
                        </span>
                        {% else %}
                        <span class="px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 border border-gray-200">
                            {{ user.user_status|replace('_', ' ')|upper }}
                        </span>
                        {% endif %}
                    </td>

                    <td class="px-6 py-4 whitespace-nowrap" data-label="EXPERIENCE">
                        {% if user.trading_experience == 'beginner' %}
                        <span class="px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            Beginner
                        </span>
                        {% elif user.trading_experience == 'intermediate' %}
                        <span class="px-3 py-1 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                            Intermediate
                        </span>
                        {% elif user.trading_experience == 'pro' %}
                        <span class="px-3 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                            Pro
                        </span>
                        {% else %}
                        <span class="px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            {{ user.trading_experience|title if user.trading_experience else 'N/A' }}
                        </span>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium" data-label="ACTIONS">
                        <a href="{{ tenant_url_for('dashboard.user_detail_route', user_id=user.user_id) }}" class="text-indigo-600 hover:text-indigo-900 mr-3">
                            <i class="fas fa-eye"></i>
                        </a>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <!-- Pagination -->
        <div class="px-6 py-4 bg-gray-50 border-t border-gray-200 flex items-center justify-between">
            <div class="text-sm text-gray-500">
                Showing <span class="font-medium text-gray-700">{{ start_item }}</span> to <span class="font-medium text-gray-700">{{ end_item }}</span> of <span class="font-medium text-gray-700">{{ pagination.total }}</span> users
            </div>
            <div class="flex space-x-1">
                {% if pagination.has_prev %}
                <a href="{{ tenant_url_for('dashboard.users_route', page=pagination.page-1, per_page=pagination.per_page, sort=sort_field, order=sort_order, filter=filter_query, status=filter_status) }}" class="px-3 py-1 bg-white border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                    <i class="fas fa-chevron-left"></i>
                </a>
                {% else %}
                <span class="px-3 py-1 bg-gray-100 border border-gray-300 rounded-md text-sm text-gray-400 cursor-not-allowed">
                    <i class="fas fa-chevron-left"></i>
                </span>
                {% endif %}

                {% set window_size = 5 %}
                {% set window_start = [1, pagination.page - (window_size // 2)]|max %}
                {% set window_end = [window_start + window_size - 1, pagination.total_pages]|min %}

                {% if window_start > 1 %}
                <a href="{{ tenant_url_for('dashboard.users_route', page=1, per_page=pagination.per_page, sort=sort_field, order=sort_order, filter=filter_query, status=filter_status) }}" class="px-3 py-1 bg-white border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                    1
                </a>
                {% if window_start > 2 %}
                <span class="px-2 py-1 text-gray-500">...</span>
                {% endif %}
                {% endif %}

                {% for p in range(window_start, window_end + 1) %}
                {% if p == pagination.page %}
                <span class="px-3 py-1 bg-indigo-600 border border-indigo-600 rounded-md text-sm text-white">
                    {{ p }}
                </span>
                {% else %}
                <a href="{{ tenant_url_for('dashboard.users_route', page=p, per_page=pagination.per_page, sort=sort_field, order=sort_order, filter=filter_query, status=filter_status) }}" class="px-3 py-1 bg-white border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                    {{ p }}
                </a>
                {% endif %}
                {% endfor %}

                {% if window_end < pagination.total_pages %}
                {% if window_end < pagination.total_pages - 1 %}
                <span class="px-2 py-1 text-gray-500">...</span>
                {% endif %}
                <a href="{{ tenant_url_for('dashboard.users_route', page=pagination.total_pages, per_page=pagination.per_page, sort=sort_field, order=sort_order, filter=filter_query, status=filter_status) }}" class="px-3 py-1 bg-white border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                    {{ pagination.total_pages }}
                </a>
                {% endif %}

                {% if pagination.has_next %}
                <a href="{{ tenant_url_for('dashboard.users_route', page=pagination.page+1, per_page=pagination.per_page, sort=sort_field, order=sort_order, filter=filter_query, status=filter_status) }}" class="px-3 py-1 bg-white border border-gray-300 rounded-md text-sm text-gray-700 hover:bg-gray-50 transition-colors">
                    <i class="fas fa-chevron-right"></i>
                </a>
                {% else %}
                <span class="px-3 py-1 bg-gray-100 border border-gray-300 rounded-md text-sm text-gray-400 cursor-not-allowed">
                    <i class="fas fa-chevron-right"></i>
                </span>
                {% endif %}
            </div>
        </div>
        {% else %}
        <div class="px-6 py-12 text-center">
            <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-indigo-100 text-indigo-400 mb-4">
                <i class="fas fa-users text-2xl"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No users found</h3>
            <p class="text-gray-500 max-w-md mx-auto">
                {% if filter_query or filter_status != 'all' %}
                No users match your current filters. Try adjusting your search criteria.
                {% else %}
                There are no users in the system yet.
                {% endif %}
            </p>
            {% if filter_query or filter_status != 'all' %}
            <div class="mt-4">
                <a href="{{ tenant_url_for('dashboard.users_route') }}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-indigo-700 bg-indigo-100 hover:bg-indigo-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <i class="fas fa-times-circle mr-2"></i>
                    Clear Filters
                </a>
            </div>
            {% endif %}
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add hover effect to table rows (desktop only)
        if (window.innerWidth > 768) {
            const tableRows = document.querySelectorAll('tbody tr');
            tableRows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.classList.add('bg-gray-50');
                });
                row.addEventListener('mouseleave', function() {
                    this.classList.remove('bg-gray-50');
                });
            });
        }

        // Add click event to mobile cards to navigate to user detail
        if (window.innerWidth <= 768) {
            const tableRows = document.querySelectorAll('tbody tr');
            tableRows.forEach(row => {
                // Get the user ID link from the first cell
                const userIdLink = row.querySelector('td:first-child a');
                if (userIdLink) {
                    const href = userIdLink.getAttribute('href');
                    // Make the entire row clickable except for action buttons
                    row.addEventListener('click', function(e) {
                        // Don't trigger if clicking on action buttons
                        if (!e.target.closest('td:last-child')) {
                            window.location.href = href;
                        }
                    });
                    // Add cursor pointer to indicate clickable
                    row.style.cursor = 'pointer';
                }
            });
        }
    });
</script>
{% endblock %}
