{% extends 'base.html' %}

{% block title %}Verification Requests{% endblock %}
{% block header_title %}Verification Requests{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/verification_requests_mobile.css') }}">
<link rel="stylesheet" href="{{ url_for('static', filename='css/verification_requests.css') }}">
<style>
    /* Additional custom styles can be added here */
</style>
{% endblock %}

{% block content %}
    <!-- Verification Requests Table Card -->
    <div class="bg-white rounded-xl shadow-md mb-8 border border-gray-100 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center bg-gradient-to-r from-indigo-50 to-white">
            <div class="flex items-center">
                <i class="fas fa-user-check text-indigo-500 mr-3"></i>
                <h2 class="text-lg font-semibold text-gray-800">Verification Requests</h2>
            </div>
        </div>

        {% if requests %}
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
            <thead>
                <tr class="bg-gray-50">
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Request ID</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User Name</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User Email</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">WhatsApp</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Access Code</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created At</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for request in requests %}
                <tr class="{% if loop.index is odd %}bg-white{% else %}bg-gray-50{% endif %} hover:bg-gray-100 transition-colors duration-150 {% if not request.access_code_exists %}border-l-4 border-red-400{% endif %}" data-request-id="{{ request.request_id }}" title="{% if not request.access_code_exists %}Access code not found in the system{% endif %}">
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900" data-label="REQUEST ID">{{ request.request_id }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700" data-label="NAME">{{ request.user_details.name if request.user_details is defined else 'N/A' }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700" data-label="EMAIL">{{ request.user_details.email if request.user_details is defined else 'N/A' }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700" data-label="WHATSAPP">{{ request.user_details.whatsapp if request.user_details is defined else 'N/A' }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700" data-label="ACCESS CODE">
                        {% if request.access_code %}
                            <div class="flex items-center">
                                <span class="mr-2">{{ request.access_code }}</span>
                                {% if request.access_code_exists %}
                                    <span class="text-green-500" title="Access code exists in the system"><i class="fas fa-check-circle"></i></span>
                                {% else %}
                                    <span class="text-red-500" title="Access code not found in the system"><i class="fas fa-exclamation-circle"></i></span>
                                {% endif %}
                            </div>
                        {% else %}
                            N/A
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700" data-label="STATUS">
                        {% if request.status == 'pending' %}
                        <span class="px-2.5 py-0.5 inline-flex text-xs leading-5 font-medium rounded-full bg-yellow-100 text-yellow-800">Pending</span>
                        {% elif request.status == 'approved' %}
                        <span class="px-2.5 py-0.5 inline-flex text-xs leading-5 font-medium rounded-full bg-green-100 text-green-800">Approved</span>
                        {% elif request.status == 'denied' %}
                        <span class="px-2.5 py-0.5 inline-flex text-xs leading-5 font-medium rounded-full bg-red-100 text-red-800">Denied</span>
                        {% else %}
                        <span class="px-2.5 py-0.5 inline-flex text-xs leading-5 font-medium rounded-full bg-gray-100 text-gray-800">{{ request.status }}</span>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-700" data-label="CREATED AT">{{ request.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium" data-label="ACTIONS">
                        <button onclick="viewRequestDetails('{{ request.request_id }}')" class="text-indigo-600 hover:text-indigo-900 focus:outline-none transition-colors duration-150 flex items-center">
                            <i class="fas fa-eye mr-1.5 text-xs"></i> View Details
                        </button>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
            </table>
        </div>

        {% else %}
        <div class="p-8">
            <div class="flex flex-col items-center justify-center py-12">
                <div class="rounded-full bg-gray-50 p-6 mb-4">
                    <i class="fas fa-user-check text-4xl text-indigo-200"></i>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No Verification Requests</h3>
                <p class="text-gray-500 text-center max-w-md">Verification requests will appear here when users submit them.</p>
            </div>
        </div>
        {% endif %}
    </div>

    <!-- Verification Request Details Modal -->
    <div id="requestDetailsModal" class="fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-xl shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto border border-gray-200">

            <!-- Modal Header -->
            <div class="px-6 py-5 border-b border-gray-200 flex justify-between items-center sticky top-0 bg-gradient-to-r from-indigo-50 to-white z-10 shadow-sm">
                <div class="flex items-center">
                    <i class="fas fa-user-check text-indigo-500 mr-3 text-xl"></i>
                    <h3 class="text-lg font-semibold text-gray-800">Verification Request Details</h3>
                </div>
                <button onclick="closeModal()" class="text-gray-400 hover:text-gray-600 focus:outline-none transition-colors duration-150">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- Modal Content -->
            <div class="px-6 py-6">
                <!-- Loading indicator -->
                <div id="loadingIndicator" class="flex flex-col justify-center items-center py-12">
                    <div class="animate-spin rounded-full h-16 w-16 border-b-4 border-indigo-500 mb-4"></div>
                    <p class="text-gray-600 font-medium">Loading request details...</p>
                    <p class="text-gray-500 text-sm mt-1">Please wait</p>
                </div>

                <!-- Request details will be populated here -->
                <div id="requestDetails" class="hidden space-y-6">
                    <!-- Request Info -->
                    <div class="mb-6">
                        <div class="flex items-center mb-3 border-b border-gray-200 pb-2">
                            <i class="fas fa-info-circle text-indigo-500 mr-2 text-lg"></i>
                            <h4 class="text-md font-semibold text-gray-800">Request Information</h4>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 bg-gray-50 p-5 rounded-lg shadow-inner border border-gray-100">
                            <div>
                                <p class="text-xs uppercase tracking-wider text-gray-500 mb-1">Request ID</p>
                                <p id="detailRequestId" class="text-sm font-medium text-gray-800">-</p>
                            </div>
                            <div>
                                <p class="text-xs uppercase tracking-wider text-gray-500 mb-1">Status</p>
                                <p id="detailStatus" class="text-sm font-medium">-</p>
                            </div>
                            <div>
                                <p class="text-xs uppercase tracking-wider text-gray-500 mb-1">Access Code</p>
                                <div class="flex items-center">
                                    <p id="detailAccessCode" class="text-sm font-medium text-gray-800 mr-2">-</p>
                                    <span id="accessCodeStatus" class="hidden"></span>
                                </div>
                            </div>
                            <div>
                                <p class="text-xs uppercase tracking-wider text-gray-500 mb-1">Verified</p>
                                <p id="detailVerified" class="text-sm font-medium text-gray-800">-</p>
                            </div>
                            <div>
                                <p class="text-xs uppercase tracking-wider text-gray-500 mb-1">Created At</p>
                                <p id="detailCreatedAt" class="text-sm font-medium text-gray-800">-</p>
                            </div>
                            <div>
                                <p class="text-xs uppercase tracking-wider text-gray-500 mb-1">Updated At</p>
                                <p id="detailUpdatedAt" class="text-sm font-medium text-gray-800">-</p>
                            </div>
                        </div>
                    </div>

                    <!-- User Info -->
                    <div class="mb-6">
                        <div class="flex items-center mb-3 border-b border-gray-200 pb-2">
                            <i class="fas fa-user text-indigo-500 mr-2 text-lg"></i>
                            <h4 class="text-md font-semibold text-gray-800">User Information</h4>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 bg-gray-50 p-5 rounded-lg shadow-inner border border-gray-100">
                            <div>
                                <p class="text-xs uppercase tracking-wider text-gray-500 mb-1">User ID</p>
                                <p id="detailUserId" class="text-sm font-medium text-gray-800">-</p>
                            </div>
                            <div>
                                <p class="text-xs uppercase tracking-wider text-gray-500 mb-1">Name</p>
                                <p id="detailUserName" class="text-sm font-medium text-gray-800">-</p>
                            </div>
                            <div>
                                <p class="text-xs uppercase tracking-wider text-gray-500 mb-1">Email</p>
                                <p id="detailUserEmail" class="text-sm font-medium text-gray-800">-</p>
                            </div>
                            <div>
                                <p class="text-xs uppercase tracking-wider text-gray-500 mb-1">WhatsApp</p>
                                <p id="detailUserWhatsapp" class="text-sm font-medium text-gray-800">-</p>
                            </div>
                        </div>
                    </div>

                    <!-- Verification History -->
                    <div class="mb-6">
                        <div class="flex items-center mb-3 border-b border-gray-200 pb-2">
                            <i class="fas fa-history text-indigo-500 mr-2 text-lg"></i>
                            <h4 class="text-md font-semibold text-gray-800">Verification History</h4>
                        </div>
                        <div id="historyList" class="space-y-3 bg-gray-50 p-5 rounded-lg shadow-inner border border-gray-100 max-h-[300px] overflow-y-auto">
                            <!-- History items will be populated here -->
                            <div class="flex flex-col items-center justify-center py-6">
                                <i class="fas fa-history text-gray-300 text-3xl mb-2"></i>
                                <p class="text-sm text-gray-500">No verification history available</p>
                            </div>
                        </div>
                    </div>

                    <!-- Admin Notes -->
                    <div class="mb-6">
                        <div class="flex items-center mb-3 border-b border-gray-200 pb-2">
                            <i class="fas fa-sticky-note text-indigo-500 mr-2 text-lg"></i>
                            <h4 class="text-md font-semibold text-gray-800">Admin Notes</h4>
                        </div>
                        <div id="adminNotesList" class="space-y-3 bg-gray-50 p-5 rounded-lg shadow-inner border border-gray-100 max-h-[200px] overflow-y-auto mb-4">
                            <!-- Admin notes will be populated here -->
                            <div class="flex flex-col items-center justify-center py-6">
                                <i class="fas fa-sticky-note text-gray-300 text-3xl mb-2"></i>
                                <p class="text-sm text-gray-500">No admin notes available</p>
                            </div>
                        </div>

                        <!-- Add Admin Note Form -->
                        <div class="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
                            <h5 class="text-sm font-medium text-gray-700 mb-3 flex items-center">
                                <i class="fas fa-plus-circle text-indigo-500 mr-2"></i>
                                Add New Note
                            </h5>
                            <div class="space-y-3">
                                <textarea id="adminNoteText" class="w-full px-3 py-2 text-gray-700 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" rows="3" placeholder="Enter your note here..."></textarea>
                                <button id="addAdminNoteBtn" onclick="addAdminNote()" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 focus:outline-none transition-all duration-200 shadow-sm font-medium flex items-center">
                                    <i class="fas fa-save mr-2"></i> Save Note
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modal Footer -->
            <div id="requestActionButtons" class="px-6 py-4 border-t border-gray-200 flex justify-end sticky bottom-0 bg-white z-10 hidden shadow-md">
                <button onclick="closeModal()" class="px-5 py-2.5 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 focus:outline-none mr-3 transition-all duration-200 shadow-sm font-medium flex items-center">
                    <i class="fas fa-times mr-2"></i> Close
                </button>
                <form id="approveForm" method="POST" style="display: inline;" onsubmit="return handleApproveSubmit(event)">
                    <button type="submit" class="px-5 py-2.5 bg-green-600 text-white rounded-lg hover:bg-green-700 focus:outline-none transition-all duration-200 shadow-md mr-3 font-medium flex items-center">
                        <i class="fas fa-check mr-2"></i> Approve
                    </button>
                </form>
                <form id="denyForm" method="POST" style="display: inline;" onsubmit="return handleDenySubmit(event)">
                    <button type="submit" class="px-5 py-2.5 bg-red-600 text-white rounded-lg hover:bg-red-700 focus:outline-none transition-all duration-200 shadow-md font-medium flex items-center">
                        <i class="fas fa-times mr-2"></i> Deny
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Confirmation Modal -->
<div id="confirmationModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center bg-gradient-to-r from-indigo-50 to-white">
            <div class="flex items-center">
                <i id="confirmIcon" class="fas fa-question-circle text-indigo-500 mr-3 text-xl"></i>
                <h3 id="confirmTitle" class="text-lg font-semibold text-gray-800">Confirmation</h3>
            </div>
            <button onclick="closeConfirmModal()" class="text-gray-400 hover:text-gray-600 focus:outline-none transition-colors">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="px-6 py-4">
            <p id="confirmMessage" class="text-gray-700 mb-4">Are you sure you want to perform this action?</p>
        </div>
        <div class="px-6 py-3 bg-gray-50 border-t border-gray-200 flex justify-end">
            <button onclick="closeConfirmModal()" class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none mr-3 transition-all duration-200">
                Cancel
            </button>
            <button id="confirmButton" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none transition-all duration-200">
                Confirm
            </button>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    // Add mobile-specific functionality
    document.addEventListener('DOMContentLoaded', function() {
        // Make entire row clickable on mobile
        if (window.innerWidth <= 768) {
            const tableRows = document.querySelectorAll('tbody tr');
            tableRows.forEach(row => {
                row.addEventListener('click', function(e) {
                    // Don't trigger if clicking on a button or link
                    if (!e.target.closest('button') && !e.target.closest('a')) {
                        const requestId = this.getAttribute('data-request-id');
                        if (requestId) {
                            viewRequestDetails(requestId);
                        }
                    }
                });
                // Add cursor pointer to indicate clickable
                row.style.cursor = 'pointer';
            });
        }
    });

    // Store the current request ID for use in the functions
    let currentRequestId = '';

    // Variables to store form submission data
    let currentForm = null;
    let currentAction = '';
    let originalButtonText = '';
    let actionButton = null;

    // Function to show toast notifications
    function showToast(type, message) {
        const toast = document.createElement('div');
        toast.className = `fixed bottom-4 right-4 px-6 py-3 rounded-lg shadow-lg z-50 ${
            type === 'error' ? 'bg-red-500 text-white' :
            type === 'success' ? 'bg-green-500 text-white' :
            'bg-indigo-500 text-white'
        }`;
        toast.innerHTML = `
            <div class="flex items-center">
                <i class="fas ${
                    type === 'error' ? 'fa-exclamation-circle' :
                    type === 'success' ? 'fa-check-circle' :
                    'fa-info-circle'
                } mr-2"></i>
                <span>${message}</span>
            </div>
        `;
        document.body.appendChild(toast);

        // Remove the toast after 5 seconds
        setTimeout(() => {
            toast.classList.add('opacity-0', 'transition-opacity', 'duration-500');
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 500);
        }, 5000);
    }

    // Function to open confirmation modal
    function openConfirmModal(title, message, icon, buttonText, buttonClass, callback) {
        document.getElementById('confirmTitle').textContent = title;
        document.getElementById('confirmMessage').textContent = message;
        document.getElementById('confirmIcon').className = `fas ${icon} text-${buttonClass}-500 mr-3 text-xl`;

        const confirmButton = document.getElementById('confirmButton');
        confirmButton.textContent = buttonText;
        confirmButton.className = `px-4 py-2 bg-${buttonClass}-600 text-white rounded-md hover:bg-${buttonClass}-700 focus:outline-none transition-all duration-200`;

        // Set the callback function
        confirmButton.onclick = callback;

        // Show the modal
        document.getElementById('confirmationModal').classList.remove('hidden');
    }

    // Function to close confirmation modal
    function closeConfirmModal() {
        document.getElementById('confirmationModal').classList.add('hidden');
    }

    // Handle approve form submission
    function handleApproveSubmit(event) {
        event.preventDefault();

        // Store form data for later use
        currentForm = event.target;
        currentAction = 'approve';
        actionButton = event.target.querySelector('button');
        originalButtonText = actionButton.innerHTML;

        // Show custom confirmation modal
        openConfirmModal(
            'Approve Verification Request',
            'Are you sure you want to approve this verification request? This will grant the user full access to the system.',
            'fa-check-circle',
            'Approve',
            'green',
            submitVerificationAction
        );

        return false;
    }

    // Handle deny form submission
    function handleDenySubmit(event) {
        event.preventDefault();

        // Store form data for later use
        currentForm = event.target;
        currentAction = 'deny';
        actionButton = event.target.querySelector('button');
        originalButtonText = actionButton.innerHTML;

        // Show custom confirmation modal
        openConfirmModal(
            'Deny Verification Request',
            'Are you sure you want to deny this verification request? This action cannot be undone.',
            'fa-times-circle',
            'Deny',
            'red',
            submitVerificationAction
        );

        return false;
    }

    // Function to submit the verification action (approve or deny)
    function submitVerificationAction() {
        // Close the confirmation modal
        closeConfirmModal();

        // Show loading state on the button
        actionButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Processing...';
        actionButton.disabled = true;

        // Create a response container for displaying server responses
        let responseContainer = document.getElementById('responseContainer');
        if (!responseContainer) {
            responseContainer = document.createElement('div');
            responseContainer.id = 'responseContainer';
            responseContainer.className = 'mt-4 p-4 rounded-lg hidden';
            const modalContent = document.querySelector('#requestDetails');
            modalContent.appendChild(responseContainer);
        }

        // Hide any previous response
        responseContainer.classList.add('hidden');

        // Submit the form
        fetch(currentForm.action, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams(new FormData(currentForm))
        })
        .then(response => {
            // Check if the response is JSON
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
                return response.json().then(data => {
                    if (response.ok) {
                        // Success response
                        responseContainer.className = 'mt-4 p-4 rounded-lg bg-green-50 border border-green-200 text-green-700';
                        responseContainer.innerHTML = `<i class="fas fa-check-circle mr-2"></i> ${data.message || 'Operation completed successfully'}`;
                        responseContainer.classList.remove('hidden');

                        // Redirect after a short delay to show the success message
                        setTimeout(() => {
                            window.location.reload();
                        }, 1500);
                    } else {
                        // Error response
                        const errorMessage = data.error || 'An error occurred';

                        // Check if it's an access code error
                        const isAccessCodeError = errorMessage.includes('Access code') && errorMessage.includes('not available');

                        // Create a more prominent error display for access code errors
                        if (isAccessCodeError) {
                            responseContainer.className = 'mt-4 p-4 rounded-lg bg-red-50 border-l-4 border-red-500 text-red-700';
                            responseContainer.innerHTML = `
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 pt-0.5">
                                        <i class="fas fa-exclamation-triangle text-red-500 text-lg"></i>
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-sm font-medium text-red-800">Access Code Error</h3>
                                        <div class="mt-1 text-sm text-red-700">
                                            <p>${errorMessage}</p>
                                            <p class="mt-2">Please add this access code in the <a href="{{ tenant_prefix }}dashboard/access-codes" class="font-medium underline">Access Codes</a> section before approving.</p>
                                        </div>
                                    </div>
                                </div>
                            `;
                        } else {
                            responseContainer.className = 'mt-4 p-4 rounded-lg bg-red-50 border border-red-200 text-red-700';
                            responseContainer.innerHTML = `<i class="fas fa-exclamation-circle mr-2"></i> ${errorMessage}`;
                        }

                        responseContainer.classList.remove('hidden');
                        throw new Error(errorMessage);
                    }
                });
            } else if (response.redirected) {
                // Handle redirect
                window.location.href = response.url;
            } else {
                // Handle text response
                return response.text().then(text => {
                    if (response.ok) {
                        // Success response
                        responseContainer.className = 'mt-4 p-4 rounded-lg bg-green-50 border border-green-200 text-green-700';
                        responseContainer.innerHTML = `<i class="fas fa-check-circle mr-2"></i> Operation completed successfully`;
                        responseContainer.classList.remove('hidden');

                        // Redirect after a short delay to show the success message
                        setTimeout(() => {
                            window.location.reload();
                        }, 1500);
                    } else {
                        throw new Error('An error occurred');
                    }
                });
            }
        })
        .catch(error => {
            // Reset button state
            actionButton.innerHTML = originalButtonText;
            actionButton.disabled = false;

            const errorMessage = error.message || `Failed to ${currentAction} request. Please try again.`;

            // If the response container hasn't been updated yet (e.g., by the JSON handler)
            if (responseContainer.classList.contains('hidden')) {
                // Check if it's an access code error
                const isAccessCodeError = errorMessage.includes('Access code') && errorMessage.includes('not available');

                // Create a more prominent error display for access code errors
                if (isAccessCodeError) {
                    responseContainer.className = 'mt-4 p-4 rounded-lg bg-red-50 border-l-4 border-red-500 text-red-700';
                    responseContainer.innerHTML = `
                        <div class="flex items-start">
                            <div class="flex-shrink-0 pt-0.5">
                                <i class="fas fa-exclamation-triangle text-red-500 text-lg"></i>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-800">Access Code Error</h3>
                                <div class="mt-1 text-sm text-red-700">
                                    <p>${errorMessage}</p>
                                    <p class="mt-2">Please add this access code in the <a href="{{ tenant_prefix }}dashboard/access-codes" class="font-medium underline">Access Codes</a> section before approving.</p>
                                </div>
                            </div>
                        </div>
                    `;
                } else {
                    responseContainer.className = 'mt-4 p-4 rounded-lg bg-red-50 border border-red-200 text-red-700';
                    responseContainer.innerHTML = `<i class="fas fa-exclamation-circle mr-2"></i> ${errorMessage}`;
                }

                responseContainer.classList.remove('hidden');
            }

            // Also show toast notification
            showToast('error', errorMessage);
        });
    }

    // Function to view request details
    function viewRequestDetails(requestId) {
        // Set the current request ID
        currentRequestId = requestId;

        // Show loading state
        document.getElementById('requestDetailsModal').classList.remove('hidden');
        document.getElementById('loadingIndicator').classList.remove('hidden');
        document.getElementById('requestDetails').classList.add('hidden');
        document.getElementById('requestActionButtons').classList.add('hidden');

        // Remove any existing warning messages
        const existingWarnings = document.querySelectorAll('.bg-red-50.border-l-4.border-red-400');
        existingWarnings.forEach(warning => warning.remove());

        // Clear any previous response container
        const responseContainer = document.getElementById('responseContainer');
        if (responseContainer) {
            responseContainer.classList.add('hidden');
        }

        // Fetch request details
        fetch(`{{ tenant_prefix }}dashboard/verification-request-details/${requestId}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`Request failed with status ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Received data:', data); // Debug log
                // Hide loading indicator and show details
                document.getElementById('loadingIndicator').classList.add('hidden');
                document.getElementById('requestDetails').classList.remove('hidden');

                // Populate basic information
                document.getElementById('detailRequestId').textContent = data.request_id || 'N/A';
                document.getElementById('detailUserId').textContent = data.user_id || 'N/A';
                document.getElementById('detailAccessCode').textContent = data.access_code || 'N/A';

                // Show access code status
                const accessCodeStatus = document.getElementById('accessCodeStatus');
                if (data.access_code) {
                    accessCodeStatus.classList.remove('hidden');
                    if (data.access_code_exists) {
                        accessCodeStatus.innerHTML = '<span class="text-green-500" title="Access code exists in the system"><i class="fas fa-check-circle"></i></span>';
                    } else {
                        accessCodeStatus.innerHTML = '<span class="text-red-500" title="Access code not found in the system"><i class="fas fa-exclamation-circle"></i></span>';
                    }
                } else {
                    accessCodeStatus.classList.add('hidden');
                }
                document.getElementById('detailVerified').textContent = data.verified ? 'Yes' : 'No';
                document.getElementById('detailCreatedAt').textContent = data.created_at || 'N/A';
                document.getElementById('detailUpdatedAt').textContent = data.updated_at || 'N/A';

                // Set status with appropriate styling
                const statusElement = document.getElementById('detailStatus');
                statusElement.textContent = data.status.charAt(0).toUpperCase() + data.status.slice(1);

                // Apply appropriate status color
                statusElement.className = 'text-sm font-medium';
                if (data.status === 'pending') {
                    statusElement.classList.add('text-yellow-800');
                } else if (data.status === 'approved') {
                    statusElement.classList.add('text-green-800');
                } else if (data.status === 'denied') {
                    statusElement.classList.add('text-red-800');
                } else {
                    statusElement.classList.add('text-gray-800');
                }

                // Populate user details
                document.getElementById('detailUserName').textContent = data.user_details?.name || 'N/A';
                document.getElementById('detailUserEmail').textContent = data.user_details?.email || 'N/A';
                document.getElementById('detailUserWhatsapp').textContent = data.user_details?.whatsapp || 'N/A';

                // Populate verification history
                const historyList = document.getElementById('historyList');
                if (data.verification_history && data.verification_history.length > 0) {
                    historyList.innerHTML = '';
                    data.verification_history.forEach(entry => {
                        const historyElement = document.createElement('div');
                        historyElement.className = 'p-3 rounded-lg';

                        // Style based on status
                        if (entry.status === 'pending') {
                            historyElement.classList.add('bg-yellow-50', 'border-l-4', 'border-yellow-500');
                        } else if (entry.status === 'approved') {
                            historyElement.classList.add('bg-green-50', 'border-l-4', 'border-green-500');
                        } else if (entry.status === 'denied') {
                            historyElement.classList.add('bg-red-50', 'border-l-4', 'border-red-500');
                        } else {
                            historyElement.classList.add('bg-gray-100');
                        }

                        historyElement.innerHTML = `
                            <div class="flex justify-between items-start">
                                <p class="text-sm font-medium text-gray-800">${entry.status.charAt(0).toUpperCase() + entry.status.slice(1)}</p>
                                <span class="text-xs text-gray-500">${entry.timestamp}</span>
                            </div>
                            <p class="text-sm text-gray-700 mt-1">${entry.note || 'No note provided'}</p>
                        `;

                        historyList.appendChild(historyElement);
                    });
                } else {
                    historyList.innerHTML = `
                        <div class="flex flex-col items-center justify-center py-6">
                            <i class="fas fa-history text-gray-300 text-3xl mb-2"></i>
                            <p class="text-sm text-gray-500">No verification history available</p>
                        </div>
                    `;
                }

                // Populate admin notes
                const adminNotesList = document.getElementById('adminNotesList');
                if (data.admin_notes && data.admin_notes.length > 0) {
                    adminNotesList.innerHTML = '';
                    // Sort notes by timestamp in descending order (newest first)
                    const sortedNotes = [...data.admin_notes].sort((a, b) => {
                        const dateA = new Date(a.timestamp || 0);
                        const dateB = new Date(b.timestamp || 0);
                        return dateB - dateA; // Descending order
                    });

                    sortedNotes.forEach(note => {
                        const noteElement = document.createElement('div');
                        noteElement.className = 'p-3 rounded-lg bg-gray-100 border-l-4 border-indigo-500 mb-3';

                        // Format timestamp if available
                        let formattedTimestamp = note.timestamp || 'N/A';
                        if (note.timestamp) {
                            try {
                                const date = new Date(note.timestamp);
                                if (!isNaN(date)) {
                                    formattedTimestamp = date.toISOString().slice(0, 10) + ' ' +
                                                       date.toTimeString().slice(0, 5);
                                }
                            } catch (e) {
                                console.error('Error formatting timestamp:', e);
                            }
                        }

                        noteElement.innerHTML = `
                            <div class="flex justify-between items-start">
                                <p class="text-sm font-medium text-gray-800">${note.admin_name || 'Admin'}</p>
                                <span class="text-xs text-gray-500">${formattedTimestamp}</span>
                            </div>
                            <p class="text-sm text-gray-700 mt-1">${note.text || 'No note provided'}</p>
                        `;

                        adminNotesList.appendChild(noteElement);
                    });
                } else {
                    adminNotesList.innerHTML = `
                        <div class="flex flex-col items-center justify-center py-6">
                            <i class="fas fa-sticky-note text-gray-300 text-3xl mb-2"></i>
                            <p class="text-sm text-gray-500">No admin notes available</p>
                        </div>
                    `;
                }

                // Show action buttons for pending requests
                if (data.status === 'pending') {
                    document.getElementById('requestActionButtons').classList.remove('hidden');
                    document.getElementById('approveForm').action = `{{ tenant_prefix }}dashboard/verification-requests/approve/${requestId}`;
                    document.getElementById('denyForm').action = `{{ tenant_prefix }}dashboard/verification-requests/deny/${requestId}`;

                    // Show warning if access code doesn't exist
                    if (data.access_code && !data.access_code_exists) {
                        // Add warning message before the buttons
                        const warningDiv = document.createElement('div');
                        warningDiv.className = 'bg-red-50 border-l-4 border-red-400 p-4 mb-4 w-full';
                        warningDiv.innerHTML = `
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <i class="fas fa-exclamation-triangle text-red-400"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm text-red-700">
                                        <strong>Warning:</strong> Access code "${data.access_code}" does not exist in the system.
                                        You need to add this access code before approving this request.
                                    </p>
                                </div>
                            </div>
                        `;

                        // Insert the warning before the buttons
                        const actionButtons = document.getElementById('requestActionButtons');
                        actionButtons.parentNode.insertBefore(warningDiv, actionButtons);
                    }
                } else {
                    document.getElementById('requestActionButtons').classList.remove('hidden');
                    // Hide approve/deny buttons for non-pending requests
                    document.getElementById('approveForm').style.display = 'none';
                    document.getElementById('denyForm').style.display = 'none';
                }
            })
            .catch(error => {
                console.error('Error fetching request details:', error);

                // Show error in the modal instead of closing it
                document.getElementById('loadingIndicator').classList.add('hidden');
                document.getElementById('requestDetails').innerHTML = `
                    <div class="p-6 bg-red-50 border border-red-200 rounded-lg shadow-sm">
                        <div class="flex items-center mb-3 border-b border-red-200 pb-3">
                            <i class="fas fa-exclamation-circle text-red-500 mr-3 text-xl"></i>
                            <h4 class="text-lg font-semibold text-red-800">Error Loading Details</h4>
                        </div>
                        <p class="text-sm text-red-700 mb-3">${error.message}</p>
                        <p class="text-sm text-red-600 bg-red-100 p-3 rounded-md border border-red-200">
                            <i class="fas fa-info-circle mr-2"></i>
                            Please try again or contact the system administrator.
                        </p>
                    </div>
                `;
                document.getElementById('requestDetails').classList.remove('hidden');
            });
    }

    // Function to add admin note
    function addAdminNote() {
        // Get the note text
        const noteText = document.getElementById('adminNoteText').value.trim();

        // Validate input
        if (!noteText) {
            alert('Please enter a note before saving.');
            return;
        }

        if (!currentRequestId) {
            alert('No request selected. Please try again.');
            return;
        }

        // Disable the button to prevent multiple submissions
        const addNoteBtn = document.getElementById('addAdminNoteBtn');
        addNoteBtn.disabled = true;
        addNoteBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i> Saving...';

        // Send the note to the server
        fetch(`{{ tenant_prefix }}dashboard/verification-request/add-note/${currentRequestId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ note: noteText })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`Request failed with status ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                // Clear the textarea
                document.getElementById('adminNoteText').value = '';

                // Add the new note to the list
                const adminNotesList = document.getElementById('adminNotesList');

                // Remove the 'no notes' message if it exists
                if (adminNotesList.querySelector('.flex.flex-col.items-center')) {
                    adminNotesList.innerHTML = '';
                }

                // Create a new note element
                const noteElement = document.createElement('div');
                noteElement.className = 'p-3 rounded-lg bg-gray-100 border-l-4 border-blue-500';

                // Get current date and time
                const now = new Date();
                const formattedDate = now.toISOString().slice(0, 10) + ' ' +
                                     now.toTimeString().slice(0, 5);

                noteElement.innerHTML = `
                    <div class="flex justify-between items-start">
                        <p class="text-sm font-medium text-gray-800">${data.admin_name || 'Admin'}</p>
                        <span class="text-xs text-gray-500">${formattedDate}</span>
                    </div>
                    <p class="text-sm text-gray-700 mt-1">${noteText}</p>
                `;

                // Add the note to the list
                adminNotesList.prepend(noteElement);

                // Show success message
                showToast('success', 'Note Added', 'Your note has been added successfully.');
            } else {
                // Show error message
                showToast('error', 'Error', data.message || 'Failed to add note');
            }

            // Re-enable the button
            addNoteBtn.disabled = false;
            addNoteBtn.innerHTML = '<i class="fas fa-save mr-2"></i> Save Note';
        })
        .catch(error => {
            console.error('Error adding note:', error);

            // Show error message
            showToast('error', 'Error', 'Failed to add note. Please try again.');

            // Re-enable the button
            addNoteBtn.disabled = false;
            addNoteBtn.innerHTML = '<i class="fas fa-save mr-2"></i> Save Note';
        });
    }

    // Function to show toast notification
    function showToast(type, title, message) {
        // Check if toast container exists, if not create it
        let toastContainer = document.getElementById('toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'toast-container';
            toastContainer.className = 'fixed top-4 right-4 z-50 flex flex-col space-y-4';
            document.body.appendChild(toastContainer);
        }

        // Create toast element
        const toast = document.createElement('div');
        toast.className = `flex items-center p-4 mb-4 rounded-lg shadow-lg max-w-xs transform transition-all duration-300 translate-x-full opacity-0`;

        // Set background color based on type
        if (type === 'success') {
            toast.classList.add('bg-green-50', 'border-l-4', 'border-green-500');
        } else if (type === 'error') {
            toast.classList.add('bg-red-50', 'border-l-4', 'border-red-500');
        } else {
            toast.classList.add('bg-indigo-50', 'border-l-4', 'border-indigo-500');
        }

        // Set icon based on type
        let icon = '';
        if (type === 'success') {
            icon = '<i class="fas fa-check-circle text-green-500 mr-3 text-xl"></i>';
        } else if (type === 'error') {
            icon = '<i class="fas fa-exclamation-circle text-red-500 mr-3 text-xl"></i>';
        } else {
            icon = '<i class="fas fa-info-circle text-indigo-500 mr-3 text-xl"></i>';
        }

        // Set content
        toast.innerHTML = `
            ${icon}
            <div>
                <p class="text-sm font-medium ${type === 'success' ? 'text-green-800' : type === 'error' ? 'text-red-800' : 'text-blue-800'}">${title}</p>
                <p class="text-xs ${type === 'success' ? 'text-green-700' : type === 'error' ? 'text-red-700' : 'text-blue-700'} mt-1">${message}</p>
            </div>
            <button type="button" class="ml-auto -mx-1.5 -my-1.5 rounded-lg p-1.5 inline-flex h-8 w-8 ${type === 'success' ? 'text-green-500 hover:bg-green-100' : type === 'error' ? 'text-red-500 hover:bg-red-100' : 'text-indigo-500 hover:bg-blue-100'} focus:outline-none">
                <i class="fas fa-times"></i>
            </button>
        `;

        // Add toast to container
        toastContainer.appendChild(toast);

        // Animate in
        setTimeout(() => {
            toast.classList.remove('translate-x-full', 'opacity-0');
        }, 10);

        // Add click event to close button
        toast.querySelector('button').addEventListener('click', () => {
            closeToast(toast);
        });

        // Auto close after 5 seconds
        setTimeout(() => {
            closeToast(toast);
        }, 5000);
    }

    // Function to close toast
    function closeToast(toast) {
        toast.classList.add('translate-x-full', 'opacity-0');
        setTimeout(() => {
            toast.remove();
        }, 300);
    }

    // Function to close the modal
    function closeModal() {
        document.getElementById('requestDetailsModal').classList.add('hidden');
        currentRequestId = ''; // Clear the current request ID
    }

    // Close modal when clicking outside of it
    document.getElementById('requestDetailsModal').addEventListener('click', function(event) {
        if (event.target === this) {
            closeModal();
        }
    });

    // Close modal with escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && !document.getElementById('requestDetailsModal').classList.contains('hidden')) {
            closeModal();
        }
    });
</script>
{% endblock %}
