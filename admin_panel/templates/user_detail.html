{% extends 'base.html' %}

{% block title %}User Details{% endblock %}

{% block header_title %}User Profile{% endblock %}

{% block content %}
<div class="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden mb-6">
    <!-- Header with User ID and Actions -->
    <div class="bg-gradient-to-r from-indigo-50 to-white px-6 py-4 border-b border-gray-200">
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div class="flex items-center">
                <div class="h-12 w-12 rounded-full bg-indigo-100 flex items-center justify-center mr-4 shadow-sm">
                    <i class="fas fa-user text-indigo-500 text-lg"></i>
                </div>
                <div>
                    <h2 class="text-lg font-semibold text-gray-800">{{ subscription.user_details.name }}</h2>
                    <div class="flex items-center text-gray-500 text-sm">
                        <span class="font-mono bg-gray-100 px-2 py-0.5 rounded-md">ID: {{ subscription.user_id }}</span>
                        <span class="mx-2">•</span>
                        <span class="{% if subscription.user_status == 'active' %}text-green-600{% elif subscription.user_status == 'inactive' %}text-red-600{% elif subscription.user_status == 'pending_verification' %}text-yellow-600{% elif subscription.user_status == 'incomplete' %}text-orange-600{% else %}text-indigo-600{% endif %} font-medium">
                            {{ subscription.user_status|replace('_', ' ')|capitalize }}
                        </span>
                    </div>
                </div>
            </div>
            <div class="flex space-x-2">
                <a href="{{ tenant_url_for('dashboard.home_route') }}" class="px-4 py-2 bg-white text-gray-700 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors duration-200 flex items-center shadow-sm text-sm">
                    <i class="fas fa-arrow-left mr-2 text-gray-400"></i>Back
                </a>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="p-6">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- User Information Card -->
            <div class="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
                <div class="px-4 py-3 border-b border-gray-200 bg-gradient-to-r from-indigo-50 to-white">
                    <div class="flex items-center">
                        <i class="fas fa-user-circle text-indigo-500 mr-2"></i>
                        <h3 class="font-semibold text-gray-800 text-sm">User Information</h3>
                    </div>
                </div>
                <div class="p-4 space-y-2">
                    <div class="flex items-center py-2 border-b border-gray-100">
                        <div class="w-8 text-center text-indigo-400"><i class="fab fa-telegram"></i></div>
                        <div class="flex-1">
                            <div class="text-xs text-gray-500 mb-1">Telegram Name</div>
                            <div class="font-medium text-sm">{{ subscription.user_details.name }}</div>
                        </div>
                    </div>
                    <div class="flex items-center py-2 border-b border-gray-100">
                        <div class="w-8 text-center text-indigo-400"><i class="fas fa-envelope"></i></div>
                        <div class="flex-1">
                            <div class="text-xs text-gray-500 mb-1">Email</div>
                            <div class="font-medium text-sm">{{ subscription.user_details.email }}</div>
                        </div>
                    </div>
                    <div class="flex items-center py-2 border-b border-gray-100">
                        <div class="w-8 text-center text-indigo-400"><i class="fab fa-whatsapp"></i></div>
                        <div class="flex-1">
                            <div class="text-xs text-gray-500 mb-1">WhatsApp</div>
                            <div class="font-medium text-sm">{{ subscription.user_details.whatsapp }}</div>
                        </div>
                    </div>
                    <div class="flex items-center py-2">
                        <div class="w-8 text-center text-indigo-400"><i class="fas fa-chart-line"></i></div>
                        <div class="flex-1">
                            <div class="text-xs text-gray-500 mb-1">Trading Experience</div>
                            <div class="font-medium text-sm">{{ subscription.user_details.trading_experience }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Subscription Details Card -->
            <div class="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
                <div class="px-4 py-3 border-b border-gray-200 bg-gradient-to-r from-indigo-50 to-white">
                    <div class="flex items-center">
                        <i class="fas fa-id-card text-indigo-500 mr-2"></i>
                        <h3 class="font-semibold text-gray-800 text-sm">Subscription Details</h3>
                    </div>
                </div>
                <div class="relative">
                    <!-- Fade effect positioned absolutely -->
                    <div id="subscriptionDetailsFade" class="absolute bottom-0 left-0 right-0 h-24 bg-gradient-to-t from-white via-white to-transparent pointer-events-none z-10"></div>
                    <!-- Content container -->
                    <div id="subscriptionDetailsContent" class="p-4 space-y-2 overflow-hidden" style="max-height: 300px; transition: max-height 0.3s ease-in-out;">
                    <div class="flex items-center py-2 border-b border-gray-100">
                        <div class="w-8 text-center text-indigo-400"><i class="fas fa-key"></i></div>
                        <div class="flex-1">
                            <div class="text-xs text-gray-500 mb-1">Access Code</div>
                            <div class="font-medium text-sm font-mono">{{ subscription.access_code }}</div>
                        </div>
                    </div>
                    <div class="flex items-center py-2 border-b border-gray-100">
                        <div class="w-8 text-center text-indigo-400"><i class="fas fa-check-circle"></i></div>
                        <div class="flex-1">
                            <div class="text-xs text-gray-500 mb-1">Status</div>
                            <div>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                    {% if subscription.user_status == 'active' %}bg-green-100 text-green-800 border border-green-200
                                    {% elif subscription.user_status == 'inactive' %}bg-red-100 text-red-800 border border-red-200
                                    {% elif subscription.user_status == 'pending_verification' %}bg-yellow-100 text-yellow-800 border border-yellow-200
                                    {% elif subscription.user_status == 'incomplete' %}bg-orange-100 text-orange-800 border border-orange-200
                                    {% else %}bg-indigo-100 text-indigo-800 border border-indigo-200{% endif %}">
                                    <span class="w-1.5 h-1.5 mr-1.5 rounded-full
                                        {% if subscription.user_status == 'active' %}bg-green-500
                                        {% elif subscription.user_status == 'inactive' %}bg-red-500
                                        {% elif subscription.user_status == 'pending_verification' %}bg-yellow-500
                                        {% elif subscription.user_status == 'incomplete' %}bg-orange-500
                                        {% else %}bg-indigo-500{% endif %}"></span>
                                    {{ subscription.user_status|replace('_', ' ')|upper }}
                                </span>
                            </div>
                        </div>
                    </div>
                    <!-- Removed Subscription Until section as requested -->
                    {% if subscription.subscribed_on %}
                    <div class="flex items-center py-2 border-b border-gray-100">
                        <div class="w-8 text-center text-indigo-400"><i class="fas fa-calendar-plus"></i></div>
                        <div class="flex-1">
                            <div class="text-xs text-gray-500 mb-1">Subscribed On</div>
                            <div class="font-medium text-sm">{{ subscription.subscribed_on.strftime('%d %b, %Y %H:%M') }}</div>
                        </div>
                    </div>
                    {% endif %}
                    {% if subscription.added_at %}
                    <div class="flex items-center py-2 border-b border-gray-100">
                        <div class="w-8 text-center text-indigo-400"><i class="fas fa-user-plus"></i></div>
                        <div class="flex-1">
                            <div class="text-xs text-gray-500 mb-1">Added At</div>
                            <div class="font-medium text-sm">{{ subscription.added_at.strftime('%d-%m-%Y %H:%M') }}</div>
                        </div>
                    </div>
                    {% endif %}
                    <div class="flex items-center py-2 border-b border-gray-100">
                        <div class="w-8 text-center text-indigo-400"><i class="fas fa-building"></i></div>
                        <div class="flex-1">
                            <div class="text-xs text-gray-500 mb-1">Broker Registration</div>
                            <div>
                                {% if subscription.broker_reg %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200">
                                    <span class="w-1.5 h-1.5 mr-1.5 rounded-full bg-green-500"></span>
                                    REGISTERED
                                </span>
                                {% else %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 border border-red-200">
                                    <span class="w-1.5 h-1.5 mr-1.5 rounded-full bg-red-500"></span>
                                    NOT REGISTERED
                                </span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center py-2 border-b border-gray-100">
                        <div class="w-8 text-center text-indigo-400"><i class="fas fa-user-check"></i></div>
                        <div class="flex-1">
                            <div class="text-xs text-gray-500 mb-1">Verification Status</div>
                            <div>
                                {% if subscription.user_verify %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200">
                                    <span class="w-1.5 h-1.5 mr-1.5 rounded-full bg-green-500"></span>
                                    VERIFIED
                                </span>
                                {% else %}
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 border border-red-200">
                                    <span class="w-1.5 h-1.5 mr-1.5 rounded-full bg-red-500"></span>
                                    NOT VERIFIED
                                </span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <!-- Removed Expiration Date section as requested -->
                    <div class="flex items-center py-2">
                        <div class="w-8 text-center text-indigo-400"><i class="fas fa-hourglass-half"></i></div>
                        <div class="flex-1">
                            <div class="text-xs text-gray-500 mb-1">Days Remaining</div>
                            {% if access_code_details and access_code_details.expiration_date %}
                                {% set expiration_date_aware = access_code_details.expiration_date.replace(tzinfo=now.tzinfo) if access_code_details.expiration_date.tzinfo is none else access_code_details.expiration_date %}
                                {% set expiration_date_only = expiration_date_aware.date() %}
                                {% set current_date_only = now.date() %}
                                {% set delta = (expiration_date_only - current_date_only).days %}
                                {% set days_remaining = delta if delta > 0 else 0 %}
                                <div class="font-medium text-sm
                                    {% if days_remaining <= 0 %}text-red-600
                                    {% elif days_remaining <= 7 %}text-yellow-600
                                    {% elif days_remaining <= 30 %}text-amber-600
                                    {% else %}text-green-600{% endif %}">
                                    {% if days_remaining > 0 %}
                                        {{ days_remaining }} day{% if days_remaining != 1 %}s{% endif %} (expires {{ access_code_details.expiration_date.strftime('%d %b, %Y') }})
                                    {% else %}
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800 border border-red-200">
                                            <i class="fas fa-exclamation-circle mr-1"></i> Expired
                                        </span>
                                    {% endif %}
                                </div>
                            {% else %}
                                {% set subscription_time_aware = subscription.subscription_time.replace(tzinfo=now.tzinfo) if subscription.subscription_time.tzinfo is none else subscription.subscription_time %}
                                {% set days_remaining = (subscription_time_aware - now).days %}
                                <div class="font-medium text-sm
                                    {% if days_remaining <= 0 %}text-red-600
                                    {% elif days_remaining <= 7 %}text-yellow-600
                                    {% else %}text-green-600{% endif %}">
                                    {% if days_remaining > 0 %}
                                        {{ days_remaining }} day{% if days_remaining != 1 %}s{% endif %} (expires {{ subscription.subscription_time.strftime('%d %b, %Y') }})
                                    {% else %}
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800 border border-red-200">
                                            <i class="fas fa-exclamation-circle mr-1"></i> Expired
                                        </span>
                                    {% endif %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    </div>
                    <!-- See More button outside of the content container -->
                </div>
                <div id="seeMoreContainer" class="flex justify-center py-2 border-t border-gray-100 relative z-20">
                    <button id="seeMoreButton" class="bg-white hover:bg-indigo-50 text-gray-800 px-4 py-2 rounded-full focus:outline-none text-sm font-medium flex items-center shadow-sm transition-all duration-200 border border-indigo-200">
                        <div class="w-5 h-5 rounded-full border border-indigo-400 flex items-center justify-center mr-2 bg-indigo-50">
                            <i class="fas fa-chevron-down text-xs text-indigo-500" id="seeMoreIcon"></i>
                        </div>
                        <span id="seeMoreText" class="text-indigo-600">See More</span>
                    </button>
                </div>
            </div>

            <!-- Telegram Channel Status Card -->
            <div class="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
                <div class="px-4 py-3 border-b border-gray-200 bg-gradient-to-r from-indigo-50 to-white">
                    <div class="flex items-center">
                        <i class="fab fa-telegram text-indigo-500 mr-2"></i>
                        <h3 class="font-semibold text-gray-800 text-sm">Telegram Channel Status</h3>
                    </div>
                </div>
                <div class="p-4 space-y-2">
                    <div class="flex items-center py-2 border-b border-gray-100">
                        <div class="w-8 text-center text-indigo-400"><i class="fas fa-user-check"></i></div>
                        <div class="flex-1">
                            <div class="text-xs text-gray-500 mb-1">Channel Membership</div>
                            <div>
                                {% if telegram_status.error %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 border border-red-200">
                                        <i class="fas fa-exclamation-circle mr-1"></i> Error: {{ telegram_status.error }}
                                    </span>
                                {% else %}
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                        {% if telegram_status.is_member %}bg-green-100 text-green-800 border border-green-200{% else %}bg-red-100 text-red-800 border border-red-200{% endif %}">
                                        <span class="w-1.5 h-1.5 mr-1.5 rounded-full
                                            {% if telegram_status.is_member %}bg-green-500{% else %}bg-red-500{% endif %}"></span>
                                        {{ "Member" if telegram_status.is_member else "Not a Member" }}
                                    </span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="flex items-center py-2 border-b border-gray-100">
                        <div class="w-8 text-center text-indigo-400"><i class="fas fa-info-circle"></i></div>
                        <div class="flex-1">
                            <div class="text-xs text-gray-500 mb-1">Status</div>
                            <div class="font-medium text-sm">{{ telegram_status.status|default('Unknown') }}</div>
                        </div>
                    </div>

                    <div class="pt-3">
                        <div class="grid grid-cols-1 sm:grid-cols-3 gap-3">
                            <button id="kickUserBtn" class="flex items-center justify-center px-3 py-2 bg-white border border-red-200 text-red-600 rounded-lg hover:bg-red-50 transition-colors duration-200 shadow-sm text-xs font-medium"
                                {% if not telegram_status.is_member %}disabled class="opacity-50 cursor-not-allowed"{% endif %}>
                                <i class="fas fa-user-slash mr-1.5"></i> Kick
                            </button>

                            <button id="unbanUserBtn" class="flex items-center justify-center px-3 py-2 bg-white border border-indigo-200 text-indigo-600 rounded-lg hover:bg-indigo-50 transition-colors duration-200 shadow-sm text-xs font-medium">
                                <i class="fas fa-user-check mr-1.5"></i> Unban
                            </button>

                            <button id="sendInviteBtn" class="flex items-center justify-center px-3 py-2 bg-white border border-green-200 text-green-600 rounded-lg hover:bg-green-50 transition-colors duration-200 shadow-sm text-xs font-medium">
                                <i class="fas fa-paper-plane mr-1.5"></i> Invite
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Toast Notification Component -->
<div id="toast" class="fixed top-4 right-4 z-50 transform transition-transform duration-300 translate-x-full">
    <div class="bg-white rounded-lg shadow-md border border-gray-100 p-4 flex items-center max-w-md">
        <div id="toastIcon" class="flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center mr-3"></div>
        <div class="flex-1">
            <h4 id="toastTitle" class="font-medium text-gray-900 text-sm"></h4>
            <p id="toastMessage" class="text-gray-600 text-xs mt-1"></p>
        </div>
        <button onclick="hideToast()" class="ml-4 text-gray-400 hover:text-gray-600 transition-colors duration-200">
            <i class="fas fa-times"></i>
        </button>
    </div>
</div>

<!-- Confirmation Modal -->
<div id="confirmModal" class="fixed inset-0 bg-gray-900 bg-opacity-50 backdrop-blur-sm z-50 flex items-center justify-center hidden">
    <div class="bg-white rounded-lg shadow-md max-w-md w-full mx-4 overflow-hidden transform transition-all duration-300 scale-95 opacity-0 border border-gray-100">
        <div id="confirmHeader" class="px-6 py-4 border-b border-gray-200">
            <h3 id="confirmTitle" class="text-lg font-medium text-gray-800"></h3>
        </div>
        <div class="px-6 py-4">
            <p id="confirmMessage" class="text-gray-600 text-sm"></p>
        </div>
        <div class="px-6 py-3 bg-gray-50 flex justify-end space-x-3 border-t border-gray-200">
            <button id="cancelBtn" class="px-4 py-2 bg-white border border-gray-300 rounded-lg text-gray-700 text-sm hover:bg-gray-50 transition-colors duration-200">
                Cancel
            </button>
            <button id="confirmBtn" class="px-4 py-2 rounded-lg text-white text-sm transition-colors duration-200">
                Confirm
            </button>
        </div>
    </div>
</div>

<script>
// Toast notification system
function showToast(type, title, message, duration = 3000) {
    const toast = document.getElementById('toast');
    const toastIcon = document.getElementById('toastIcon');
    const toastTitle = document.getElementById('toastTitle');
    const toastMessage = document.getElementById('toastMessage');

    // Set content
    toastTitle.textContent = title;
    toastMessage.textContent = message;

    // Set icon and colors based on type
    if (type === 'success') {
        toastIcon.className = 'flex-shrink-0 w-8 h-8 bg-green-100 text-green-500 rounded-full flex items-center justify-center mr-3';
        toastIcon.innerHTML = '<i class="fas fa-check"></i>';
    } else if (type === 'error') {
        toastIcon.className = 'flex-shrink-0 w-8 h-8 bg-red-100 text-red-500 rounded-full flex items-center justify-center mr-3';
        toastIcon.innerHTML = '<i class="fas fa-exclamation"></i>';
    } else if (type === 'info') {
        toastIcon.className = 'flex-shrink-0 w-8 h-8 bg-indigo-100 text-indigo-500 rounded-full flex items-center justify-center mr-3';
        toastIcon.innerHTML = '<i class="fas fa-info"></i>';
    }

    // Show toast
    toast.classList.remove('translate-x-full');
    toast.classList.add('translate-x-0');

    // Hide after duration
    setTimeout(hideToast, duration);
}

function hideToast() {
    const toast = document.getElementById('toast');
    toast.classList.remove('translate-x-0');
    toast.classList.add('translate-x-full');
}

// Confirmation modal system
function showConfirmModal(title, message, confirmText, confirmClass, callback) {
    const modal = document.getElementById('confirmModal');
    const modalContent = modal.querySelector('div.bg-white');
    const confirmTitle = document.getElementById('confirmTitle');
    const confirmMessage = document.getElementById('confirmMessage');
    const confirmBtn = document.getElementById('confirmBtn');
    const cancelBtn = document.getElementById('cancelBtn');
    const confirmHeader = document.getElementById('confirmHeader');

    // Set content
    confirmTitle.textContent = title;
    confirmMessage.textContent = message;
    confirmBtn.textContent = confirmText;

    // Set button color
    confirmBtn.className = `px-4 py-2 ${confirmClass} rounded-lg text-white text-sm font-medium`;

    // Set header color to match button
    if (confirmClass.includes('bg-red')) {
        confirmHeader.className = 'px-6 py-4 border-b border-gray-100 bg-red-50';
    } else if (confirmClass.includes('bg-indigo')) {
        confirmHeader.className = 'px-6 py-4 border-b border-gray-100 bg-indigo-50';
    } else if (confirmClass.includes('bg-green')) {
        confirmHeader.className = 'px-6 py-4 border-b border-gray-100 bg-green-50';
    } else {
        confirmHeader.className = 'px-6 py-4 border-b border-gray-100';
    }

    // Show modal with animation
    modal.classList.remove('hidden');
    setTimeout(() => {
        modalContent.classList.remove('scale-95', 'opacity-0');
        modalContent.classList.add('scale-100', 'opacity-100');
    }, 10);

    // Set up event listeners
    const handleConfirm = () => {
        hideConfirmModal();
        callback();
        confirmBtn.removeEventListener('click', handleConfirm);
        cancelBtn.removeEventListener('click', handleCancel);
    };

    const handleCancel = () => {
        hideConfirmModal();
        confirmBtn.removeEventListener('click', handleConfirm);
        cancelBtn.removeEventListener('click', handleCancel);
    };

    confirmBtn.addEventListener('click', handleConfirm);
    cancelBtn.addEventListener('click', handleCancel);
}

function hideConfirmModal() {
    const modal = document.getElementById('confirmModal');
    const modalContent = modal.querySelector('div.bg-white');

    modalContent.classList.remove('scale-100', 'opacity-100');
    modalContent.classList.add('scale-95', 'opacity-0');

    setTimeout(() => {
        modal.classList.add('hidden');
    }, 300);
}

// Close modal when clicking outside
document.getElementById('confirmModal').addEventListener('click', function(e) {
    if (e.target === this) {
        hideConfirmModal();
    }
});

// Close modal with escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && !document.getElementById('confirmModal').classList.contains('hidden')) {
        hideConfirmModal();
    }
});

document.addEventListener('DOMContentLoaded', function() {
    const userId = '{{ user_id }}';

    // Kick user button
    document.getElementById('kickUserBtn').addEventListener('click', function() {
        showConfirmModal(
            'Kick User from Channel',
            'Are you sure you want to kick this user from the channel? They will not be able to rejoin unless unbanned.',
            'Kick User',
            'bg-red-500 hover:bg-red-600',
            function() {
                fetch(`{{ tenant_prefix }}dashboard/user/${userId}/kick`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showToast('success', 'Success', 'User kicked successfully');
                        setTimeout(() => location.reload(), 1500);
                    } else {
                        showToast('error', 'Error', data.message || 'Failed to kick user');
                    }
                })
                .catch(error => {
                    showToast('error', 'Error', error.message || 'An unexpected error occurred');
                });
            }
        );
    });

    // Unban user button
    document.getElementById('unbanUserBtn').addEventListener('click', function() {
        showConfirmModal(
            'Unban User',
            'Are you sure you want to unban this user? They will be able to rejoin the channel.',
            'Unban User',
            'bg-indigo-500 hover:bg-indigo-600',
            function() {
                fetch(`{{ tenant_prefix }}dashboard/user/${userId}/unban`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showToast('success', 'Success', 'User unbanned successfully');
                        setTimeout(() => location.reload(), 1500);
                    } else {
                        showToast('error', 'Error', data.message || 'Failed to unban user');
                    }
                })
                .catch(error => {
                    showToast('error', 'Error', error.message || 'An unexpected error occurred');
                });
            }
        );
    });

    // Send invite button
    document.getElementById('sendInviteBtn').addEventListener('click', function() {
        const messageInput = document.createElement('input');
        messageInput.type = 'text';
        messageInput.placeholder = 'Enter a custom message (optional)';
        messageInput.className = 'w-full px-3 py-2 border border-gray-300 rounded-lg mt-3 text-sm';

        const messageContainer = document.createElement('div');
        messageContainer.appendChild(document.createTextNode('You can include a custom message with the invite:'));
        messageContainer.appendChild(document.createElement('br'));
        messageContainer.appendChild(messageInput);

        const confirmMessage = document.getElementById('confirmMessage');
        confirmMessage.textContent = 'Are you sure you want to send an invite link to this user?';
        confirmMessage.appendChild(document.createElement('br'));
        confirmMessage.appendChild(messageContainer);

        showConfirmModal(
            'Send Invite Link',
            '',
            'Send Invite',
            'bg-green-500 hover:bg-green-600',
            function() {
                const message = messageInput.value;

                fetch(`{{ tenant_prefix }}dashboard/user/${userId}/send-invite`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        message: message || ''
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showToast('success', 'Success', 'Invite sent successfully');
                    } else {
                        showToast('error', 'Error', data.message || 'Failed to send invite');
                    }
                })
                .catch(error => {
                    showToast('error', 'Error', error.message || 'An unexpected error occurred');
                });
            }
        );
    });

    // Toggle subscription details
    const subscriptionDetailsContent = document.getElementById('subscriptionDetailsContent');
    const seeMoreButton = document.getElementById('seeMoreButton');
    const seeMoreContainer = document.getElementById('seeMoreContainer');
    const seeMoreText = document.getElementById('seeMoreText');
    const seeMoreIcon = document.getElementById('seeMoreIcon');
    const subscriptionDetailsFade = document.getElementById('subscriptionDetailsFade');

    let isCollapsed = true;
    const contentHeight = subscriptionDetailsContent.scrollHeight;
    const collapsedHeight = 300; // px

    // Function to toggle subscription details
    function toggleDetails() {
        if (isCollapsed) {
            // Expand
            subscriptionDetailsContent.style.maxHeight = contentHeight + 'px';
            seeMoreText.textContent = 'See Less';
            seeMoreIcon.classList.remove('fa-chevron-down');
            seeMoreIcon.classList.add('fa-chevron-up');
            subscriptionDetailsFade.style.display = 'none';
            seeMoreButton.classList.add('bg-indigo-50');
            seeMoreButton.classList.remove('border-indigo-200');
            seeMoreButton.classList.add('border-indigo-300');
        } else {
            // Collapse
            subscriptionDetailsContent.style.maxHeight = collapsedHeight + 'px';
            seeMoreText.textContent = 'See More';
            seeMoreIcon.classList.remove('fa-chevron-up');
            seeMoreIcon.classList.add('fa-chevron-down');
            subscriptionDetailsFade.style.display = 'block';
            seeMoreButton.classList.remove('bg-indigo-50');
            seeMoreButton.classList.add('bg-white');
            seeMoreButton.classList.remove('border-indigo-300');
            seeMoreButton.classList.add('border-indigo-200');
        }
        isCollapsed = !isCollapsed;
    }

    // Initial setup
    // Always set initial collapsed state
    subscriptionDetailsContent.style.maxHeight = collapsedHeight + 'px';

    // Only hide the fade effect if content is very short
    if (contentHeight <= collapsedHeight) {
        // Content is short, no need for fade effect
        subscriptionDetailsFade.style.display = 'none';
    }

    // Add event listener
    seeMoreButton.addEventListener('click', toggleDetails);

    // Add animation to cards
    const cards = document.querySelectorAll('.rounded-xl');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        setTimeout(() => {
            card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, 100 + (index * 100));
    });
});
</script>
{% endblock %}