{% extends 'base.html' %}

{% block title %}{{ bot.name }} Users - Admin Panel{% endblock %}

{% block header_title %}{{ bot.name }} Users{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/telegram_bot_users_mobile.css') }}">
{% endblock %}

{% block content %}
<div class="bg-white rounded-xl shadow-md mb-8 border border-gray-100 overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center bg-gradient-to-r from-indigo-50 to-white">
        <div class="flex items-center">
            <i class="fab fa-telegram text-indigo-500 mr-3 text-xl"></i>
            <div>
                <h2 class="text-lg font-semibold text-gray-800">{{ bot.name }} Users</h2>
                <p class="text-sm text-gray-500">@{{ bot.username }}</p>
            </div>
        </div>
        <a href="{{ tenant_url_for('dashboard.telegram_bots_route') }}"
           class="text-sm px-4 py-2 bg-gray-100 text-gray-700 rounded-lg  hover:bg-gray-200 transition-colors duration-200 flex items-center shadow-sm">
            <i class="fas fa-arrow-left mr-2"></i>Back to Bots
        </a>
    </div>

    {% if bot_users %}
    <div class="px-6 py-3 bg-gray-50 border-b border-gray-200 flex justify-between items-center">
        <div class="text-sm text-gray-600">
            <span class="font-medium">{{ bot_users|length }}</span> users connected to this bot
        </div>
        <div class="flex space-x-2">
            <div class="relative max-w-xs w-full">
                <label for="searchInput" class="sr-only">Search</label>
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                    <input id="searchInput" type="text" class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors text-sm" placeholder="Search users...">
                </div>
            </div>
        </div>
    </div>

    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead>
                <tr class="bg-gray-50">
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">User ID</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Name</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Access Code</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Status</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-b">Actions</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for user in bot_users %}
                <tr class="hover:bg-gray-50 transition-colors duration-150">
                    <td class="px-6 py-4 whitespace-nowrap" data-label="USER ID">
                        <a href="{{ tenant_url_for('dashboard.user_detail_route', user_id=user.user_id) }}"
                           class="text-indigo-600 hover:text-indigo-800 hover:underline font-medium">
                           {{ user.user_id }}
                        </a>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap" data-label="NAME">
                        <div class="font-medium text-gray-800">{{ user.name or 'N/A' }}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap" data-label="ACCESS CODE">
                        <span class="font-mono text-sm">{{ user.access_code or 'N/A' }}</span>
                    </td>

                    <td class="px-6 py-4 whitespace-nowrap" data-label="STATUS">
                        {% if user.status == 'active' %}
                        <span class="px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 border border-green-200">
                            ACTIVE
                        </span>
                        {% elif user.status == 'inactive' %}
                        <span class="px-3 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 border border-gray-200">
                            INACTIVE
                        </span>
                        {% elif user.status == 'blocked' %}
                        <span class="px-3 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 border border-red-200">
                            BLOCKED
                        </span>
                        {% else %}
                        <span class="px-3 py-1 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800 border border-indigo-200">
                            {{ user.status|upper }}
                        </span>
                        {% endif %}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium" data-label="ACTIONS">
                        <a href="{{ tenant_url_for('dashboard.user_detail_route', user_id=user.user_id) }}" class="text-indigo-600 hover:text-indigo-900 mr-3">
                            <i class="fas fa-eye"></i>
                        </a>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% else %}
    <div class="px-6 py-12 text-center">
        <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-indigo-100 text-indigo-400 mb-4">
            <i class="fas fa-users-slash text-2xl"></i>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">No users found</h3>
        <p class="text-gray-500 max-w-md mx-auto">This bot doesn't have any connected users yet.</p>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add mobile-specific functionality
        if (window.innerWidth <= 768) {
            // Make rows clickable on mobile
            const tableRows = document.querySelectorAll('tbody tr');
            tableRows.forEach(row => {
                row.addEventListener('click', function(e) {
                    // Don't trigger if clicking on a button or link
                    if (!e.target.closest('a') && !e.target.closest('button')) {
                        const userIdLink = this.querySelector('td:first-child a');
                        if (userIdLink) {
                            window.location.href = userIdLink.getAttribute('href');
                        }
                    }
                });
                // Add cursor pointer to indicate clickable
                row.style.cursor = 'pointer';
            });
        }

        // Search functionality
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();
                const rows = document.querySelectorAll('tbody tr');

                rows.forEach(row => {
                    const nameCell = row.querySelector('td:nth-child(2)');
                    const userIdCell = row.querySelector('td:nth-child(1)');
                    const accessCodeCell = row.querySelector('td:nth-child(3)');

                    if (nameCell && userIdCell && accessCodeCell) {
                        const nameText = nameCell.textContent.toLowerCase();
                        const userIdText = userIdCell.textContent.toLowerCase();
                        const accessCodeText = accessCodeCell.textContent.toLowerCase();

                        if (nameText.includes(searchTerm) || userIdText.includes(searchTerm) || accessCodeText.includes(searchTerm)) {
                            row.style.display = '';
                        } else {
                            row.style.display = 'none';
                        }
                    }
                });
            });
        }

        // Add hover effect to table rows
        const tableRows = document.querySelectorAll('tbody tr');
        tableRows.forEach(row => {
            row.addEventListener('mouseenter', function() {
                this.classList.add('bg-gray-50');
            });
            row.addEventListener('mouseleave', function() {
                this.classList.remove('bg-gray-50');
            });
        });
    });
</script>
{% endblock %}