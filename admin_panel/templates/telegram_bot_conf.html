{% extends 'base.html' %}

{% block title %}Bot Configuration - Admin Panel{% endblock %}

{% block header_title %}Bot Configuration{% endblock %}

{% block extra_css %}
<style>
    .upload-area {
        border: 2px dashed #d1d5db;
        border-radius: 8px;
        padding: 2rem;
        text-align: center;
        transition: all 0.3s ease;
        background-color: #f9fafb;
    }

    .upload-area.dragover {
        border-color: #3b82f6;
        background-color: #eff6ff;
    }

    .upload-area:hover {
        border-color: #6b7280;
    }

    .file-info {
        background-color: #f0f9ff;
        border: 1px solid #0ea5e9;
        border-radius: 6px;
        padding: 1rem;
        margin-top: 1rem;
    }

    .success-indicator {
        background-color: #f0fdf4;
        border: 1px solid #22c55e;
        border-radius: 6px;
        padding: 1rem;
        margin-top: 1rem;
        display: none;
    }

    .error-indicator {
        background-color: #fef2f2;
        border: 1px solid #ef4444;
        border-radius: 6px;
        padding: 1rem;
        margin-top: 1rem;
        display: none;
    }
</style>
{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto">
    <!-- Header -->
    <div class="bg-white rounded-xl shadow-md mb-8 border border-gray-100 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center bg-gradient-to-r from-blue-50 to-white">
            <div class="flex items-center">
                <i class="fas fa-cog text-blue-500 mr-3 text-xl"></i>
                <div>
                    <h2 class="text-lg font-semibold text-gray-800">Bot Configuration</h2>
                    <p class="text-sm text-gray-500">Upload PDF and configure referral settings</p>
                </div>
            </div>
            <a href="{{ tenant_url_for('dashboard.telegram_bots_route') }}"
               class="text-sm px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors duration-200 flex items-center shadow-sm">
                <i class="fas fa-arrow-left mr-2"></i>Back to Bots
            </a>
        </div>
    </div>

    <!-- Upload Form -->
    <div class="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                <i class="fas fa-upload text-green-500 mr-3"></i>
                Upload Configuration
            </h3>
        </div>

        <div class="p-6">
            <form id="uploadForm" action="{{ tenant_url_for('dashboard.upload_bot_config_route', bot_name=bot_name) }}" method="POST" enctype="multipart/form-data">
                <!-- PDF Upload Section -->
                <div class="mb-8">
                    <label class="block text-sm font-medium text-gray-700 mb-3">
                        <i class="fas fa-file-pdf text-red-500 mr-2"></i>
                        Upload PDF File
                    </label>
                    <div id="uploadArea" class="upload-area">
                        <div id="uploadPrompt">
                            <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                            <p class="text-lg font-medium text-gray-600 mb-2">Drop your PDF file here</p>
                            <p class="text-sm text-gray-500 mb-4">or click to browse</p>
                            <input type="file" id="pdfFile" name="pdf_file" accept=".pdf" class="hidden" required>
                            <button type="button" onclick="document.getElementById('pdfFile').click()"
                                    class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors duration-200">
                                Choose File
                            </button>
                            <p class="text-xs text-gray-400 mt-2">Maximum file size: 10MB</p>
                        </div>
                        <div id="fileInfo" class="file-info hidden">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <i class="fas fa-file-pdf text-red-500 mr-3 text-xl"></i>
                                    <div>
                                        <p id="fileName" class="font-medium text-gray-800"></p>
                                        <p id="fileSize" class="text-sm text-gray-500"></p>
                                    </div>
                                </div>
                                <button type="button" onclick="clearFile()" class="text-red-500 hover:text-red-700">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Referral Link Section -->
                <div class="mb-8">
                    <label for="referralLink" class="block text-sm font-medium text-gray-700 mb-3">
                        <i class="fas fa-link text-blue-500 mr-2"></i>
                        Referral Link
                    </label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-external-link-alt text-gray-400"></i>
                        </div>
                        <input type="url" id="referralLink" name="referral_link" required
                               class="pl-10 block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring focus:ring-blue-500 focus:ring-opacity-50 py-3"
                               placeholder="https://example.com/referral">
                    </div>
                    <p class="mt-2 text-sm text-gray-500">
                        <i class="fas fa-info-circle mr-1 text-blue-400"></i>
                        Enter a valid URL for the referral link
                    </p>
                </div>

                <!-- Submit Button -->
                <div class="flex justify-end">
                    <button type="submit" id="submitBtn"
                            class="px-6 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors duration-200 flex items-center shadow-sm disabled:opacity-50 disabled:cursor-not-allowed">
                        <i class="fas fa-save mr-2"></i>
                        Save Configuration
                    </button>
                </div>
            </form>

            <!-- Success Indicator -->
            <div id="successIndicator" class="success-indicator">
                <div class="flex items-center">
                    <i class="fas fa-check-circle text-green-500 mr-3 text-xl"></i>
                    <div>
                        <p class="font-medium text-green-800">Configuration saved successfully!</p>
                        <p class="text-sm text-green-600">PDF uploaded and referral link configured.</p>
                    </div>
                </div>
            </div>

            <!-- Error Indicator -->
            <div id="errorIndicator" class="error-indicator">
                <div class="flex items-center">
                    <i class="fas fa-exclamation-circle text-red-500 mr-3 text-xl"></i>
                    <div>
                        <p class="font-medium text-red-800">Error occurred!</p>
                        <p id="errorMessage" class="text-sm text-red-600"></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Existing Configurations -->
    <div class="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden mt-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-800 flex items-center">
                <i class="fas fa-list text-purple-500 mr-3"></i>
                Existing Configurations
            </h3>
        </div>

        <div class="p-6">
            <div id="configList">
                <!-- Configurations will be loaded here -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const uploadArea = document.getElementById('uploadArea');
    const pdfFile = document.getElementById('pdfFile');
    const uploadPrompt = document.getElementById('uploadPrompt');
    const fileInfo = document.getElementById('fileInfo');
    const fileName = document.getElementById('fileName');
    const fileSize = document.getElementById('fileSize');
    const uploadForm = document.getElementById('uploadForm');
    const submitBtn = document.getElementById('submitBtn');
    const successIndicator = document.getElementById('successIndicator');
    const errorIndicator = document.getElementById('errorIndicator');
    const errorMessage = document.getElementById('errorMessage');

    // File size limit (10MB)
    const MAX_FILE_SIZE = 10 * 1024 * 1024;

    // Drag and drop functionality
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('dragover');

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFileSelection(files[0]);
        }
    });

    // File input change
    pdfFile.addEventListener('change', function(e) {
        if (e.target.files.length > 0) {
            handleFileSelection(e.target.files[0]);
        }
    });

    function handleFileSelection(file) {
        // Validate file type
        if (file.type !== 'application/pdf') {
            showError('Please select a PDF file.');
            return;
        }

        // Validate file size
        if (file.size > MAX_FILE_SIZE) {
            showError('File size exceeds 10MB limit.');
            return;
        }

        // Display file info
        fileName.textContent = file.name;
        fileSize.textContent = formatFileSize(file.size);

        uploadPrompt.classList.add('hidden');
        fileInfo.classList.remove('hidden');

        hideMessages();
    }

    function clearFile() {
        pdfFile.value = '';
        uploadPrompt.classList.remove('hidden');
        fileInfo.classList.add('hidden');
        hideMessages();
    }

    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    function showSuccess() {
        successIndicator.style.display = 'block';
        errorIndicator.style.display = 'none';
    }

    function showError(message) {
        errorMessage.textContent = message;
        errorIndicator.style.display = 'block';
        successIndicator.style.display = 'none';
    }

    function hideMessages() {
        successIndicator.style.display = 'none';
        errorIndicator.style.display = 'none';
    }

    // Form submission
    uploadForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(uploadForm);

        // Disable submit button
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Uploading...';

        fetch(uploadForm.action, {
            method: 'POST',
            body: formData,
            credentials: 'include'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccess();
                loadExistingConfigurations();
                // Reset form
                uploadForm.reset();
                clearFile();
            } else {
                showError(data.message || 'Upload failed');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('An error occurred during upload');
        })
        .finally(() => {
            // Re-enable submit button
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-save mr-2"></i>Save Configuration';
        });
    });

    // Load existing configurations
    function loadExistingConfigurations() {
        fetch(`{{ tenant_url_for('dashboard.get_bot_configs_route', bot_name=bot_name) }}`, {
            credentials: 'include'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayConfigurations(data.configurations);
            }
        })
        .catch(error => {
            console.error('Error loading configurations:', error);
        });
    }

    function displayConfigurations(configurations) {
        const configList = document.getElementById('configList');

        if (configurations.length === 0) {
            configList.innerHTML = '<p class="text-gray-500 text-center py-4">No configurations found</p>';
            return;
        }

        let html = '';
        configurations.forEach((config, index) => {
            html += `
                <div class="border border-gray-200 rounded-lg p-4 mb-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="fas fa-file-pdf text-red-500 mr-3 text-xl"></i>
                            <div>
                                <p class="font-medium text-gray-800">Configuration ${index + 1}</p>
                                <p class="text-sm text-gray-500">
                                    <i class="fas fa-link mr-1"></i>
                                    <a href="${config.referralLink}" target="_blank" class="text-blue-500 hover:text-blue-700">
                                        ${config.referralLink}
                                    </a>
                                </p>
                                <p class="text-xs text-gray-400">
                                    Uploaded: ${new Date(config.uploadedAt).toLocaleString()}
                                </p>
                            </div>
                        </div>
                        <div class="flex space-x-2">
                            <button onclick="previewPDF('${config._id}')"
                                    class="px-3 py-1 bg-blue-500 text-white rounded text-sm hover:bg-blue-600">
                                <i class="fas fa-eye mr-1"></i>Preview
                            </button>
                            <button onclick="downloadPDF('${config._id}')"
                                    class="px-3 py-1 bg-green-500 text-white rounded text-sm hover:bg-green-600">
                                <i class="fas fa-download mr-1"></i>Download
                            </button>
                        </div>
                    </div>
                </div>
            `;
        });

        configList.innerHTML = html;
    }

    // PDF preview and download functions
    window.previewPDF = function(configId) {
        const url = `{{ tenant_url_for('dashboard.preview_pdf_route', bot_name=bot_name, config_id='') }}${configId}`;
        window.open(url, '_blank');
    };

    window.downloadPDF = function(configId) {
        const url = `{{ tenant_url_for('dashboard.download_pdf_route', bot_name=bot_name, config_id='') }}${configId}`;
        window.location.href = url;
    };

    // Make clearFile function global
    window.clearFile = clearFile;

    // Load existing configurations on page load
    loadExistingConfigurations();
});
</script>
{% endblock %}