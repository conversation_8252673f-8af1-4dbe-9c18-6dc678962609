<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Admin-Panel{% endblock %}</title>
    <!-- Favicon -->
    <link rel="apple-touch-icon" sizes="180x180" href="{{ tenant_url_for('static', filename='favicon/apple-touch-icon.png') }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ tenant_url_for('static', filename='favicon/favicon-32x32.png') }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ tenant_url_for('static', filename='favicon/favicon-16x16.png') }}">
    <link rel="manifest" href="{{ tenant_url_for('static', filename='site.webmanifest') }}">
    <link rel="shortcut icon" href="{{ tenant_url_for('static', filename='favicon/favicon.ico') }}">
    <meta name="theme-color" content="#4f46e5">
    <!-- Inline script to prevent sidebar flicker -->
    <script>
        // Check localStorage for sidebar state immediately
        (function() {
            // For mobile view, always set sidebar to expanded (not collapsed)
            if (window.innerWidth <= 768) {
                document.documentElement.style.setProperty('--sidebar-width', '16rem');
                document.documentElement.style.setProperty('--main-margin', '16rem');
                document.documentElement.classList.remove('sidebar-collapsed');
                // We don't update localStorage here to preserve user's desktop preference
            } else {
                // For desktop, use the user's saved preference
                var sidebarState = localStorage.getItem('sidebarCollapsed');
                if (sidebarState === 'true') {
                    // Apply collapsed styles immediately via inline style
                    document.documentElement.style.setProperty('--sidebar-width', '3.5rem');
                    document.documentElement.style.setProperty('--main-margin', '3.5rem');
                    document.documentElement.classList.add('sidebar-collapsed');
                } else {
                    document.documentElement.style.setProperty('--sidebar-width', '16rem');
                    document.documentElement.style.setProperty('--main-margin', '16rem');
                }
            }
        })();
    </script>
    <link rel="stylesheet" href="{{ tenant_url_for('static', filename='css/base.css') }}">
    <style>
        /* Additional custom styles can be added here */
    </style>
    <!-- Tailwind CSS from CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ tenant_url_for('static', filename='css/tailwind.css') }}">
    {% block extra_css %}{% endblock %}
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'sidebar-dark': '#1e293b',
                        'main-bg': '#f8fafc',
                        'primary': '#4f46e5',
                        'primary-hover': '#4338ca',
                        'secondary': '#4f46e5',
                        'secondary-hover': '#4338ca',
                        'success': '#10b981',
                        'success-hover': '#059669',
                        'danger': '#ef4444',
                        'danger-hover': '#dc2626',
                        'warning': '#f59e0b',
                        'warning-hover': '#d97706',
                        'indigo-50': '#eef2ff',
                        'indigo-100': '#e0e7ff',
                        'indigo-200': '#c7d2fe',
                        'indigo-300': '#a5b4fc',
                        'indigo-400': '#818cf8',
                        'indigo-500': '#6366f1',
                        'indigo-600': '#4f46e5',
                        'indigo-700': '#4338ca',
                        'indigo-800': '#3730a3',
                        'indigo-900': '#312e81',
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif'],
                    },
                }
            }
        }
    </script>
</head>
<body class="bg-main-bg min-h-screen font-sans overflow-x-hidden max-w-[100vw]">
    <!-- Sidebar -->
    <aside id="sidebar" class="w-64 bg-sidebar-dark text-white fixed h-full transition-all duration-300 z-50 flex flex-col">
        <div class="p-5 flex items-center justify-between border-b border-gray-700 border-opacity-50 flex-shrink-0">
            <div class="flex items-center space-x-3">
                <div class="text-indigo-400 text-2xl">
                    <i class="fas fa-chart-pie"></i>
                </div>
                <h1 class="text-sm font-bold sidebar-text tracking-wide">Membership Manager</h1>
            </div>
            <!-- Mobile Close Button -->
            <button id="mobileSidebarClose" class="text-gray-400 hover:text-white md:hidden p-2 rounded-full hover:bg-gray-700 transition-colors">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <nav class="mt-6 flex-1 overflow-y-auto sidebar-scrollbar">
            <div class="px-4 py-2">
                <h5 class="text-xs font-semibold text-gray-400 uppercase tracking-wider sidebar-text">Main</h5>
            </div>
            <ul class="mt-2 space-y-1 px-2">
                <li>
                    <a href="{{ tenant_url_for('dashboard.home_route') }}" class="sidebar-link rounded-md {% if request.endpoint == 'dashboard.home_route' %}active bg-indigo-500 bg-opacity-20 border-l-indigo-500 text-white{% endif %}" data-tooltip="Dashboard" style="padding-left: 1rem;">
                        <i class="fas fa-tachometer-alt" style="width: 1.25rem; text-align: center; margin-right: 0.75rem;"></i>
                        <span class="sidebar-text">Dashboard</span>
                    </a>
                </li>
                <li>
                    <a href="{{ tenant_url_for('dashboard.access_codes_route') }}" class="sidebar-link rounded-md {% if request.endpoint == 'dashboard.access_codes_route' %}active bg-indigo-500 bg-opacity-20 border-l-indigo-500 text-white{% endif %}" data-tooltip="Access Codes" style="padding-left: 1rem;">
                        <i class="fas fa-key" style="width: 1.25rem; text-align: center; margin-right: 0.75rem;"></i>
                        <span class="sidebar-text">Access Codes</span>
                    </a>
                </li>
                <li>
                    <a href="{{ tenant_url_for('dashboard.users_route') }}" class="sidebar-link rounded-md {% if request.endpoint == 'dashboard.users_route' %}active bg-indigo-500 bg-opacity-20 border-l-indigo-500 text-white{% endif %}" data-tooltip="Users" style="padding-left: 1rem;">
                        <i class="fas fa-users" style="width: 1.25rem; text-align: center; margin-right: 0.75rem;"></i>
                        <span class="sidebar-text">Users</span>
                    </a>
                </li>
                <li>
                    <a href="{{ tenant_url_for('dashboard.telegram_bots_route') }}" class="sidebar-link rounded-md {% if request.endpoint == 'dashboard.telegram_bots_route' %}active bg-indigo-500 bg-opacity-20 border-l-indigo-500 text-white{% endif %}" data-tooltip="Telegram Bots" style="padding-left: 1rem;">
                        <i class="fab fa-telegram" style="width: 1.25rem; text-align: center; margin-right: 0.75rem;"></i>
                        <span class="sidebar-text">Telegram Bots</span>
                    </a>
                </li>


                <!-- Requests Dropdown Menu -->
                <li class="relative">
                    <button id="requestsDropdownButton" class="sidebar-link rounded-md w-full text-left flex items-center justify-between" data-tooltip="Requests" style="padding-left: 1rem;">
                        <div class="flex items-center flex-1">
                            <i class="fas fa-inbox" style="width: 1.25rem; text-align: center; margin-right: 0.75rem;"></i>
                            <span class="sidebar-text">Requests</span>
                        </div>
                        <i class="fas fa-chevron-down sidebar-text transition-transform duration-200" id="requestsDropdownIcon" style="margin-right: 0.5rem; font-size: 0.75rem;"></i>
                    </button>


                    <div id="requestsDropdownMenu" class="sidebar-submenu overflow-hidden transition-all duration-300 max-h-0">
                        <ul class="pl-4 mt-1 space-y-1 sidebar-expanded-menu">
                            <li>
                                <a href="{{ tenant_url_for('dashboard.verification_requests_route') }}" class="sidebar-sublink rounded-md {% if request.endpoint == 'dashboard.verification_requests_route' %}active bg-indigo-500 bg-opacity-20 border-l-indigo-500 text-white{% endif %}" data-tooltip="Verification Requests">
                                    <i class="fas fa-user-check text-xs w-4 text-center"></i>
                                    <span class="sidebar-text text-sm ml-2">Verification Requests</span>
                                </a>
                            </li>
                            <li>
                                <a href="{{ tenant_url_for('dashboard.support_requests_route') }}" class="sidebar-sublink rounded-md {% if request.endpoint == 'dashboard.support_requests_route' %}active bg-indigo-500 bg-opacity-20 border-l-indigo-500 text-white{% endif %}" data-tooltip="Support Requests">
                                    <i class="fas fa-headset text-xs w-4 text-center"></i>
                                    <span class="sidebar-text text-sm ml-2">Support Requests</span>
                                </a>
                            </li>
                        </ul>
                        <!-- Popup menu for collapsed sidebar -->
                        <ul class="sidebar-popup-menu hidden">
                            <li class="px-4 py-2 text-xs font-semibold text-gray-300 border-b border-gray-700 mb-1">
                                <i class="fas fa-inbox mr-1"></i> Requests Menu
                            </li>
                            <li>
                                <a href="{{ tenant_url_for('dashboard.verification_requests_route') }}" class="block px-4 py-2 text-sm text-gray-200 hover:bg-opacity-10 hover:bg-white hover:text-white transition-colors duration-150 {% if request.endpoint == 'dashboard.verification_requests_route' %}bg-indigo-600 bg-opacity-80 text-white{% endif %}">
                                    <i class="fas fa-user-check mr-2 text-gray-400"></i>
                                    Verification Requests
                                </a>
                            </li>
                            <li>
                                <a href="{{ tenant_url_for('dashboard.support_requests_route') }}" class="block px-4 py-2 text-sm text-gray-200 hover:bg-opacity-10 hover:bg-white hover:text-white transition-colors duration-150 {% if request.endpoint == 'dashboard.support_requests_route' %}bg-indigo-600 bg-opacity-80 text-white{% endif %}">
                                    <i class="fas fa-headset mr-2 text-gray-400"></i>
                                    Support Requests
                                </a>
                            </li>
                        </ul>
                    </div>
                </li>

                <li>
                    <a href="{{ tenant_url_for('dashboard.broker_setup_route') }}" class="sidebar-link rounded-md {% if request.endpoint == 'dashboard.broker_setup_route' %}active bg-indigo-500 bg-opacity-20 border-l-indigo-500 text-white{% endif %}" data-tooltip="Broker Setup" style="padding-left: 1rem;">
                        <i class="fas fa-chart-line" style="width: 1.25rem; text-align: center; margin-right: 0.75rem;"></i>
                        <span class="sidebar-text">Broker Setup</span>
                    </a>
                </li>
            </ul>

            <div class="px-4 py-2 mt-8">
                <h5 class="text-xs font-semibold text-gray-400 uppercase tracking-wider sidebar-text">Account</h5>
            </div>
            <ul class="mt-2 space-y-1 px-2">
                <li>
                    <a href="{{ tenant_url_for('dashboard.admin_profile_route') }}" class="sidebar-link rounded-md {% if request.endpoint == 'dashboard.admin_profile_route' %}active bg-indigo-500 bg-opacity-20 border-l-indigo-500 text-white{% endif %}" data-tooltip="Profile" style="padding-left: 1rem;">
                        <i class="fas fa-user-cog" style="width: 1.25rem; text-align: center; margin-right: 0.75rem;"></i>
                        <span class="sidebar-text">Profile</span>
                    </a>
                </li>
                <li>
                    <a href="{{ tenant_url_for('auth.logout') }}" class="sidebar-link rounded-md text-red-400 hover:text-red-300" data-tooltip="Logout" style="padding-left: 1rem;">
                        <i class="text-red-400 fas fa-sign-out-alt" style="width: 1.25rem; text-align: center; margin-right: 0.75rem;"></i>
                        <span class="text-red-400 sidebar-text">Logout</span>
                    </a>
                </li>
            </ul>
        </nav>
    </aside>

    <!-- Mobile overlay for sidebar -->
    <div id="mobileOverlay" class="mobile-overlay"></div>

    <!-- Main Content -->
    <div id="mainContent" class="ml-64 flex-1 transition-all duration-300 overflow-x-hidden max-w-full md:w-auto w-full">
        <!-- Header -->
        <header class="bg-white shadow-sm sticky top-0 z-40">
            <div class="flex flex-col md:flex-row justify-between items-start md:items-center px-6 py-4">
                <div class="flex flex-wrap items-center w-full md:w-auto mb-3 md:mb-0">
                    <!-- Mobile menu toggle button -->
                    <button id="mobileMenuToggle" class="mr-4 text-gray-600 hover:text-indigo-600 md:hidden">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                    <h1 class="text-xl md:text-2xl font-bold text-gray-800 mr-2">User onboarding Dashboard</h1>
                    <span class="px-3 py-1 bg-indigo-100 text-indigo-800 text-xs font-medium rounded-full">{{tenant}}</span>
                </div>
                <div id="headerIcons" class="flex flex-wrap items-center space-x-3 md:space-x-6 w-full md:w-auto">
                    <div class="relative w-full md:w-auto">
                        <input
                            type="text"
                            id="globalSearch"
                            placeholder="Search codes, users, contacts..."
                            class="pl-10 pr-4 py-2 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-indigo-500 w-full md:w-64 transition-all duration-300 hover:shadow-sm focus:shadow-md md:focus:w-72"
                            autocomplete="off"
                        >
                        <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>

                        <!-- Search Results Dropdown -->
                        <div id="searchResults" class="hidden absolute left-0 right-0 mt-1 bg-white rounded-lg shadow-lg overflow-hidden z-50 max-h-96 overflow-y-auto border border-gray-200">
                            <div class="divide-y divide-gray-100">
                                <!-- Results will be populated here -->
                            </div>
                        </div>
                    </div>
                    <div class="relative">
                        <button class="text-gray-600 hover:text-indigo-600 transition-colors duration-200 relative">
                            <i class="fas fa-bell text-xl"></i>
                            <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">2</span>
                        </button>
                    </div>
                    <!-- User dropdown -->
                    <div class="relative" id="userDropdownContainer">
                        <div class="w-10 h-10 rounded-full bg-indigo-100 flex items-center justify-center text-indigo-700 cursor-pointer hover:bg-indigo-200 transition-colors duration-200" onclick="toggleProfileMenu()">
                            <i class="fas fa-user"></i>
                        </div>
                    </div>

                </div>
            </div>
        </header>

        <!-- Dropdown menu (outside the header completely) -->
        <div id="profileMenu" style="display: none; position: fixed; right: 16px; top: 70px; width: 224px; background: white; border-radius: 0.5rem; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); z-index: 9999; border: 1px solid #e5e7eb; padding: 0.5rem 0;">
                        <div style="padding: 0.75rem 1rem; border-bottom: 1px solid #f3f4f6;">
                            <p style="font-size: 0.875rem; font-weight: 500; color: #111827;">Admin User</p>
                            <p style="font-size: 0.75rem; color: #6b7280;"><EMAIL></p>
                        </div>
                        <a href="{{ tenant_url_for('dashboard.admin_profile_route') }}" style="display: block; padding: 0.75rem 1rem; font-size: 0.875rem; color: #374151; text-decoration: none;" onmouseover="this.style.backgroundColor='#f9fafb'" onmouseout="this.style.backgroundColor='transparent'">
                            <i class="fas fa-user-cog mr-2" style="color: #6366f1;"></i>Profile Settings
                        </a>
                        <a href="#" style="display: block; padding: 0.75rem 1rem; font-size: 0.875rem; color: #374151; text-decoration: none;" onmouseover="this.style.backgroundColor='#f9fafb'" onmouseout="this.style.backgroundColor='transparent'">
                            <i class="fas fa-cog mr-2" style="color: #6366f1;"></i>System Settings
                        </a>
                        <div style="border-top: 1px solid #f3f4f6; margin: 0.25rem 0;"></div>
                        <a href="{{ tenant_url_for('auth.logout') }}" style="display: block; padding: 0.75rem 1rem; font-size: 0.875rem; color: #dc2626; text-decoration: none;" onmouseover="this.style.backgroundColor='#fef2f2'" onmouseout="this.style.backgroundColor='transparent'">
                            <i class="fas fa-sign-out-alt mr-2"></i>Logout
                        </a>
                    </div>


        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="px-6 mt-4">
                    {% for category, message in messages %}
                        <div class="p-4 mb-4 rounded-lg shadow-sm border-l-4 {% if category == 'error' %}border-red-500 bg-red-50 text-red-700{% elif category == 'success' %}border-green-500 bg-green-50 text-green-700{% else %}border-indigo-500 bg-indigo-50 text-indigo-700{% endif %} flash-message flex items-center justify-between">
                            <div class="flex items-center">
                                <i class="{% if category == 'error' %}fas fa-exclamation-circle text-red-500{% elif category == 'success' %}fas fa-check-circle text-green-500{% else %}fas fa-info-circle text-indigo-500{% endif %} mr-3 text-lg"></i>
                                <span>{{ message }}</span>
                            </div>
                            <button class="text-gray-400 hover:text-gray-600" onclick="this.parentElement.style.display='none'">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    {% endfor %}
                </div>
            {% endif %}
        {% endwith %}

        <!-- Page Content -->
        <div class="p-6">
            {% block content %}{% endblock %}
        </div>

        <!-- Footer -->
        <footer class="bg-white border-t border-gray-200 mt-auto">
            <div class="max-w-7xl mx-auto py-4 px-6">
                <div class="flex justify-between items-center">
                    <p class="text-sm text-gray-500">&copy; {{ now.year if now else '2023' }} Admin Panel. All rights reserved.</p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-gray-600 transition-colors duration-200">
                            <i class="fas fa-question-circle"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-gray-600 transition-colors duration-200">
                            <i class="fas fa-cog"></i>
                        </a>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <!-- JavaScript -->
    <script src="{{ tenant_url_for('static', filename='js/main.js') }}"></script>
    <script>
        // Mobile sidebar toggle
        document.addEventListener('DOMContentLoaded', function() {
            const mobileMenuToggle = document.getElementById('mobileMenuToggle');
            const mobileSidebarClose = document.getElementById('mobileSidebarClose');
            const sidebar = document.getElementById('sidebar');
            const mobileOverlay = document.getElementById('mobileOverlay');

            // Function to close the sidebar
            function closeMobileSidebar() {
                sidebar.classList.remove('mobile-open');
                mobileOverlay.classList.remove('active');
                document.body.classList.remove('overflow-hidden');

                // Add animation class
                sidebar.classList.add('sidebar-closing');

                // Remove animation class after transition completes
                setTimeout(() => {
                    sidebar.classList.remove('sidebar-closing');
                }, 300);
            }

            // Function to open the sidebar
            function openMobileSidebar() {
                sidebar.classList.add('mobile-open');
                mobileOverlay.classList.add('active');
                document.body.classList.add('overflow-hidden');

                // Add animation class
                sidebar.classList.add('sidebar-opening');

                // Remove animation class after transition completes
                setTimeout(() => {
                    sidebar.classList.remove('sidebar-opening');
                }, 300);
            }

            if (mobileMenuToggle && sidebar && mobileOverlay) {
                // Open sidebar when hamburger menu is clicked
                mobileMenuToggle.addEventListener('click', function(e) {
                    e.stopPropagation(); // Prevent event bubbling
                    if (sidebar.classList.contains('mobile-open')) {
                        closeMobileSidebar();
                    } else {
                        openMobileSidebar();
                    }
                });

                // Close sidebar when overlay is clicked
                mobileOverlay.addEventListener('click', function(e) {
                    e.stopPropagation(); // Prevent event bubbling
                    closeMobileSidebar();
                });
            }

            // Close sidebar when close button is clicked
            if (mobileSidebarClose) {
                mobileSidebarClose.addEventListener('click', function(e) {
                    e.stopPropagation(); // Prevent event bubbling
                    closeMobileSidebar();
                });
            }

            // Close sidebar when escape key is pressed
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && sidebar.classList.contains('mobile-open')) {
                    closeMobileSidebar();
                }
            });

            // Handle window resize events
            window.addEventListener('resize', function() {
                // If transitioning from mobile to desktop view, close the mobile sidebar
                if (window.innerWidth > 768 && sidebar.classList.contains('mobile-open')) {
                    closeMobileSidebar();
                }
            });

            // Check if we're on mobile on page load and ensure proper state
            if (window.innerWidth <= 768) {
                // Make sure sidebar is closed by default on mobile
                sidebar.classList.remove('mobile-open');
                mobileOverlay.classList.remove('active');
            }
        });

        function toggleProfileMenu() {
            const menu = document.getElementById('profileMenu');
            const userIcon = document.querySelector('.w-10.h-10.rounded-full');

            if (menu.style.display === 'none') {
                // Position the menu relative to the user icon
                const rect = userIcon.getBoundingClientRect();
                menu.style.right = (window.innerWidth - rect.right) + 'px';
                menu.style.top = (rect.bottom + 8) + 'px';
                menu.style.display = 'block';
            } else {
                menu.style.display = 'none';
            }
        }

        // Close dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const menu = document.getElementById('profileMenu');
            const userIcon = event.target.closest('.cursor-pointer');
            if (!userIcon && !menu.contains(event.target) && menu.style.display === 'block') {
                menu.style.display = 'none';
            }
        });

        // Global search functionality
        const searchInput = document.getElementById('globalSearch');
        const searchResults = document.getElementById('searchResults');

        searchInput.addEventListener('input', debounce(async function(e) {
            const query = e.target.value.trim();

            if (query.length < 2) {
                searchResults.classList.add('hidden');
                return;
            }

            try {
                // Show loading indicator
                searchResults.innerHTML = `
                    <div class="p-4 text-center">
                        <div class="inline-block animate-spin rounded-full h-5 w-5 border-b-2 border-indigo-500"></div>
                        <p class="mt-1 text-sm text-gray-500">Searching...</p>
                    </div>
                `;
                searchResults.classList.remove('hidden');

                // Get tenant prefix for URLs
                const tenantPrefix = '{{ tenant_prefix }}' || '/';

                const response = await fetch(`${tenantPrefix}dashboard/search?q=${encodeURIComponent(query)}`);
                const data = await response.json();

                if (data.results && data.results.length > 0) {
                    searchResults.innerHTML = data.results.map(result => `
                        <a href="${tenantPrefix}dashboard/user/${result.user_id}" class="block px-4 py-3 hover:bg-indigo-50 transition-colors duration-150">
                            <div class="flex items-center justify-between">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">
                                        ${result.name || 'Unknown User'} ${result.user_id ? `(${result.user_id})` : ''}
                                    </div>
                                    <div class="text-sm text-gray-500">
                                        ${result.access_code ? `Code: ${result.access_code}` : ''}
                                    </div>
                                    <div class="text-xs text-gray-400">
                                        ${result.email || ''} ${result.whatsapp ? `• ${result.whatsapp}` : ''}
                                    </div>
                                </div>
                                <div class="text-xs px-2 py-1 rounded-full ${
                                    result.status === 'active' ? 'bg-green-100 text-green-800' :
                                    'bg-gray-100 text-gray-800'
                                }">
                                    ${result.status || 'N/A'}
                                </div>
                            </div>
                        </a>
                    `).join('');
                } else {
                    searchResults.innerHTML = `
                        <div class="px-4 py-6 text-center">
                            <div class="inline-flex items-center justify-center w-12 h-12 rounded-full bg-gray-100 text-gray-400 mb-3">
                                <i class="fas fa-search text-lg"></i>
                            </div>
                            <p class="text-sm font-medium text-gray-900 mb-1">No results found</p>
                            <p class="text-xs text-gray-500">Try different keywords or check spelling</p>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Search error:', error);
                searchResults.innerHTML = `
                    <div class="px-4 py-3 text-sm text-red-500">
                        <i class="fas fa-exclamation-circle mr-2"></i>Error searching. Please try again.
                    </div>
                `;
            }
        }, 300));

        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // Close search results when clicking outside
        document.addEventListener('click', function(event) {
            if (!searchInput.contains(event.target) && !searchResults.contains(event.target)) {
                searchResults.classList.add('hidden');
            }
        });

        // Close search results when pressing Escape
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                searchResults.classList.add('hidden');
            }
        });

        // Add animation to flash messages
        document.addEventListener('DOMContentLoaded', function() {
            const flashMessages = document.querySelectorAll('.flash-message');
            flashMessages.forEach(function(message) {
                setTimeout(function() {
                    message.style.opacity = '0.8';
                    setTimeout(function() {
                        message.style.opacity = '0';
                        setTimeout(function() {
                            message.style.display = 'none';
                        }, 300);
                    }, 4700);
                }, 300);
            });
        });
    </script>
    {% block extra_js %}{% endblock %}
    <script src="{{ tenant_url_for('static', filename='js/sidebar.js') }}"></script>
</body>
</html>