{% extends 'base.html' %}

{% block title %}API Documentation{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h1 class="text-2xl font-bold text-gray-800 mb-6">API Documentation</h1>

    <div class="bg-blue-50 border-l-4 border-blue-500 p-4 mb-6">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                </svg>
            </div>
            <div class="ml-3">
                <p class="text-sm text-blue-700">
                    <strong>Important:</strong> All API endpoints require a tenant prefix in the URL. Your current tenant is <code>{{ tenant }}</code>.
                </p>
                <p class="text-sm text-blue-700 mt-2">
                    Example: Use <code>http://your-server/{{ tenant }}/api/access-codes/add</code> instead of <code>http://your-server/api/access-codes/add</code>
                </p>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-8">
        <div class="p-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">Authentication</h2>
            <p class="mb-4">All API endpoints require authentication using an API key. Include your API key in the <code>X-API-Key</code> header of your requests.</p>
            <p class="mb-4">You can manage your API keys in the <a href="{{ tenant_url_for('api_manager.index') }}" class="text-indigo-600 hover:text-indigo-800">API Key Management</a> page.</p>

            <div class="bg-gray-50 p-4 rounded-lg">
                <h3 class="text-lg font-medium text-gray-800 mb-2">Example Request with API Key</h3>
                <pre class="bg-gray-100 p-3 rounded-lg text-sm overflow-x-auto"><code>curl -X POST http://your-server/{{ tenant }}/api/access-codes/add \
  -H "X-API-Key: your_api_key" \
  -H "Content-Type: application/json" \
  -d '{"code": "ABC123"}'</code></pre>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-8">
        <div class="p-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">Access Code Endpoints</h2>

            <div class="mb-8">
                <h3 class="text-lg font-medium text-gray-800 mb-2">Upload Access Codes</h3>
                <p class="mb-2"><span class="bg-indigo-100 text-indigo-800 px-2 py-1 rounded-md text-sm font-medium">POST</span> <code>/{{ tenant }}/api/access-codes/upload</code></p>
                <p class="mb-4">Upload a CSV or TXT file containing access codes. Existing access codes will be updated with new metadata but their original expiration dates will be preserved. New access codes will be added with the specified expiration date. Previously deleted access codes will be re-added if they appear in the uploaded file.</p>

                <h4 class="text-md font-medium text-gray-700 mb-2">Request Parameters</h4>
                <table class="min-w-full divide-y divide-gray-200 mb-4">
                    <thead>
                        <tr>
                            <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Parameter</th>
                            <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">file</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">File</td>
                            <td class="px-6 py-4 text-sm text-gray-500">CSV or TXT file containing access codes (one per line for TXT or in first column for CSV). CSV files should include a header row which will be automatically skipped.</td>
                        </tr>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">expiration_days</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Integer</td>
                            <td class="px-6 py-4 text-sm text-gray-500">Optional. Number of days until the codes expire (default: 30)</td>
                        </tr>
                    </tbody>
                </table>

                <h4 class="text-md font-medium text-gray-700 mb-2">Response</h4>
                <pre class="bg-gray-100 p-3 rounded-lg text-sm overflow-x-auto mb-4"><code>{
  "success": true,
  "added": 10,
  "updated": 5,
  "skipped": 2,
  "errors": 0,
  "expiration_days": 30,
  "expiration_date": "2023-12-31T00:00:00Z"
}</code></pre>

                <h4 class="text-md font-medium text-gray-700 mb-2">Example</h4>
                <pre class="bg-gray-100 p-3 rounded-lg text-sm overflow-x-auto"><code>curl -X POST http://your-server/{{ tenant }}/api/access-codes/upload \
  -H "X-API-Key: your_api_key" \
  -F "file=@codes.csv" \
  -F "expiration_days=30"</code></pre>
            </div>

            <div>
                <h3 class="text-lg font-medium text-gray-800 mb-2">Add Single Access Code</h3>
                <p class="mb-2"><span class="bg-indigo-100 text-indigo-800 px-2 py-1 rounded-md text-sm font-medium">POST</span> <code>/{{ tenant }}/api/access-codes/add</code></p>
                <p class="mb-4">Add a single access code. If the code already exists, the API will return an error.</p>

                <h4 class="text-md font-medium text-gray-700 mb-2">Request Body</h4>
                <pre class="bg-gray-100 p-3 rounded-lg text-sm overflow-x-auto mb-4"><code>{
  "code": "ABC123",
  "expiration_days": 30  // Optional, defaults to 30
}</code></pre>

                <h4 class="text-md font-medium text-gray-700 mb-2">Response</h4>
                <pre class="bg-gray-100 p-3 rounded-lg text-sm overflow-x-auto mb-4"><code>{
  "success": true,
  "code": "ABC123",
  "expiration_days": 30,
  "expiration_date": "2023-12-31T00:00:00Z"
}</code></pre>

                <h4 class="text-md font-medium text-gray-700 mb-2">Example</h4>
                <pre class="bg-gray-100 p-3 rounded-lg text-sm overflow-x-auto"><code>curl -X POST http://your-server/{{ tenant }}/api/access-codes/add \
  -H "X-API-Key: your_api_key" \
  -H "Content-Type: application/json" \
  -d '{"code": "ABC123", "expiration_days": 30}'</code></pre>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-8">
        <div class="p-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">Telegram Management Endpoints</h2>

            <div class="mb-8">
                <h3 class="text-lg font-medium text-gray-800 mb-2">Kick User</h3>
                <p class="mb-2"><span class="bg-indigo-100 text-indigo-800 px-2 py-1 rounded-md text-sm font-medium">POST</span> <code>/{{ tenant }}/api/telegram/kick/{user_id}</code></p>
                <p class="mb-4">Kick (ban) a user from the Telegram channel.</p>

                <h4 class="text-md font-medium text-gray-700 mb-2">URL Parameters</h4>
                <table class="min-w-full divide-y divide-gray-200 mb-4">
                    <thead>
                        <tr>
                            <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Parameter</th>
                            <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">user_id</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Integer</td>
                            <td class="px-6 py-4 text-sm text-gray-500">The Telegram user ID to kick</td>
                        </tr>
                    </tbody>
                </table>

                <h4 class="text-md font-medium text-gray-700 mb-2">Response</h4>
                <pre class="bg-gray-100 p-3 rounded-lg text-sm overflow-x-auto mb-4"><code>{
  "success": true,
  "message": "User 123456789 kicked successfully."
}</code></pre>

                <h4 class="text-md font-medium text-gray-700 mb-2">Example</h4>
                <pre class="bg-gray-100 p-3 rounded-lg text-sm overflow-x-auto"><code>curl -X POST http://your-server/{{ tenant }}/api/telegram/kick/123456789 \
  -H "X-API-Key: your_api_key"</code></pre>
            </div>

            <div class="mb-8">
                <h3 class="text-lg font-medium text-gray-800 mb-2">Unban User</h3>
                <p class="mb-2"><span class="bg-indigo-100 text-indigo-800 px-2 py-1 rounded-md text-sm font-medium">POST</span> <code>/{{ tenant }}/api/telegram/unban/{user_id}</code></p>
                <p class="mb-4">Unban a user from the Telegram channel.</p>

                <h4 class="text-md font-medium text-gray-700 mb-2">URL Parameters</h4>
                <table class="min-w-full divide-y divide-gray-200 mb-4">
                    <thead>
                        <tr>
                            <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Parameter</th>
                            <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">user_id</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Integer</td>
                            <td class="px-6 py-4 text-sm text-gray-500">The Telegram user ID to unban</td>
                        </tr>
                    </tbody>
                </table>

                <h4 class="text-md font-medium text-gray-700 mb-2">Response</h4>
                <pre class="bg-gray-100 p-3 rounded-lg text-sm overflow-x-auto mb-4"><code>{
  "success": true,
  "message": "User 123456789 unbanned successfully."
}</code></pre>

                <h4 class="text-md font-medium text-gray-700 mb-2">Example</h4>
                <pre class="bg-gray-100 p-3 rounded-lg text-sm overflow-x-auto"><code>curl -X POST http://your-server/{{ tenant }}/api/telegram/unban/123456789 \
  -H "X-API-Key: your_api_key"</code></pre>
            </div>

            <div class="mb-8">
                <h3 class="text-lg font-medium text-gray-800 mb-2">Get Active Telegram Bot Info</h3>
                <p class="mb-2"><span class="bg-green-100 text-green-800 px-2 py-1 rounded-md text-sm font-medium">GET</span> <code>/{{ tenant }}/api/telegram/bot-info</code></p>
                <p class="mb-4">Get information about the active Telegram bot (or most recently updated one if no active bot exists).</p>

                <h4 class="text-md font-medium text-gray-700 mb-2">Response</h4>
                <pre class="bg-gray-100 p-3 rounded-lg text-sm overflow-x-auto mb-4"><code>{
  "success": true,
  "bot_token": "7598445046:AAERK6pET7I6HkGUnK1Ay_xu9J5twDwFSDU",
  "bot_username": "MyAccessBot_1234_bot",
  "name": "MyAccessBot",
  "channel_id": "-1002682842201"
}</code></pre>

                <h4 class="text-md font-medium text-gray-700 mb-2">Example</h4>
                <pre class="bg-gray-100 p-3 rounded-lg text-sm overflow-x-auto"><code>curl -X GET http://your-server/{{ tenant }}/api/telegram/bot-info \
  -H "X-API-Key: your_api_key"</code></pre>
            </div>

            <div class="mb-8">
                <h3 class="text-lg font-medium text-gray-800 mb-2">Get Specific Telegram Bot Info</h3>
                <p class="mb-2"><span class="bg-green-100 text-green-800 px-2 py-1 rounded-md text-sm font-medium">GET</span> <code>/{{ tenant }}/api/telegram/bot-info/{bot_id}</code></p>
                <p class="mb-4">Get information about a specific Telegram bot by ID, telegram_id, or username.</p>

                <h4 class="text-md font-medium text-gray-700 mb-2">URL Parameters</h4>
                <table class="min-w-full divide-y divide-gray-200 mb-4">
                    <thead>
                        <tr>
                            <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Parameter</th>
                            <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">bot_id</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">String</td>
                            <td class="px-6 py-4 text-sm text-gray-500">The bot ID (MongoDB ObjectId), telegram_id (number), or telegram_username</td>
                        </tr>
                    </tbody>
                </table>

                <h4 class="text-md font-medium text-gray-700 mb-2">Response</h4>
                <pre class="bg-gray-100 p-3 rounded-lg text-sm overflow-x-auto mb-4"><code>{
  "success": true,
  "bot_token": "7598445046:AAERK6pET7I6HkGUnK1Ay_xu9J5twDwFSDU",
  "bot_username": "MyAccessBot_1234_bot",
  "name": "MyAccessBot",
  "channel_id": "-1002682842201",
  "is_active": true
}</code></pre>

                <h4 class="text-md font-medium text-gray-700 mb-2">Example</h4>
                <pre class="bg-gray-100 p-3 rounded-lg text-sm overflow-x-auto"><code>curl -X GET http://your-server/{{ tenant }}/api/telegram/bot-info/7598445046 \
  -H "X-API-Key: your_api_key"</code></pre>
            </div>

            <div>
                <h3 class="text-lg font-medium text-gray-800 mb-2">List All Telegram Bots</h3>
                <p class="mb-2"><span class="bg-green-100 text-green-800 px-2 py-1 rounded-md text-sm font-medium">GET</span> <code>/{{ tenant }}/api/telegram/bots</code></p>
                <p class="mb-4">Get a list of all Telegram bots in the system.</p>

                <h4 class="text-md font-medium text-gray-700 mb-2">Response</h4>
                <pre class="bg-gray-100 p-3 rounded-lg text-sm overflow-x-auto mb-4"><code>{
  "success": true,
  "bots": [
    {
      "id": "67fce3592bac94f71d5b006a",
      "name": "MyAccessBot",
      "username": "MyAccessBot_1234_bot",
      "telegram_id": 7598445046,
      "is_active": true,
      "channel_id": "-1002682842201",
      "last_updated": "2025-04-15T15:29:02.942Z"
    },
    {
      "id": "67fce3592bac94f71d5b006b",
      "name": "SecondBot",
      "username": "SecondBot_bot",
      "telegram_id": 7598445047,
      "is_active": false,
      "channel_id": "-1002682842202",
      "last_updated": "2025-04-14T10:28:41.716Z"
    }
  ],
  "count": 2
}</code></pre>

                <h4 class="text-md font-medium text-gray-700 mb-2">Example</h4>
                <pre class="bg-gray-100 p-3 rounded-lg text-sm overflow-x-auto"><code>curl -X GET http://your-server/{{ tenant }}/api/telegram/bots \
  -H "X-API-Key: your_api_key"</code></pre>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-xl shadow-md overflow-hidden mb-8">
        <div class="p-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">Verification Request Endpoints</h2>

            <div class="mb-8">
                <h3 class="text-lg font-medium text-gray-800 mb-2">Approve Verification Request</h3>
                <p class="mb-2"><span class="bg-indigo-100 text-indigo-800 px-2 py-1 rounded-md text-sm font-medium">POST</span> <code>/{{ tenant }}/api/verification-requests/approve/{request_id}</code></p>
                <p class="mb-4">Approve a verification request.</p>

                <h4 class="text-md font-medium text-gray-700 mb-2">URL Parameters</h4>
                <table class="min-w-full divide-y divide-gray-200 mb-4">
                    <thead>
                        <tr>
                            <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Parameter</th>
                            <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">request_id</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">String</td>
                            <td class="px-6 py-4 text-sm text-gray-500">The verification request ID to approve</td>
                        </tr>
                    </tbody>
                </table>

                <h4 class="text-md font-medium text-gray-700 mb-2">Response</h4>
                <pre class="bg-gray-100 p-3 rounded-lg text-sm overflow-x-auto mb-4"><code>{
  "success": true,
  "message": "Verification request approved successfully",
  "user_id": 123456789,
  "telegram_message_sent": true,
  "telegram_error": null
}</code></pre>
                <p class="mb-4">The API now sends a Telegram message with an invite link to the user, just like the UI version.</p>

                <h4 class="text-md font-medium text-gray-700 mb-2">Example</h4>
                <pre class="bg-gray-100 p-3 rounded-lg text-sm overflow-x-auto"><code>curl -X POST http://your-server/{{ tenant }}/api/verification-requests/approve/abc123 \
  -H "X-API-Key: your_api_key"</code></pre>
            </div>

            <div>
                <h3 class="text-lg font-medium text-gray-800 mb-2">Deny Verification Request</h3>
                <p class="mb-2"><span class="bg-indigo-100 text-indigo-800 px-2 py-1 rounded-md text-sm font-medium">POST</span> <code>/{{ tenant }}/api/verification-requests/deny/{request_id}</code></p>
                <p class="mb-4">Deny a verification request.</p>

                <h4 class="text-md font-medium text-gray-700 mb-2">URL Parameters</h4>
                <table class="min-w-full divide-y divide-gray-200 mb-4">
                    <thead>
                        <tr>
                            <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Parameter</th>
                            <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                            <th class="px-6 py-3 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">request_id</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">String</td>
                            <td class="px-6 py-4 text-sm text-gray-500">The verification request ID to deny</td>
                        </tr>
                    </tbody>
                </table>

                <h4 class="text-md font-medium text-gray-700 mb-2">Response</h4>
                <pre class="bg-gray-100 p-3 rounded-lg text-sm overflow-x-auto mb-4"><code>{
  "success": true,
  "message": "Verification request denied successfully",
  "user_id": 123456789,
  "telegram_message_sent": true,
  "telegram_error": null
}</code></pre>
                <p class="mb-4">The API now sends a Telegram message with a denial notification to the user, just like the UI version.</p>

                <h4 class="text-md font-medium text-gray-700 mb-2">Example</h4>
                <pre class="bg-gray-100 p-3 rounded-lg text-sm overflow-x-auto"><code>curl -X POST http://your-server/{{ tenant }}/api/verification-requests/deny/abc123 \
  -H "X-API-Key: your_api_key"</code></pre>
            </div>
        </div>
    </div>

    <div class="bg-white rounded-xl shadow-md overflow-hidden">
        <div class="p-6">
            <h2 class="text-xl font-semibold text-gray-800 mb-4">Error Responses</h2>
            <p class="mb-4">All API endpoints return a JSON response with a <code>success</code> field indicating whether the request was successful. If <code>success</code> is <code>false</code>, the response will include <code>error</code> and <code>message</code> fields with details about the error.</p>

            <h4 class="text-md font-medium text-gray-700 mb-2">Example Error Response</h4>
            <pre class="bg-gray-100 p-3 rounded-lg text-sm overflow-x-auto"><code>{
  "success": false,
  "error": "Invalid API key",
  "message": "The provided API key is invalid or has been revoked"
}</code></pre>
        </div>
    </div>
</div>
{% endblock %}
