<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome</title>
    <!-- Tailwind CSS from CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#4f46e5',
                        'primary-dark': '#4338ca',
                        'primary-light': '#eef2ff',
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f8fafc;
        }
    </style>
</head>
<body class="min-h-screen flex items-center justify-center bg-gray-50">
    <div class="max-w-md w-full p-8 bg-white rounded-xl shadow-lg">
        <div class="text-center">
            <i class="fas fa-rocket text-5xl text-primary mb-4"></i>
            <h1 class="text-3xl font-bold text-gray-800 mb-2">Welcome!</h1>
            <p class="text-gray-600 mb-6">This tenant has not been set up yet.</p>
            <div class="bg-primary-light p-4 rounded-lg mb-6">
                <p class="text-primary-dark">
                    <i class="fas fa-info-circle mr-2"></i>
                    If you're a customer, please contact your administrator.
                </p>
            </div>
            <p class="text-sm text-gray-500">
                Tenant: <span class="font-semibold">{{ tenant_name }}</span>
            </p>
        </div>
    </div>
</body>
</html>
