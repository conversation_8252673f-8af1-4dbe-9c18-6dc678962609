{% extends 'base.html' %}

{% block title %}Admin Profile{% endblock %}
{% block header_title %}Profile Settings{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/admin_profile.css') }}">
{% endblock %}

{% block content %}
<div class="max-w-6xl mx-auto py-8 px-4 sm:px-6 lg:px-8">

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <div class="lg:col-span-1">
             <!-- Added flex flex-col here -->
            <div class="profile-card h-full flex flex-col">
                <div class="profile-header">
                    <i class="fas fa-user-circle mr-2"></i>
                    <h2>Admin Profile</h2>
                </div>
                 <!-- Added flex-grow here -->
                <div class="profile-content flex-grow">
                    <div class="flex flex-col items-center mb-6">
                        <div class="profile-avatar mb-3">
                            <i class="fas fa-user text-4xl text-gray-400"></i>
                        </div>
                        <h3 class="profile-name font-semibold text-lg">{{ admin.username if admin and admin.username else 'Administrator' }}</h3>
                        <p class="profile-role text-sm text-gray-500">Administrator</p>
                    </div>

                    <div class="space-y-4">
                        <div class="profile-info-item">
                            <div class="profile-info-icon"><i class="fas fa-envelope w-5 text-center"></i></div>
                            <div class="profile-info-content">
                                <div class="profile-info-label">Email</div>
                                <div class="profile-info-value">{{ admin.email if admin and admin.email else '<EMAIL>' }}</div>
                            </div>
                        </div>
                        <div class="profile-info-item">
                            <div class="profile-info-icon"><i class="fas fa-clock w-5 text-center"></i></div>
                            <div class="profile-info-content">
                                <div class="profile-info-label">Last Login</div>
                                <div class="profile-info-value">{{ admin.last_login.strftime('%Y-%m-%d %H:%M:%S') if admin and admin.last_login else 'N/A' }}</div>
                            </div>
                        </div>
                        <div class="profile-info-item">
                            <div class="profile-info-icon"><i class="fas fa-shield-alt w-5 text-center"></i></div>
                            <div class="profile-info-content">
                                <div class="profile-info-label">Role</div>
                                <div class="profile-info-value">
                                    <span class="profile-badge">Administrator</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="lg:col-span-1">
            <!-- No changes needed here, the form naturally takes up space -->
            <div class="profile-card h-full">
                <div class="profile-header">
                    <i class="fas fa-lock mr-2"></i>
                    <h2>Change Password</h2>
                </div>
                <div class="profile-content">
                    <form method="POST" action="{{ tenant_url_for('dashboard.change_password_route') }}" class="password-form space-y-6">
                        <div class="form-group">
                            <label for="current_password" class="form-label">Current Password</label>
                            <div class="form-input-wrapper">
                                <i class="fas fa-key form-input-icon"></i>
                                <input type="password" id="current_password" name="current_password" required class="form-input">
                                <div class="form-input-toggle toggle-password" data-target="current_password">
                                    <i class="fas fa-eye"></i>
                                </div>
                            </div>
                            <p class="form-hint">Enter your current password</p>
                        </div>
                        <div class="form-group">
                            <label for="new_password" class="form-label">New Password</label>
                            <div class="form-input-wrapper">
                                <i class="fas fa-lock form-input-icon"></i>
                                <input type="password" id="new_password" name="new_password" required class="form-input">
                                <div class="form-input-toggle toggle-password" data-target="new_password">
                                    <i class="fas fa-eye"></i>
                                </div>
                            </div>
                            <p class="form-hint">Min. 8 characters</p>
                        </div>
                        <div class="form-group">
                            <label for="confirm_password" class="form-label">Confirm New Password</label>
                            <div class="form-input-wrapper">
                                <i class="fas fa-lock form-input-icon"></i>
                                <input type="password" id="confirm_password" name="confirm_password" required class="form-input">
                                <div class="form-input-toggle toggle-password" data-target="confirm_password">
                                    <i class="fas fa-eye"></i>
                                </div>
                            </div>
                            <p class="form-hint">Re-enter new password</p>
                        </div>
                        <div class="form-submit pt-4">
                            <button type="submit" class="btn-primary">
                                <i class="fas fa-save mr-1"></i> Update Password
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

    </div>

    <div>
        <div class="profile-card">
            <div class="profile-header">
                <i class="fas fa-history mr-2"></i>
                <h2>Recent Activity</h2>
                <span class="profile-badge ml-auto">{{ admin_activities|length if admin_activities else 0 }} Activities</span>
            </div>
            <div class="overflow-x-auto">
                {% if admin_activities %}
                <table class="activity-table w-full text-sm text-left text-gray-500">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                        <tr>
                            <th scope="col" class="px-6 py-3">Action</th>
                            <th scope="col" class="px-6 py-3">Details</th>
                            <th scope="col" class="px-6 py-3">Timestamp</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for activity in admin_activities %}
                        <tr class="bg-white border-b hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    {% set action_lower = activity.action|lower %}
                                    {% if 'login' in action_lower %}
                                        <div class="activity-icon login"><i class="fas fa-sign-in-alt"></i></div>
                                    {% elif 'password' in action_lower %}
                                        <div class="activity-icon password"><i class="fas fa-key"></i></div>
                                    {% elif 'add' in action_lower or 'create' in action_lower %}
                                        <div class="activity-icon add"><i class="fas fa-plus"></i></div>
                                    {% elif 'delete' in action_lower %}
                                        <div class="activity-icon delete"><i class="fas fa-trash-alt"></i></div>
                                    {% elif 'edit' in action_lower or 'update' in action_lower %}
                                        <div class="activity-icon edit"><i class="fas fa-edit"></i></div>
                                    {% elif 'view' in action_lower %}
                                        <div class="activity-icon view"><i class="fas fa-eye"></i></div>
                                    {% elif 'logout' in action_lower %}
                                        <div class="activity-icon logout"><i class="fas fa-sign-out-alt"></i></div>
                                    {% else %}
                                        <div class="activity-icon default"><i class="fas fa-cog"></i></div>
                                    {% endif %}
                                    <span class="activity-action ml-2">{{ activity.action }}</span>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="activity-details break-words">{{ activity.details }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="activity-time flex items-center">
                                    <i class="far fa-clock mr-1"></i>
                                    {{ activity.timestamp.strftime('%Y-%m-%d %H:%M:%S') if activity.timestamp else 'N/A' }}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
                {% else %}
                <div class="empty-state text-center py-10">
                    <div class="empty-icon text-gray-400 mb-4">
                        <i class="fas fa-history text-4xl"></i>
                    </div>
                    <h3 class="empty-title text-lg font-semibold text-gray-700 mb-2">No activity logs</h3>
                    <p class="empty-message text-sm text-gray-500">There are no recent activities recorded.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Password toggle functionality (ensure elements exist before adding listeners)
        const toggleButtons = document.querySelectorAll('.toggle-password');
        toggleButtons.forEach(button => {
            const targetId = button.getAttribute('data-target');
            const passwordInput = document.getElementById(targetId);
            const icon = button.querySelector('i');

            if (passwordInput && icon) { // Check if elements exist
                 button.addEventListener('click', function() {
                    if (passwordInput.type === 'password') {
                        passwordInput.type = 'text';
                        icon.classList.remove('fa-eye');
                        icon.classList.add('fa-eye-slash');
                    } else {
                        passwordInput.type = 'password';
                        icon.classList.remove('fa-eye-slash');
                        icon.classList.add('fa-eye');
                    }
                });
            } else {
                console.warn(`Toggle password elements not found for target: ${targetId}`);
            }
        });
    });
</script>
{% endblock %}