{% extends "base.html" %}

{% block title %}Broker Setup{% endblock %}

{% block content %}
<div class="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden mb-8 transition-all duration-300">
    <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center bg-gradient-to-r from-indigo-50 to-white">
        <div class="flex items-center">
            <i class="fas fa-chart-line text-indigo-500 text-xl mr-3"></i>
            <h2 class="text-lg font-semibold text-gray-800">Broker Setup</h2>
        </div>
        <div class="flex items-center space-x-3">
            <button id="refreshSetupBtn" class="px-4 py-2 bg-indigo-100 text-indigo-700 rounded-lg hover:bg-indigo-200 transition-all duration-200 shadow-sm hover:shadow flex items-center transform hover:-translate-y-0.5">
                <i class="fas fa-sync-alt mr-2"></i>
                Refresh
            </button>
            <a href="{{ tenant_url_for('dashboard.home_route') }}" class="back-button px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-all duration-200 shadow-sm hover:shadow flex items-center transform hover:-translate-y-0.5">
                <i class="fas fa-arrow-left mr-2"></i>
                Back to Dashboard
            </a>
        </div>
    </div>

    <div class="p-6">
        <!-- Status Summary Card -->
        <div class="mb-6 p-4 bg-indigo-50 rounded-lg border border-indigo-100 shadow-sm">
            <div class="flex flex-wrap items-center justify-between">
                <div class="flex items-center mb-2 md:mb-0">
                    <div class="w-10 h-10 rounded-full bg-indigo-100 flex items-center justify-center mr-3">
                        <i class="fas fa-cog text-indigo-500"></i>
                    </div>
                    <div>
                        <h3 class="font-medium text-gray-800">Broker Integration Status</h3>
                        <p class="text-sm text-gray-600">Current configuration status</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center">
                        <div id="connectionStatusIndicator" class="w-3 h-3 rounded-full bg-green-500 mr-2 pulse-animation"></div>
                        <span id="connectionStatus" class="text-sm font-medium text-gray-700">Connected</span>
                    </div>
                    <div class="flex items-center">
                        <div id="syncStatusIndicator" class="w-3 h-3 rounded-full bg-yellow-500 mr-2"></div>
                        <span id="syncStatus" class="text-sm font-medium text-gray-700">Last sync: 5 min ago</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <!-- Left Column -->
            <div>
                <!-- Toggle Switch -->
                <div class="mb-8 p-5 bg-white rounded-lg border border-gray-200 shadow-sm transition-all duration-300 hover:shadow-md">
                    <h3 class="text-md font-semibold text-gray-700 mb-4 flex items-center">
                        <i class="fas fa-toggle-on text-indigo-500 mr-2"></i>
                        OPERATION MODE
                    </h3>
                    <div class="inline-flex rounded-md shadow-sm" role="group">
                        <button type="button" id="autoModeBtn" class="px-4 py-2 text-sm font-medium bg-indigo-500 text-white rounded-l-lg focus:z-10 focus:ring-2 focus:ring-indigo-500 focus:text-white transition-all duration-200">
                            <i class="fas fa-robot mr-1"></i> Auto Mode
                        </button>
                        <button type="button" id="manualModeBtn" class="px-4 py-2 text-sm font-medium bg-gray-200 text-gray-700 rounded-r-lg focus:z-10 focus:ring-2 focus:ring-indigo-500 focus:text-white transition-all duration-200">
                            <i class="fas fa-user-cog mr-1"></i> Manual Mode
                        </button>
                    </div>
                    <div class="mt-3 text-xs text-gray-500">
                        <span id="modeDescription" class="italic">Auto mode automatically syncs user data with broker API</span>
                    </div>
                </div>

                <!-- JWT Token Status -->
                <div class="mb-8 p-5 bg-white rounded-lg border border-gray-200 shadow-sm transition-all duration-300 hover:shadow-md">
                    <h3 class="text-md font-semibold text-gray-700 mb-4 flex items-center">
                        <i class="fas fa-key text-indigo-500 mr-2"></i>
                        TOKEN STATUS
                    </h3>
                    <div class="flex items-center mb-2">
                        <span class="text-md font-medium text-gray-700 mr-2">JWT Status:</span>
                        <span id="jwtStatus" class="px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                            <i class="fas fa-check-circle mr-1"></i> Active
                        </span>
                    </div>
                    <div class="flex items-center">
                        <span class="text-md font-medium text-gray-700 mr-2">Token Status:</span>
                        <span id="tokenStatusDisplay" class="px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                            <i class="fas fa-info-circle mr-1"></i> Not validated
                        </span>
                    </div>
                    <div class="mt-3 text-xs text-gray-500">
                        <span id="tokenExpiry" class="italic">Token expires in 30 days</span>
                    </div>
                </div>

                <!-- Logs -->
                <div class="mb-8 p-5 bg-white rounded-lg border border-gray-200 shadow-sm transition-all duration-300 hover:shadow-md">
                    <div class="flex justify-between items-center mb-4">
                        <div class="flex items-center">
                            <h3 class="text-md font-semibold text-gray-700">CRON LOGS</h3>
                            <div id="liveIndicator" class="ml-2 w-2 h-2 rounded-full bg-green-500 pulse-animation"></div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <div class="text-xs text-gray-500 italic" id="logUpdateStatus">Auto-refresh: ON</div>
                            <button id="toggleAutoRefreshBtn" class="text-indigo-500 hover:text-indigo-700 transition-colors duration-200 px-2 py-1 rounded hover:bg-indigo-50">
                                <i class="fas fa-pause"></i>
                            </button>
                            <button id="refreshLogsBtn" class="text-indigo-500 hover:text-indigo-700 transition-colors duration-200 px-2 py-1 rounded hover:bg-indigo-50">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Logs content -->
                    <div class="relative">
                        <div class="max-h-60 overflow-y-auto scrollbar-thin scrollbar-thumb-indigo-200 scrollbar-track-gray-100 p-4 bg-gray-900 text-gray-100 rounded-lg font-mono">
                            <div id="customerLogContent" class="text-sm leading-relaxed">
                                <div class="flex items-center justify-center h-20 text-gray-400">
                                    <i class="fas fa-spinner fa-spin mr-2"></i> Loading logs...
                                </div>
                            </div>
                        </div>
                        <div class="absolute bottom-0 left-0 right-0 h-6 bg-gradient-to-t from-gray-900 to-transparent pointer-events-none"></div>
                    </div>
                </div>
            </div>

            <!-- Right Column -->
            <div>
                <!-- Broker Selection -->
                <div class="mb-8 p-5 bg-white rounded-lg border border-gray-200 shadow-sm transition-all duration-300 hover:shadow-md">
                    <h3 class="text-md font-semibold text-gray-700 mb-4 flex items-center">
                        <i class="fas fa-building text-indigo-500 mr-2"></i>
                        SELECT YOUR BROKER
                    </h3>
                    <div class="relative">
                        <select id="brokerSelect" class="block w-full px-4 py-3 pr-8 leading-tight text-gray-700 bg-white border border-gray-300 rounded-lg appearance-none focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-200">
                            <option value="exness">Exness</option>
                            <option value="other">Other Broker</option>
                        </select>
                        <div class="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                            <i class="fas fa-chevron-down text-gray-400"></i>
                        </div>
                    </div>
                    <div class="mt-3 text-xs text-gray-500">
                        <span class="italic">Select the broker you want to integrate with</span>
                    </div>
                </div>

                <!-- Broker API Credentials -->
                <div class="mb-8 p-5 bg-white rounded-lg border border-gray-200 shadow-sm transition-all duration-300 hover:shadow-md">
                    <h3 class="text-md font-semibold text-gray-700 mb-4 flex items-center">
                        <i class="fas fa-lock text-indigo-500 mr-2"></i>
                        BROKER API CREDENTIALS
                    </h3>
                    <button id="generateTokenBtn" class="w-full px-4 py-3 bg-indigo-500 text-white rounded-lg hover:bg-indigo-600 transition-all duration-200 shadow-sm hover:shadow flex items-center justify-center transform hover:-translate-y-0.5 relative overflow-hidden">
                        <span class="absolute inset-0 bg-white opacity-10 grainy-effect"></span>
                        <i class="fas fa-key mr-2"></i>
                        <span>Generate Login Token</span>
                        <span id="generateSpinner" class="ml-2 hidden">
                            <i class="fas fa-circle-notch fa-spin"></i>
                        </span>
                    </button>

                    <!-- Development Mode Toggle -->
                    <div class="mt-3 flex items-center justify-between text-xs text-gray-500">
                        <span class="italic">Having connection issues?</span>
                        <div class="flex items-center group relative">
                            <span class="mr-2">Development Mode</span>
                            <i class="fas fa-question-circle text-gray-400 mr-2 cursor-help"></i>
                            <div class="absolute bottom-full left-0 mb-2 w-64 bg-gray-800 text-white p-2 rounded-lg shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 text-xs">
                                Development mode generates a mock token when the broker API is unavailable. Use this only for testing purposes.
                            </div>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" id="devModeToggle" class="sr-only peer">
                                <div class="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-indigo-500"></div>
                            </label>
                        </div>
                    </div>
                </div>


                <!-- Token Security Notice -->
                <div class="mb-8 p-5 bg-white rounded-lg border border-gray-200 shadow-sm transition-all duration-300 hover:shadow-md">
                    <div class="flex items-center mb-4">
                        <h3 class="text-md font-semibold text-gray-700 flex items-center">
                            <i class="fas fa-shield-alt text-indigo-500 mr-2"></i>
                            TOKEN SECURITY
                        </h3>
                    </div>
                    <div class="p-4 bg-indigo-50 rounded-lg">
                        <div class="flex items-start">
                            <i class="fas fa-info-circle text-indigo-500 mt-1 mr-3"></i>
                            <p class="text-sm text-indigo-700">
                                For security reasons, the JWT token is stored securely on the server and not displayed.
                                Use the validation button below to check if your token is valid and active.
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Validate Button -->
                <div class="mb-4 p-5 bg-white rounded-lg border border-gray-200 shadow-sm transition-all duration-300 hover:shadow-md">
                    <h3 class="text-md font-semibold text-gray-700 mb-4 flex items-center">
                        <i class="fas fa-check-circle text-indigo-500 mr-2"></i>
                        VALIDATION
                    </h3>
                    <button id="validateBtn" class="w-full px-6 py-3 bg-indigo-500 text-white rounded-lg hover:bg-indigo-600 transition-all duration-200 shadow-sm hover:shadow flex items-center justify-center transform hover:-translate-y-0.5 relative overflow-hidden">
                        <span class="absolute inset-0 bg-white opacity-10 grainy-effect"></span>
                        <i class="fas fa-check-circle mr-2"></i>
                        <span>Validate Token</span>
                        <span id="validateSpinner" class="ml-2 hidden">
                            <i class="fas fa-circle-notch fa-spin"></i>
                        </span>
                    </button>

                    <!-- Validation Status -->
                    <div class="mt-4">
                        <div class="flex items-center">
                            <span class="text-md font-medium text-gray-700 mr-2">Status:</span>
                            <span id="validationStatus" class="px-3 py-1 rounded-full text-sm font-medium bg-gray-100 text-gray-800">
                                Not validated
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Login Token Modal -->
<div id="loginTokenModal" class="fixed inset-0 bg-gray-900 bg-opacity-50 backdrop-blur-sm hidden overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-6 w-full max-w-md shadow-xl rounded-xl bg-white transform transition-all duration-300 scale-100">
        <div>
            <div class="flex justify-between items-center mb-5">
                <h3 class="text-xl font-semibold text-gray-800 flex items-center">
                    <i class="fas fa-key text-indigo-500 mr-3"></i>
                    Exness Affiliates Login
                </h3>
                <button type="button" class="text-gray-400 hover:text-gray-600 transition-colors duration-200 rounded-full hover:bg-gray-100 p-2" onclick="closeLoginTokenModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <!-- Progress Steps -->
            <div class="mb-6">
                <div class="flex items-center justify-between">
                    <div class="flex flex-col items-center">
                        <div class="w-8 h-8 rounded-full bg-indigo-500 flex items-center justify-center text-white text-sm font-medium">1</div>
                        <span class="text-xs mt-1 text-indigo-500 font-medium">Credentials</span>
                    </div>
                    <div class="flex-1 h-1 mx-2 bg-indigo-200">
                        <div class="h-1 bg-indigo-500" style="width: 0%" id="progressBar"></div>
                    </div>
                    <div class="flex flex-col items-center">
                        <div class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center text-gray-500 text-sm font-medium">2</div>
                        <span class="text-xs mt-1 text-gray-500">Token</span>
                    </div>
                </div>
            </div>

            <div id="formErrors" class="mb-4 hidden">
                <div class="p-3 bg-red-50 border-l-4 border-red-500 rounded-md">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-circle text-red-500"></i>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-red-700" id="errorMessage">Please correct the errors below.</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="space-y-5">
                <div>
                    <label for="username" class="block text-sm font-medium text-gray-700 mb-2">EMAIL</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-envelope text-gray-400"></i>
                        </div>
                        <input type="email" name="username" id="username" required
                               class="pl-10 block w-full rounded-lg border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-500 focus:ring-opacity-50 py-3 transition-all duration-200"
                               placeholder="Enter your Exness Affiliates email">
                        <div id="usernameError" class="mt-1 text-xs text-red-500 hidden">Please enter a valid email address</div>
                    </div>
                </div>
                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-2">PASSWORD</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-lock text-gray-400"></i>
                        </div>
                        <input type="password" name="password" id="password" required
                               class="pl-10 block w-full rounded-lg border-gray-300 shadow-sm focus:border-indigo-500 focus:ring focus:ring-indigo-500 focus:ring-opacity-50 py-3 transition-all duration-200"
                               placeholder="Enter your password">
                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center">
                            <button type="button" id="togglePassword" class="text-gray-400 hover:text-gray-600 focus:outline-none">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div id="passwordError" class="mt-1 text-xs text-red-500 hidden">Password must be at least 6 characters</div>
                    </div>
                </div>
                <div class="pt-2">
                    <button id="authenticateBtn" class="w-full px-4 py-3 bg-indigo-500 text-white rounded-lg hover:bg-indigo-600 transition-all duration-200 shadow-sm hover:shadow flex items-center justify-center transform hover:-translate-y-0.5 relative overflow-hidden">
                        <span class="absolute inset-0 bg-white opacity-10 grainy-effect"></span>
                        <i class="fas fa-sign-in-alt mr-2"></i>
                        <span>Authenticate</span>
                        <span id="authSpinner" class="ml-2 hidden">
                            <i class="fas fa-circle-notch fa-spin"></i>
                        </span>
                    </button>
                </div>
                <div class="text-xs text-gray-500 mt-4">
                    <div class="p-3 bg-indigo-50 rounded-md">
                        <p class="text-center text-indigo-700 mt-1">
                            <i class="fas fa-info-circle mr-1"></i>
                            Please use the same email and password you use to log in to my.exnessaffiliates.com
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<style>
    /* Pulse animation for live indicator */
    @keyframes pulse {
        0% {
            transform: scale(0.95);
            box-shadow: 0 0 0 0 rgba(72, 187, 120, 0.7);
        }

        70% {
            transform: scale(1);
            box-shadow: 0 0 0 6px rgba(72, 187, 120, 0);
        }

        100% {
            transform: scale(0.95);
            box-shadow: 0 0 0 0 rgba(72, 187, 120, 0);
        }
    }

    .pulse-animation {
        animation: pulse 2s infinite;
    }
</style>
<script src="{{ tenant_url_for('static', filename='js/broker_setup.js') }}"></script>
<script>
    // Additional initialization for broker setup page
    document.addEventListener('DOMContentLoaded', function() {
        // Add animation classes for elements that should animate on page load
        setTimeout(() => {
            const animatedElements = document.querySelectorAll('.animate-on-load');
            animatedElements.forEach(el => {
                el.classList.add('opacity-100', 'translate-y-0');
                el.classList.remove('opacity-0', 'translate-y-4');
            });

            // Make sure logs are loaded
            if (typeof fetchCustomerLogs === 'function') {
                fetchCustomerLogs();
            }
        }, 100);
    });
</script>
{% endblock %}
