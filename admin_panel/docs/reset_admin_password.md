# Reset Admin Password Script

This document describes how to use the `reset_admin_password.py` script to reset the admin password for a customer database.

## Overview

The `reset_admin_password.py` script allows you to reset the admin password for a specific customer database. This is useful when an admin password is forgotten or needs to be changed for security reasons.

## Usage

```bash
python reset_admin_password.py --password <new_password> --cn <customer_name>
```

### Arguments

- `--password`: The new admin password (required)
- `--cn`: The customer name without the `_custdb` suffix (required)

### Example

To reset the admin password for the customer database `newcustomer_custdb` to `NewPassword123`:

```bash
python reset_admin_password.py --password NewPassword123 --cn newcustomer
```

## How It Works

The script performs the following steps:

1. Connects to the MongoDB server
2. Checks if the specified customer database exists
3. Finds the admin user in the `admins` collection
4. Generates a new password hash using Werkzeug's `generate_password_hash` function
5. Updates the admin user's password hash in the database

## Requirements

- Python 3.6 or higher
- pymongo
- werkzeug

## Security Considerations

- The script should be run by authorized personnel only
- The script should be run in a secure environment
- The new password should be strong and meet your organization's password policy
- The script logs the new password hash for verification purposes, but not the plaintext password

## Troubleshooting

If the script fails, check the following:

- Ensure the MongoDB server is running and accessible
- Ensure the customer database exists
- Ensure the `admins` collection exists in the customer database
- Ensure the admin user exists in the `admins` collection
- Check the logs for specific error messages
