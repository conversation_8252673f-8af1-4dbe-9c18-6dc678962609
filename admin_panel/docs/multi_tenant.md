# Multi-Tenant Functionality

This document describes the multi-tenant functionality implemented in the admin panel application.

## Overview

The multi-tenant functionality allows the application to serve multiple customers (tenants) with isolated data. Each tenant has their own MongoDB database named `customer_name_custdb`.

## URL Structure

The application uses URL-based tenant resolution. The tenant name is extracted from the first segment of the URL path:

```
http://localhost:5001/{tenant_name}/dashboard/
http://localhost:5001/{tenant_name}/login
```

In production, this would map to:

```
https://{tenant_name}.domainname.com/dashboard/
https://{tenant_name}.domainname.com/login
```

## Database Structure

Each tenant has their own MongoDB database named `{tenant_name}_custdb`. All tenant databases have the same schema with the following collections:

- `activity_logs`: Logs of admin activities
- `admins`: Admin user accounts
- `access_codes`: Access codes for user registration
- `master_user_data`: User data
- `verification_request`: User verification requests
- `support_request`: User support requests
- `telegram_bots`: Telegram bot configurations
- `api_keys`: API keys for API access

## Implementation Details

### Tenant Resolution

The application uses a WSGI middleware (`TenantMiddleware`) to rewrite URLs with tenant prefixes. The middleware extracts the tenant name from the URL path and rewrites the URL to remove the tenant prefix. This allows the application to handle tenant-prefixed URLs without modifying the existing routes.

A Flask `before_request` middleware then checks if the tenant database exists and redirects to a welcome page if it doesn't.

### Database Connection

The MongoDB connection is managed by the `MongoDB` class in `utils/mongo_db.py`. The class is a singleton that maintains a connection to the MongoDB server and switches databases based on the tenant context.

The MongoDB class tracks the current tenant and actively checks if the database needs to be switched when the tenant changes. This ensures that the correct database is used for each tenant, even when navigating between different tenant URLs.

The MongoDB class is designed to work both inside and outside of a Flask application context. This allows it to be initialized during application startup and used in background tasks or scripts.

The database switching logic includes:
- Checking the tenant from the request environment (set by the middleware)
- Comparing the current database with the tenant database
- Switching the database if needed
- Updating the tenant context in the session and application context

### URL Generation

The application uses a custom URL generation function (`tenant_url_for`) to ensure that all URLs include the tenant prefix. This function is available in templates and controllers.

### Template Context

The tenant information is made available to all templates through a context processor. Templates can access the tenant name and prefix using the `tenant` and `tenant_prefix` variables.

### Session Management

The tenant context is stored in the Flask session and application context (`g`) for use throughout the request lifecycle.

## Testing

To test the multi-tenant functionality, you can create a test tenant database using the `scripts/create_test_tenant.py` script:

```bash
python scripts/create_test_tenant.py test_tenant
```

This will create a new MongoDB database named `test_tenant_custdb` with the required collections and a default admin user.

You can then access the application using the tenant URL:

```
http://localhost:5001/test_tenant/login
```

To see examples of tenant-specific URLs for testing, you can use the `scripts/test_tenant_urls.py` script:

```bash
python scripts/test_tenant_urls.py test_tenant
```

This will print out examples of tenant-specific URLs for testing.

To test the database switching functionality, you can use the `scripts/test_tenant_db_switching.py` script:

```bash
python scripts/test_tenant_db_switching.py
```

This script simulates multiple requests to different tenant URLs and verifies that the database switching works correctly.

To test MongoDB initialization outside of an application context, you can use the `scripts/test_mongodb_init.py` script:

```bash
python scripts/test_mongodb_init.py
```

This script verifies that the MongoDB class can be initialized without an application context, which is important for background tasks and scripts.

To test the URL rewriting functionality, you can use the `scripts/test_tenant_url_rewriting.py` script:

```bash
python scripts/test_tenant_url_rewriting.py
```

This script simulates requests to tenant-prefixed URLs and verifies that the URL rewriting works correctly.

To test database switching between different tenant URLs, you can use the `scripts/test_tenant_db_switching_between_urls.py` script:

```bash
python scripts/test_tenant_db_switching_between_urls.py
```

This script simulates multiple requests to different tenant URLs and verifies that the database is switched correctly when navigating between tenants.

## Default Behavior

If no tenant is specified in the URL or the tenant database doesn't exist, the application will use the default database (`telegram_bot`) or redirect to a welcome page.

## Security Considerations

- Each tenant's data is isolated in a separate MongoDB database
- Tenant context is maintained in the session to prevent cross-tenant access
- Database connections are dynamically switched based on the tenant context
- The application validates tenant names to prevent injection attacks
