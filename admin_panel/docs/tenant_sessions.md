# Tenant-Specific Sessions

This document describes the implementation of tenant-specific sessions in the admin panel application.

## Overview

The admin panel application supports multiple tenants, each with their own database and session. This allows users to be logged in to multiple tenants simultaneously in the same browser.

## Implementation

### Session Cookie Names

Each tenant has its own session cookie name, which is derived from the tenant name:

```
session_<tenant_name>
```

For example, if the tenant name is `customer1`, the session cookie name will be `session_customer1`.

### Custom Session Interface

A custom session interface (`TenantSessionInterface`) is used to modify the session cookie name based on the tenant. This class extends Flask's `SecureCookieSessionInterface` and overrides the `get_cookie_name` method to return a tenant-specific cookie name.

```python
class TenantSessionInterface(SecureCookieSessionInterface):
    def get_cookie_name(self, app):
        # Get tenant from environ or g
        tenant = request.environ.get('tenant') or getattr(g, 'tenant', None)
        
        # Use tenant-specific session name if tenant is available
        if tenant:
            return f"session_{tenant}"
        
        # Fall back to default session name
        return app.session_cookie_name
```

### Tenant Resolution

The tenant is resolved from the URL path using the `TenantMiddleware` class. This middleware extracts the tenant name from the URL path and stores it in the request environ.

The `resolve_tenant` middleware then retrieves the tenant name from the request environ and stores it in the session and `g` object.

### Session Management

When a user logs in, the session data is stored in a tenant-specific session cookie. This allows the user to be logged in to multiple tenants simultaneously in the same browser.

When a user logs out, only the session data for the current tenant is cleared. The session data for other tenants remains intact.

## Testing

A test script (`scripts/test_tenant_sessions.py`) is provided to verify the tenant-specific session functionality. This script creates a simple Flask app with tenant-specific sessions and provides routes to test session isolation between tenants.

To run the test script:

```bash
cd admin_panel
python scripts/test_tenant_sessions.py
```

Then open the following URLs in your browser:

- http://localhost:5000/ - Default route
- http://localhost:5000/tenant1/ - Tenant 1 route
- http://localhost:5000/tenant2/ - Tenant 2 route
- http://localhost:5000/tenant1/login - Login for Tenant 1
- http://localhost:5000/tenant2/login - Login for Tenant 2
- http://localhost:5000/tenant1/session - Session info for Tenant 1
- http://localhost:5000/tenant2/session - Session info for Tenant 2

You should be able to log in to both tenants simultaneously and see that the session data is isolated between tenants.

## Debugging

To debug session issues, enable DEBUG level logging and check the logs for messages related to tenant resolution and session management.

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

Look for log messages from the following modules:

- `utils.tenant_session`
- `utils.tenant_middleware`
- `app` (resolve_tenant middleware)
