# Admin Tools Script

This document describes how to use the `admin_tools.py` script to manage admin users for customer databases.

## Overview

The `admin_tools.py` script provides various admin management functions:
- Reset admin password
- Create new admin user
- List admin users
- Delete admin user
- List customer databases

## Usage

```bash
python admin_tools.py [command] [options]
```

### Commands

#### Reset Admin Password

Reset the password for an admin user in a customer database.

```bash
python admin_tools.py reset-password --password <new_password> --cn <customer_name> [--username <admin_username>]
```

Arguments:
- `--password`: The new admin password (required)
- `--cn`: The customer name without the `_custdb` suffix (required)
- `--username`: The admin username (default: admin)

Example:
```bash
python admin_tools.py reset-password --password NewPassword123 --cn newcustomer
```

#### Create Admin User

Create a new admin user in a customer database.

```bash
python admin_tools.py create-admin --username <admin_username> --password <admin_password> --cn <customer_name>
```

Arguments:
- `--username`: The admin username (required)
- `--password`: The admin password (required)
- `--cn`: The customer name without the `_custdb` suffix (required)

Example:
```bash
python admin_tools.py create-admin --username superadmin --password SuperAdmin123 --cn newcustomer
```

#### List Admin Users

List all admin users in a customer database.

```bash
python admin_tools.py list-admins --cn <customer_name>
```

Arguments:
- `--cn`: The customer name without the `_custdb` suffix (required)

Example:
```bash
python admin_tools.py list-admins --cn newcustomer
```

#### Delete Admin User

Delete an admin user from a customer database.

```bash
python admin_tools.py delete-admin --username <admin_username> --cn <customer_name>
```

Arguments:
- `--username`: The admin username (required)
- `--cn`: The customer name without the `_custdb` suffix (required)

Example:
```bash
python admin_tools.py delete-admin --username superadmin --cn newcustomer
```

#### List Customer Databases

List all customer databases.

```bash
python admin_tools.py list-databases
```

## How It Works

The script connects to the MongoDB server and performs the requested operation on the specified customer database. It validates inputs, checks for the existence of databases and collections, and provides detailed logging of operations.

## Requirements

- Python 3.6 or higher
- pymongo
- werkzeug

## Security Considerations

- The script should be run by authorized personnel only
- The script should be run in a secure environment
- Passwords should be strong and meet your organization's password policy
- The script logs password hashes for verification purposes, but not plaintext passwords

## Troubleshooting

If the script fails, check the following:

- Ensure the MongoDB server is running and accessible
- Ensure the customer database exists
- Ensure the `admins` collection exists in the customer database
- Ensure the admin user exists in the `admins` collection (for operations that require an existing user)
- Check the logs for specific error messages
