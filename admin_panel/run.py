import logging
from utils.logging_setup import setup_logging

# Initialize logging first
setup_logging()
logger = logging.getLogger(__name__)

# Import app after logging is configured
from app import app
from utils.db_init import init_db

if __name__ == '__main__':
    logger.info("Initializing database...")
    init_db()
    logger.info("Starting Flask application...")
    app.run(debug=app.config['DEBUG'], host='0.0.0.0', port=5001)