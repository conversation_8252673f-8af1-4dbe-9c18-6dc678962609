// Toast notification system
function showToast(type, title, message, duration = 3000) {
    // Create toast container if it doesn't exist
    let toastContainer = document.getElementById('toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'fixed top-4 right-4 z-50 flex flex-col space-y-2';
        document.body.appendChild(toastContainer);
    }

    // Create toast element
    const toast = document.createElement('div');
    toast.className = 'bg-white rounded-lg shadow-md border border-gray-100 p-4 flex items-center max-w-md transform transition-all duration-300 translate-x-full';

    // Set icon based on type
    let iconClass = '';
    let iconColor = '';
    let bgColor = '';

    if (type === 'success') {
        iconClass = 'fas fa-check';
        iconColor = 'text-green-500';
        bgColor = 'bg-green-100';
    } else if (type === 'error') {
        iconClass = 'fas fa-exclamation';
        iconColor = 'text-red-500';
        bgColor = 'bg-red-100';
    } else if (type === 'info') {
        iconClass = 'fas fa-info';
        iconColor = 'text-indigo-500';
        bgColor = 'bg-indigo-100';
    } else if (type === 'warning') {
        iconClass = 'fas fa-exclamation-triangle';
        iconColor = 'text-yellow-500';
        bgColor = 'bg-yellow-100';
    }

    // Create toast content
    toast.innerHTML = `
        <div class="flex-shrink-0 w-8 h-8 ${bgColor} ${iconColor} rounded-full flex items-center justify-center mr-3">
            <i class="${iconClass}"></i>
        </div>
        <div class="flex-1">
            <h4 class="font-medium text-gray-900 text-sm">${title}</h4>
            <p class="text-gray-600 text-xs mt-1">${message}</p>
        </div>
        <button class="ml-4 text-gray-400 hover:text-gray-600 transition-colors duration-200">
            <i class="fas fa-times"></i>
        </button>
    `;

    // Add toast to container
    toastContainer.appendChild(toast);

    // Add close button functionality
    const closeButton = toast.querySelector('button');
    closeButton.addEventListener('click', () => {
        hideToast(toast);
    });

    // Show toast with animation
    setTimeout(() => {
        toast.classList.remove('translate-x-full');
        toast.classList.add('translate-x-0');
    }, 10);

    // Auto hide after duration
    setTimeout(() => {
        hideToast(toast);
    }, duration);

    return toast;
}

function hideToast(toast) {
    toast.classList.remove('translate-x-0');
    toast.classList.add('translate-x-full');

    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 300);
}

// Wait for the DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    // Auto-dismiss flash messages after 5 seconds
    const flashMessages = document.querySelectorAll('.flash-message');
    flashMessages.forEach(function(message) {
        setTimeout(function() {
            message.style.opacity = '0';
            setTimeout(function() {
                message.remove();
            }, 300);
        }, 5000);
    });

    // User management code has been removed

    // Sidebar Toggle Functionality
    // Sidebar Toggle
    document.addEventListener('DOMContentLoaded', function() {
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('mainContent');
        const toggleBtn = document.getElementById('toggleSidebar');
        const toggleIcon = toggleBtn.querySelector('i');
        const sidebarTexts = document.querySelectorAll('.sidebar-text');

        let isExpanded = true;

        function toggleSidebar() {
            if (isExpanded) {
                // Collapse
                sidebar.classList.remove('w-64');
                sidebar.classList.add('w-16');
                mainContent.classList.remove('ml-64');
                mainContent.classList.add('ml-16');
                toggleIcon.classList.remove('fa-chevron-left');
                toggleIcon.classList.add('fa-chevron-right');
                sidebarTexts.forEach(text => {
                    text.classList.add('opacity-0');
                    setTimeout(() => text.classList.add('hidden'), 300);
                });
            } else {
                // Expand
                sidebar.classList.add('w-64');
                sidebar.classList.remove('w-16');
                mainContent.classList.add('ml-64');
                mainContent.classList.remove('ml-16');
                toggleIcon.classList.add('fa-chevron-left');
                toggleIcon.classList.remove('fa-chevron-right');
                sidebarTexts.forEach(text => {
                    text.classList.remove('hidden');
                    setTimeout(() => text.classList.remove('opacity-0'), 50);
                });
            }
            isExpanded = !isExpanded;
        }

        toggleBtn.addEventListener('click', toggleSidebar);
    });

    // Access Codes Table Functionality
    if (document.querySelector('.code-checkbox')) {
        // Select All Functionality
        document.getElementById('selectAll').addEventListener('change', function() {
            document.querySelectorAll('.code-checkbox').forEach(checkbox => {
                checkbox.checked = this.checked;
            });
            updateDeleteButton();
        });

        // Delete Selected Codes
        document.getElementById('deleteSelected').addEventListener('click', async function() {
            const selectedCodes = Array.from(document.querySelectorAll('.code-checkbox:checked'))
                .map(checkbox => checkbox.value);

            if (!selectedCodes.length) return;

            if (confirm('Are you sure you want to delete the selected codes?')) {
                try {
                    const response = await fetch('/dashboard/bulk-delete-access-codes', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ codes: selectedCodes }),
                    });

                    if (response.ok) {
                        window.location.reload();
                    }
                } catch (error) {
                    console.error('Error deleting codes:', error);
                }
            }
        });

        // Search Functionality
        document.getElementById('searchInput').addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            document.querySelectorAll('tbody tr').forEach(row => {
                const code = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
                row.style.display = code.includes(searchTerm) ? '' : 'none';
            });
            updatePagination();
        });

        // Entries Per Page
        document.getElementById('entriesPerPage').addEventListener('change', function() {
            currentPage = 1;
            updatePagination();
        });
    }

    function updateDeleteButton() {
        const selectedCount = document.querySelectorAll('.code-checkbox:checked').length;
        const deleteButton = document.getElementById('deleteSelected');
        deleteButton.disabled = selectedCount === 0;
    }

    function updatePagination() {
        // Pagination logic here
        // ... implementation details ...
    }
});