document.addEventListener('DOMContentLoaded', function() {
    // Get sidebar element
    const sidebar = document.getElementById('sidebar');
    if (!sidebar) return;

    // Initialize dropdown menus
    initializeDropdowns();

    // Add collapse button at the bottom of sidebar
    const collapseButton = document.createElement('div');
    collapseButton.id = 'collapseSidebar';
    collapseButton.className = 'p-4 flex items-center cursor-pointer hover:bg-gray-700 transition-colors flex-shrink-0 border-t border-gray-700 border-opacity-50 hidden md:flex';

    // Set the correct initial button state based on HTML class
    const isCollapsed = document.documentElement.classList.contains('sidebar-collapsed');
    if (isCollapsed) {
        collapseButton.setAttribute('data-tooltip', 'Expand sidebar');
        collapseButton.innerHTML = `
            <i class="fas fa-chevron-right mr-2"></i>
            <span class="sidebar-text">Expand sidebar</span>
        `;

        // Adjust icon positioning to center
        const iconContainers = document.querySelectorAll('#sidebar i');
        iconContainers.forEach(iconEl => {
            if (iconEl.parentElement) {
                iconEl.parentElement.classList.add('justify-center');
            }
            iconEl.classList.remove('w-6');
            iconEl.classList.add('mx-auto');
        });
    } else {
        collapseButton.setAttribute('data-tooltip', 'Collapse sidebar');
        collapseButton.innerHTML = `
            <i class="fas fa-chevron-left mr-2"></i>
            <span class="sidebar-text">Collapse sidebar</span>
        `;
    }

    // Add the button to the bottom of sidebar
    sidebar.appendChild(collapseButton);

    // Toggle sidebar function
    function toggleSidebar() {
        // Don't toggle sidebar on mobile devices
        if (window.innerWidth <= 768) {
            return;
        }

        const isCurrentlyCollapsed = document.documentElement.classList.contains('sidebar-collapsed');
        const collapseIcon = collapseButton.querySelector('i');
        const collapseText = collapseButton.querySelector('span');

        if (!isCurrentlyCollapsed) {
            // Collapse sidebar
            document.documentElement.style.setProperty('--sidebar-width', '3.5rem');
            document.documentElement.style.setProperty('--main-margin', '3.5rem');
            document.documentElement.classList.add('sidebar-collapsed');

            // Adjust icon positioning to center
            const iconContainers = document.querySelectorAll('#sidebar i:not(#requestsDropdownIcon)');
            iconContainers.forEach(iconEl => {
                if (iconEl.parentElement) {
                    iconEl.parentElement.classList.add('justify-center');
                }
                iconEl.classList.remove('w-6');
                iconEl.classList.add('mx-auto');
            });

            // Hide dropdown icon when sidebar is collapsed
            const dropdownIcon = document.getElementById('requestsDropdownIcon');
            if (dropdownIcon) {
                dropdownIcon.classList.add('hidden');
            }

            // Ensure the requests button is properly centered
            const requestsButton = document.getElementById('requestsDropdownButton');
            const requestsMenu = document.getElementById('requestsDropdownMenu');
            if (requestsButton) {
                const buttonContainer = requestsButton.querySelector('.flex-1');
                if (buttonContainer) {
                    buttonContainer.classList.add('justify-center');
                }

                // Setup popup menu for collapsed state
                if (requestsMenu) {
                    const popupMenu = requestsMenu.querySelector('.sidebar-popup-menu');
                    const expandedMenu = requestsMenu.querySelector('.sidebar-expanded-menu');

                    if (popupMenu && expandedMenu) {
                        popupMenu.classList.remove('hidden');
                        expandedMenu.classList.add('hidden');
                    }

                    // Check if any submenu item is active
                    const verificationLink = document.querySelector('a[href*="verification-requests"]');
                    const supportLink = document.querySelector('a[href*="support-requests"]');
                    const isSubmenuActive =
                        (verificationLink && verificationLink.classList.contains('active')) ||
                        (supportLink && supportLink.classList.contains('active'));

                    // Check dropdown state
                    const dropdownState = localStorage.getItem('requestsDropdownState');

                    if (isSubmenuActive) {
                        // Always highlight the Requests button in collapsed state when a submenu is active
                        requestsButton.classList.add('active', 'bg-blue-500', 'bg-opacity-20', 'border-l-blue-500', 'text-white');

                        // Keep the dropdown open or closed based on user preference
                        if (dropdownState === 'closed') {
                            requestsMenu.style.maxHeight = '0px';
                        } else {
                            requestsMenu.style.maxHeight = requestsMenu.scrollHeight + 'px';
                        }
                    } else {
                        // Close the dropdown menu if no submenu item is active
                        requestsMenu.style.maxHeight = '0px';
                    }
                }
            }

            collapseIcon.classList.remove('fa-chevron-left');
            collapseIcon.classList.add('fa-chevron-right');
            collapseText.textContent = 'Expand sidebar';
            collapseButton.setAttribute('data-tooltip', 'Expand sidebar');

            // Save collapsed state to localStorage
            localStorage.setItem('sidebarCollapsed', 'true');
        } else {
            // Expand sidebar
            document.documentElement.style.setProperty('--sidebar-width', '16rem');
            document.documentElement.style.setProperty('--main-margin', '16rem');
            document.documentElement.classList.remove('sidebar-collapsed');

            // Restore icon positioning
            const iconContainers = document.querySelectorAll('#sidebar i');
            iconContainers.forEach(iconEl => {
                if (iconEl.parentElement) {
                    iconEl.parentElement.classList.remove('justify-center');
                }
                iconEl.classList.add('w-6');
                iconEl.classList.remove('mx-auto');
            });

            // Show dropdown icon when sidebar is expanded
            const dropdownIcon = document.getElementById('requestsDropdownIcon');
            if (dropdownIcon) {
                dropdownIcon.classList.remove('hidden');
            }

            // Restore the requests button alignment
            const requestsButton = document.getElementById('requestsDropdownButton');
            const requestsMenu = document.getElementById('requestsDropdownMenu');
            if (requestsButton) {
                const buttonContainer = requestsButton.querySelector('.flex-1');
                if (buttonContainer) {
                    buttonContainer.classList.remove('justify-center');
                }

                // Check if any submenu item is active
                const verificationLink = document.querySelector('a[href*="verification-requests"]');
                const supportLink = document.querySelector('a[href*="support-requests"]');
                const isSubmenuActive =
                    (verificationLink && verificationLink.classList.contains('active')) ||
                    (supportLink && supportLink.classList.contains('active'));

                if (isSubmenuActive) {
                    // When expanding sidebar with an active submenu, always open the dropdown
                    // and remove active class from the button
                    requestsMenu.style.maxHeight = requestsMenu.scrollHeight + 'px';
                    requestsButton.classList.remove('active', 'bg-blue-500', 'bg-opacity-20', 'border-l-blue-500', 'text-white');

                    // Update localStorage to reflect that the dropdown is open
                    localStorage.setItem('requestsDropdownState', 'open');

                    // Rotate the dropdown icon
                    const requestsIcon = document.getElementById('requestsDropdownIcon');
                    if (requestsIcon) {
                        requestsIcon.classList.add('transform', 'rotate-180');
                    }
                }

                // Restore expanded menu for expanded state
                if (requestsMenu) {
                    const popupMenu = requestsMenu.querySelector('.sidebar-popup-menu');
                    const expandedMenu = requestsMenu.querySelector('.sidebar-expanded-menu');

                    if (popupMenu && expandedMenu) {
                        popupMenu.classList.add('hidden');
                        expandedMenu.classList.remove('hidden');
                    }
                }
            }

            collapseIcon.classList.add('fa-chevron-left');
            collapseIcon.classList.remove('fa-chevron-right');
            collapseText.textContent = 'Collapse sidebar';
            collapseButton.setAttribute('data-tooltip', 'Collapse sidebar');

            // Save expanded state to localStorage
            localStorage.setItem('sidebarCollapsed', 'false');
        }
    }

    // Add event listener to the collapse button
    collapseButton.addEventListener('click', toggleSidebar);

    // Handle window resize events for sidebar state
    window.addEventListener('resize', function() {
        // If transitioning to mobile view, ensure sidebar is expanded
        if (window.innerWidth <= 768 && document.documentElement.classList.contains('sidebar-collapsed')) {
            // Expand sidebar for mobile without saving to localStorage
            document.documentElement.style.setProperty('--sidebar-width', '16rem');
            document.documentElement.style.setProperty('--main-margin', '16rem');
            document.documentElement.classList.remove('sidebar-collapsed');

            // Restore icon positioning
            const iconContainers = document.querySelectorAll('#sidebar i');
            iconContainers.forEach(iconEl => {
                if (iconEl.parentElement) {
                    iconEl.parentElement.classList.remove('justify-center');
                }
                iconEl.classList.add('w-6');
                iconEl.classList.remove('mx-auto');
            });

            // Show dropdown icon
            const dropdownIcon = document.getElementById('requestsDropdownIcon');
            if (dropdownIcon) {
                dropdownIcon.classList.remove('hidden');
            }
        }
        // If transitioning to desktop view, restore user's preference
        else if (window.innerWidth > 768) {
            const sidebarState = localStorage.getItem('sidebarCollapsed');
            if (sidebarState === 'true' && !document.documentElement.classList.contains('sidebar-collapsed')) {
                // Apply collapsed styles
                document.documentElement.style.setProperty('--sidebar-width', '3.5rem');
                document.documentElement.style.setProperty('--main-margin', '3.5rem');
                document.documentElement.classList.add('sidebar-collapsed');
            }
        }
    });

    // Function to initialize dropdown menus
    function initializeDropdowns() {
        // Requests dropdown functionality
        const requestsButton = document.getElementById('requestsDropdownButton');
        const requestsMenu = document.getElementById('requestsDropdownMenu');
        const requestsIcon = document.getElementById('requestsDropdownIcon');

        if (requestsButton && requestsMenu && requestsIcon) {
            // Check if any submenu item is active
            const verificationLink = document.querySelector('a[href*="verification-requests"]');
            const supportLink = document.querySelector('a[href*="support-requests"]');
            const isSubmenuActive =
                (verificationLink && verificationLink.classList.contains('active')) ||
                (supportLink && supportLink.classList.contains('active'));

            // Handle active submenu items
            if (isSubmenuActive) {
                // Check if sidebar is expanded or collapsed
                const isSidebarCollapsed = document.documentElement.classList.contains('sidebar-collapsed');

                if (isSidebarCollapsed) {
                    // In collapsed state, check dropdown state
                    const dropdownState = localStorage.getItem('requestsDropdownState');

                    // Always highlight the button in collapsed state
                    requestsButton.classList.add('active', 'bg-blue-500', 'bg-opacity-20', 'border-l-blue-500', 'text-white');

                    // Open or close dropdown based on saved state
                    if (dropdownState === 'closed') {
                        requestsMenu.style.maxHeight = '0px';
                        requestsIcon.classList.remove('transform', 'rotate-180');
                    } else {
                        requestsMenu.style.maxHeight = requestsMenu.scrollHeight + 'px';
                        requestsIcon.classList.add('transform', 'rotate-180');
                    }
                } else {
                    // In expanded state, always open the dropdown and don't highlight the button
                    requestsMenu.style.maxHeight = requestsMenu.scrollHeight + 'px';
                    requestsIcon.classList.add('transform', 'rotate-180');
                    requestsButton.classList.remove('active', 'bg-blue-500', 'bg-opacity-20', 'border-l-blue-500', 'text-white');

                    // Update localStorage to reflect that the dropdown is open
                    localStorage.setItem('requestsDropdownState', 'open');
                }
            }

            // Check if sidebar is collapsed initially
            const isSidebarCollapsed = document.documentElement.classList.contains('sidebar-collapsed');
            if (isSidebarCollapsed) {
                requestsIcon.classList.add('hidden');

                // Only reset dropdown menu when sidebar is collapsed and no submenu item is active
                if (!isSubmenuActive) {
                    requestsMenu.style.maxHeight = '0px';
                }

                // Make sure popup menu is ready
                const popupMenu = requestsMenu.querySelector('.sidebar-popup-menu');
                const expandedMenu = requestsMenu.querySelector('.sidebar-expanded-menu');

                if (popupMenu && expandedMenu) {
                    popupMenu.classList.remove('hidden');
                    expandedMenu.classList.add('hidden');
                }
            }

            // Variables for hover timing
            let hoverTimeout;
            let isHovering = false;

            // Show popup menu on hover when sidebar is collapsed
            requestsButton.addEventListener('mouseenter', function() {
                if (document.documentElement.classList.contains('sidebar-collapsed')) {
                    isHovering = true;
                    clearTimeout(hoverTimeout);

                    // Position the popup menu relative to the button
                    const buttonRect = requestsButton.getBoundingClientRect();
                    requestsMenu.style.top = buttonRect.top + 'px';

                    requestsMenu.classList.add('popup-visible');
                }
            });

            // Handle mouse leaving the button
            requestsButton.addEventListener('mouseleave', function() {
                if (document.documentElement.classList.contains('sidebar-collapsed')) {
                    isHovering = false;
                    // Delay hiding the menu to give time to move to it
                    hoverTimeout = setTimeout(() => {
                        if (!isHovering) {
                            requestsMenu.classList.remove('popup-visible');
                        }
                    }, 300); // 300ms delay
                }
            });

            // Handle mouse entering the menu
            requestsMenu.addEventListener('mouseenter', function() {
                if (document.documentElement.classList.contains('sidebar-collapsed')) {
                    isHovering = true;
                    clearTimeout(hoverTimeout);
                }
            });

            // Handle mouse leaving the menu
            requestsMenu.addEventListener('mouseleave', function() {
                if (document.documentElement.classList.contains('sidebar-collapsed')) {
                    isHovering = false;
                    // Hide menu after a short delay
                    hoverTimeout = setTimeout(() => {
                        if (!isHovering) {
                            requestsMenu.classList.remove('popup-visible');
                        }
                    }, 300); // 300ms delay
                }
            });

            // Toggle dropdown on click
            requestsButton.addEventListener('click', function(e) {
                e.preventDefault(); // Prevent default button behavior

                // Don't toggle dropdown if sidebar is collapsed
                if (document.documentElement.classList.contains('sidebar-collapsed')) {
                    return;
                }

                // Check if any submenu item is active
                const verificationLink = document.querySelector('a[href*="verification-requests"]');
                const supportLink = document.querySelector('a[href*="support-requests"]');
                const isSubmenuActive =
                    (verificationLink && verificationLink.classList.contains('active')) ||
                    (supportLink && supportLink.classList.contains('active'));

                if (requestsMenu.style.maxHeight === '0px' || requestsMenu.style.maxHeight === '') {
                    // Open the dropdown
                    requestsMenu.style.maxHeight = requestsMenu.scrollHeight + 'px';
                    requestsIcon.classList.add('transform', 'rotate-180');

                    // Save the open state
                    localStorage.setItem('requestsDropdownState', 'open');

                    // Remove active styling from the button if a submenu is active
                    if (isSubmenuActive) {
                        requestsButton.classList.remove('active', 'bg-blue-500', 'bg-opacity-20', 'border-l-blue-500', 'text-white');
                    }
                } else {
                    // Allow closing the dropdown even when a submenu item is active
                    requestsMenu.style.maxHeight = '0px';
                    requestsIcon.classList.remove('transform', 'rotate-180');

                    // Save the closed state
                    localStorage.setItem('requestsDropdownState', 'closed');

                    // Add active styling to the button if a submenu is active and we're closing the dropdown
                    if (isSubmenuActive) {
                        requestsButton.classList.add('active', 'bg-blue-500', 'bg-opacity-20', 'border-l-blue-500', 'text-white');
                    }
                }
            });
        }

        // Add more dropdown initializations here as needed
    }
});