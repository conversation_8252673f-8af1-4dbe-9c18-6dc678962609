// Token visibility toggle
document.addEventListener('DOMContentLoaded', function() {
    const toggleButtons = document.querySelectorAll('.toggle-token');
    toggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            const tokenDisplay = this.closest('.token-display');
            const tokenMask = tokenDisplay.querySelector('.token-mask');
            const token = tokenDisplay.dataset.token;
            
            if (tokenMask.textContent.includes('•')) {
                tokenMask.textContent = token;
                this.innerHTML = '<i class="fas fa-eye-slash"></i>';
            } else {
                tokenMask.textContent = '••••••••:••••••••••••••••••••••••••••••';
                this.innerHTML = '<i class="fas fa-eye"></i>';
            }
        });
    });
});

// Modal functions
function openAddBotModal() {
    document.getElementById('addBotModal').classList.remove('hidden');
}

function closeAddBotModal() {
    document.getElementById('addBotModal').classList.add('hidden');
}

function openEditBotModal(id, name, username, token, isActive) {
    document.getElementById('edit_name').value = name;
    document.getElementById('edit_username').value = username;
    document.getElementById('edit_token').value = token;
    document.getElementById('edit_is_active').checked = isActive;
    
    const form = document.getElementById('editBotForm');
    const baseUrl = form.getAttribute('data-base-url');
    form.action = baseUrl + id;
    
    document.getElementById('editBotModal').classList.remove('hidden');
}

function closeEditBotModal() {
    document.getElementById('editBotModal').classList.add('hidden');
}

function confirmDeleteBot(id, name) {
    document.getElementById('deleteBotName').textContent = name;
    document.getElementById('confirmDeleteBtn').onclick = function() {
        deleteBot(id);
    };
    document.getElementById('deleteBotModal').classList.remove('hidden');
}

function closeDeleteBotModal() {
    document.getElementById('deleteBotModal').classList.add('hidden');
}

function deleteBot(id) {
    const deleteUrl = document.getElementById('confirmDeleteBtn').getAttribute('data-base-url') + id;
    
    fetch(deleteUrl, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.location.reload();
        } else {
            alert('Error: ' + data.message);
            closeDeleteBotModal();
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while deleting the bot');
        closeDeleteBotModal();
    });
}

function testBot(id) {
    const testUrl = document.getElementById('testBotModal').getAttribute('data-test-url') + id;
    
    document.getElementById('testResultContent').innerHTML = `
        <div class="flex justify-center">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        </div>
        <p class="text-center mt-2">Testing bot connection...</p>
    `;
    document.getElementById('testBotModal').classList.remove('hidden');
    
    fetch(testUrl, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('testResultContent').innerHTML = `
                <div class="text-center">
                    <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
                        <i class="fas fa-check text-green-600 text-xl"></i>
                    </div>
                    <h4 class="text-lg font-medium text-gray-900 mb-2">Connection Successful</h4>
                    <div class="bg-gray-50 p-3 rounded-md text-left">
                        <p class="text-sm"><strong>Bot ID:</strong> ${data.bot_info.id}</p>
                        <p class="text-sm"><strong>Username:</strong> @${data.bot_info.username}</p>
                        <p class="text-sm"><strong>Name:</strong> ${data.bot_info.first_name}</p>
                    </div>
                </div>
            `;
        } else {
            document.getElementById('testResultContent').innerHTML = `
                <div class="text-center">
                    <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                        <i class="fas fa-times text-red-600 text-xl"></i>
                    </div>
                    <h4 class="text-lg font-medium text-gray-900 mb-2">Connection Failed</h4>
                    <p class="text-sm text-red-500">${data.message}</p>
                </div>
            `;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        document.getElementById('testResultContent').innerHTML = `
            <div class="text-center">
                <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                    <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                </div>
                <h4 class="text-lg font-medium text-gray-900 mb-2">Error</h4>
                <p class="text-sm text-red-500">An error occurred while testing the bot</p>
            </div>
        `;
    });
}

function closeTestBotModal() {
    document.getElementById('testBotModal').classList.add('hidden');
}