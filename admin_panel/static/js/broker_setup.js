// broker_setup.js - JavaScript for the broker setup page

document.addEventListener('DOMContentLoaded', function() {
    // Add pulse animation for status indicators
    const style = document.createElement('style');
    style.textContent = `
        @keyframes pulse {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.1); opacity: 0.8; }
            100% { transform: scale(1); opacity: 1; }
        }
        .pulse-animation {
            animation: pulse 2s infinite;
        }
        .grainy-effect {
            background-image: url('data:image/png;base64,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');
        }
    `;
    document.head.appendChild(style);

    // Mode Toggle
    const autoModeBtn = document.getElementById('autoModeBtn');
    const manualModeBtn = document.getElementById('manualModeBtn');
    const modeDescription = document.getElementById('modeDescription');
    let currentMode = 'auto'; // Default mode

    autoModeBtn.addEventListener('click', function() {
        if (currentMode !== 'auto') {
            setMode('auto');
        }
    });

    manualModeBtn.addEventListener('click', function() {
        if (currentMode !== 'manual') {
            setMode('manual');
        }
    });

    function setMode(mode) {
        // Check if mode is already set to avoid unnecessary updates
        if (currentMode === mode) {
            console.log('Mode already set to', mode);
            return;
        }

        // Update current mode
        currentMode = mode;

        // Update UI
        setModeUI(mode);

        // Get token validation status
        const jwtStatus = document.getElementById('jwtStatus');
        const isValid = jwtStatus.innerHTML.includes('Active');

        // Update connection status indicator based on mode and token validity
        updateConnectionStatus(mode, isValid);

        // Update mode in database
        updateModeInDatabase(mode);
    }

    function updateModeInDatabase(mode) {
        console.log('Updating mode in database:', mode);

        // Get the API URL with the correct format based on the URL
        let apiUrl;
        const host = window.location.host;
        const isSubdomain = host.split('.').length > 1 && !host.startsWith('localhost');

        if (isSubdomain) {
            // In production mode with subdomain routing, use /api/broker/update-mode
            apiUrl = '/api/broker/update-mode';
        } else {
            // In test mode or fallback, use tenant prefix
            apiUrl = window.location.pathname.split('/').slice(0, 2).join('/') + '/api/broker/update-mode';
        }

        console.log('API URL:', apiUrl);

        // Call the API to update mode
        fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                mode: mode
            }),
            credentials: 'same-origin'
        })
        .then(response => {
            console.log('Response status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('Response data:', data);
            if (data.success) {
                console.log('Mode updated in database:', mode);
                showToast('Mode updated to ' + mode, 'success');
            } else {
                console.error('Error updating mode:', data.message);
                showToast('Error updating mode: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error updating mode:', error);
            showToast('Error updating mode', 'error');
        });
    }

    // This function is no longer used as we now rely on the token validation status from the server
    // Keeping it commented for reference
    /*
    function updateJwtStatus(mode) {
        const jwtStatus = document.getElementById('jwtStatus');
        const tokenExpiry = document.getElementById('tokenExpiry');

        if (mode === 'auto') {
            jwtStatus.innerHTML = '<i class="fas fa-check-circle mr-1"></i> Active';
            jwtStatus.classList.remove('bg-red-100', 'text-red-800', 'bg-gray-100', 'text-gray-800');
            jwtStatus.classList.add('bg-green-100', 'text-green-800');
            tokenExpiry.textContent = 'Token expires in 30 days';
        } else {
            jwtStatus.innerHTML = '<i class="fas fa-times-circle mr-1"></i> Inactive';
            jwtStatus.classList.remove('bg-green-100', 'text-green-800', 'bg-gray-100', 'text-gray-800');
            jwtStatus.classList.add('bg-red-100', 'text-red-800');
            tokenExpiry.textContent = 'No active token';
        }
    }
    */

    // This function is now replaced by the one defined at the bottom of the file
    // Keeping it commented for reference
    /*
    function updateConnectionStatus(mode) {
        const connectionStatus = document.getElementById('connectionStatus');
        const connectionStatusIndicator = document.getElementById('connectionStatusIndicator');
        const syncStatus = document.getElementById('syncStatus');
        const syncStatusIndicator = document.getElementById('syncStatusIndicator');

        if (mode === 'auto') {
            connectionStatus.textContent = 'Connected';
            connectionStatusIndicator.classList.remove('bg-red-500', 'bg-gray-500');
            connectionStatusIndicator.classList.add('bg-green-500', 'pulse-animation');

            syncStatus.textContent = 'Last sync: 5 min ago';
            syncStatusIndicator.classList.remove('bg-red-500');
            syncStatusIndicator.classList.add('bg-yellow-500');
        } else {
            connectionStatus.textContent = 'Disconnected';
            connectionStatusIndicator.classList.remove('bg-green-500', 'pulse-animation');
            connectionStatusIndicator.classList.add('bg-red-500');

            syncStatus.textContent = 'Sync disabled';
            syncStatusIndicator.classList.remove('bg-yellow-500');
            syncStatusIndicator.classList.add('bg-gray-500');
        }
    }
    */

    // Broker selection change handler
    const brokerSelect = document.getElementById('brokerSelect');
    if (brokerSelect) {
        brokerSelect.addEventListener('change', function() {
            const selectedBroker = this.value;
            console.log('Broker selection changed to:', selectedBroker);

            // Update broker in database
            updateBrokerInDatabase(selectedBroker);
        });
    }

    function updateBrokerInDatabase(broker) {
        console.log('Updating broker in database:', broker);

        // Get the API URL with the correct format based on the URL
        let apiUrl;
        const host = window.location.host;
        const isSubdomain = host.split('.').length > 1 && !host.startsWith('localhost');

        if (isSubdomain) {
            // In production mode with subdomain routing, use /api/broker/update-broker
            apiUrl = '/api/broker/update-broker';
        } else {
            // In test mode or fallback, use tenant prefix
            apiUrl = window.location.pathname.split('/').slice(0, 2).join('/') + '/api/broker/update-broker';
        }

        console.log('API URL:', apiUrl);

        // Call the API to update broker
        fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                broker: broker
            }),
            credentials: 'same-origin'
        })
        .then(response => {
            console.log('Response status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('Response data:', data);
            if (data.success) {
                console.log('Broker updated in database:', broker);
                showToast('Broker updated to ' + broker, 'success');
            } else {
                console.error('Error updating broker:', data.message);
                showToast('Error updating broker: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error updating broker:', error);
            showToast('Error updating broker', 'error');
        });
    }

    // Log tabs functionality
    const logTabs = document.querySelectorAll('#logTabs button');

    logTabs.forEach(tab => {
        tab.addEventListener('click', () => {
            // Hide all tab contents
            document.querySelectorAll('#logTabContent > div').forEach(content => {
                content.classList.add('hidden');
                content.classList.remove('block');
            });

            // Remove active class from all tabs
            logTabs.forEach(t => {
                t.classList.remove('border-indigo-500');
                t.classList.add('border-transparent');
                t.setAttribute('aria-selected', 'false');
            });

            // Show selected tab content
            const target = document.querySelector(tab.dataset.tabsTarget);
            target.classList.remove('hidden');
            target.classList.add('block');

            // Add active class to selected tab
            tab.classList.remove('border-transparent');
            tab.classList.add('border-indigo-500');
            tab.setAttribute('aria-selected', 'true');
        });
    });

    // Refresh logs button
    const refreshLogsBtn = document.getElementById('refreshLogsBtn');
    refreshLogsBtn.addEventListener('click', function() {
        // Add spinning animation
        this.classList.add('animate-spin');

        // Force refresh by clearing lastLogContent
        lastLogContent = '';

        // Refresh logs
        fetchCustomerLogs();

        // Show success message after a short delay
        setTimeout(() => {
            showToast('Logs refreshed successfully', 'success');
        }, 1000);
    });

    // Load logs immediately and start auto-refresh
    // This will run as soon as the script is loaded
    setTimeout(() => {
        fetchCustomerLogs();
        startAutoRefresh();
    }, 500);

    // Customer logs functionality
    const customerLogContent = document.getElementById('customerLogContent');
    const toggleAutoRefreshBtn = document.getElementById('toggleAutoRefreshBtn');
    const logUpdateStatus = document.getElementById('logUpdateStatus');
    const liveIndicator = document.getElementById('liveIndicator');

    // Auto-refresh state
    let autoRefreshEnabled = true;
    let autoRefreshInterval = null;
    let lastLogContent = '';

    // Toggle auto-refresh
    toggleAutoRefreshBtn.addEventListener('click', function() {
        autoRefreshEnabled = !autoRefreshEnabled;

        if (autoRefreshEnabled) {
            // Enable auto-refresh
            this.innerHTML = '<i class="fas fa-pause"></i>';
            logUpdateStatus.textContent = 'Auto-refresh: ON';
            liveIndicator.classList.add('pulse-animation');
            startAutoRefresh();
            showToast('Auto-refresh enabled', 'success');
        } else {
            // Disable auto-refresh
            this.innerHTML = '<i class="fas fa-play"></i>';
            logUpdateStatus.textContent = 'Auto-refresh: OFF';
            liveIndicator.classList.remove('pulse-animation');
            stopAutoRefresh();
            showToast('Auto-refresh disabled', 'info');
        }
    });

    // Start auto-refresh
    function startAutoRefresh() {
        if (autoRefreshInterval) {
            clearInterval(autoRefreshInterval);
        }

        // Refresh logs every 5 seconds
        autoRefreshInterval = setInterval(fetchCustomerLogs, 5000);
    }

    // Stop auto-refresh
    function stopAutoRefresh() {
        if (autoRefreshInterval) {
            clearInterval(autoRefreshInterval);
            autoRefreshInterval = null;
        }
    }

    // Function to fetch customer logs
    function fetchCustomerLogs() {
        // If this is the first load, show loading state
        if (!lastLogContent) {
            customerLogContent.innerHTML = '<div class="flex items-center justify-center h-20 text-gray-400"><i class="fas fa-spinner fa-spin mr-2"></i> Loading logs...</div>';
        }

        // Get the API URL with the correct format based on the URL
        let apiUrl;
        const host = window.location.host;
        const isSubdomain = host.split('.').length > 1 && !host.startsWith('localhost');

        if (isSubdomain) {
            // In production mode with subdomain routing, use /api/logs/customer
            apiUrl = '/api/logs/customer';
        } else {
            // In test mode or fallback, use tenant prefix
            apiUrl = window.location.pathname.split('/').slice(0, 2).join('/') + '/api/logs/customer';
        }

        console.log('Logs API URL:', apiUrl);

        fetch(apiUrl, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
            credentials: 'same-origin'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.log_content) {
                // Check if content has changed
                if (data.log_content !== lastLogContent) {
                    lastLogContent = data.log_content;

                    // Process log content
                    const logLines = data.log_content.split('\n').filter(line => line.trim() !== '');

                    if (logLines.length > 0) {
                        // Create HTML for log lines
                        let logHtml = '';

                        logLines.forEach(line => {
                            // Try to parse log line to highlight different parts
                            let formattedLine = '';

                            // Check if line matches the format [timestamp] SECTION: description
                            const logMatch = line.match(/^\[?(\d{4}-\d{2}-\d{2}[ T]\d{2}:\d{2}:\d{2})\]?\s+([A-Z_]+):\s+(.*)/);

                            if (logMatch) {
                                const timestamp = logMatch[1];
                                const section = logMatch[2];
                                const description = logMatch[3];

                                // Determine section color based on section name
                                let sectionClass = 'text-purple-400 font-medium';

                                if (section.includes('ERROR') || section.includes('CRITICAL')) {
                                    sectionClass = 'text-red-400 font-medium';
                                } else if (section.includes('WARNING')) {
                                    sectionClass = 'text-yellow-400 font-medium';
                                } else if (section.includes('INFO')) {
                                    sectionClass = 'text-green-400 font-medium';
                                } else if (section.includes('DEBUG')) {
                                    sectionClass = 'text-blue-400 font-medium';
                                } else if (section.includes('SYSTEM')) {
                                    sectionClass = 'text-cyan-400 font-medium';
                                } else if (section.includes('WORKFLOW')) {
                                    sectionClass = 'text-indigo-400 font-medium';
                                } else if (section.includes('TOKEN')) {
                                    sectionClass = 'text-pink-400 font-medium';
                                }

                                // Format the line with colors
                                formattedLine = `<span class="text-gray-500">[${timestamp}]</span> <span class="${sectionClass}">${section}:</span> <span class="text-gray-300">${description}</span>`;
                            }
                            // Check if line contains just a timestamp
                            else if (line.match(/^(\d{4}-\d{2}-\d{2}[T ]\d{2}:\d{2}:\d{2})/)) {
                                const timestampMatch = line.match(/^(\d{4}-\d{2}-\d{2}[T ]\d{2}:\d{2}:\d{2})/);
                                const timestamp = timestampMatch[1];
                                const restOfLine = line.substring(timestamp.length);

                                // Check for log level in the rest of the line
                                let levelClass = '';
                                if (restOfLine.includes('ERROR') || restOfLine.includes('CRITICAL')) {
                                    levelClass = 'text-red-400 font-medium';
                                } else if (restOfLine.includes('WARNING')) {
                                    levelClass = 'text-yellow-400 font-medium';
                                } else if (restOfLine.includes('INFO')) {
                                    levelClass = 'text-green-400 font-medium';
                                } else if (restOfLine.includes('DEBUG')) {
                                    levelClass = 'text-blue-400 font-medium';
                                }

                                formattedLine = `<span class="text-gray-500">${timestamp}</span> <span class="${levelClass}">${restOfLine}</span>`;
                            } else if (line.includes('No logs found') || line.includes('does not exist')) {
                                // Special handling for our custom messages
                                formattedLine = `<span class="text-yellow-400 font-medium">${line}</span>`;
                            } else {
                                // If no timestamp found, just display the line
                                formattedLine = line;
                            }

                            logHtml += `<div class="mb-2 leading-relaxed">${formattedLine}</div>`;
                        });

                        customerLogContent.innerHTML = logHtml;

                        // Scroll to bottom
                        const logContainer = customerLogContent.parentElement;
                        logContainer.scrollTop = logContainer.scrollHeight;
                    } else {
                        customerLogContent.innerHTML = '<div class="flex items-center justify-center h-20 text-gray-400"><i class="fas fa-file-alt mr-2"></i> Log file is empty</div>';
                    }
                }
            } else {
                // Error or no content
                customerLogContent.innerHTML = '<div class="flex items-center justify-center h-20 text-gray-400"><i class="fas fa-exclamation-circle mr-2"></i> No log content available</div>';

                if (data.error) {
                    showToast(data.message || 'Error loading logs', 'error');
                }
            }
        })
        .catch(error => {
            console.error('Error fetching logs:', error);
            customerLogContent.innerHTML = '<div class="flex items-center justify-center h-20 text-red-400"><i class="fas fa-exclamation-triangle mr-2"></i> Error loading log content</div>';

            // Only show error toast if this is the first load
            if (!lastLogContent) {
                showToast('Error loading logs', 'error');
            }
        })
        .finally(() => {
            // Remove spinning animation from refresh button if it's active
            refreshLogsBtn.classList.remove('animate-spin');
        });
    }

    // Generate Token Modal
    const generateTokenBtn = document.getElementById('generateTokenBtn');
    // Note: generateSpinner is defined but not used in this version
    // const generateSpinner = document.getElementById('generateSpinner');
    const loginTokenModal = document.getElementById('loginTokenModal');
    const closeModalBtn = document.querySelector('#loginTokenModal button[onclick="closeLoginTokenModal()"]');
    const progressBar = document.getElementById('progressBar');

    generateTokenBtn.addEventListener('click', function() {
        // Reset form
        document.getElementById('username').value = '';
        document.getElementById('password').value = '';
        document.getElementById('usernameError').classList.add('hidden');
        document.getElementById('passwordError').classList.add('hidden');
        document.getElementById('formErrors').classList.add('hidden');
        progressBar.style.width = '0%';

        // Show modal
        loginTokenModal.classList.remove('hidden');
    });

    closeModalBtn.addEventListener('click', function() {
        closeLoginTokenModal();
    });

    // Close modal when clicking outside
    loginTokenModal.addEventListener('click', function(e) {
        if (e.target === loginTokenModal) {
            closeLoginTokenModal();
        }
    });

    // Close modal when pressing Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && !loginTokenModal.classList.contains('hidden')) {
            closeLoginTokenModal();
        }
    });

    // Toggle password visibility
    const togglePassword = document.getElementById('togglePassword');
    const passwordInput = document.getElementById('password');

    togglePassword.addEventListener('click', function() {
        const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
        passwordInput.setAttribute('type', type);

        // Toggle icon
        const icon = this.querySelector('i');
        if (type === 'text') {
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    });

    // Form validation
    const usernameInput = document.getElementById('username');
    const usernameError = document.getElementById('usernameError');
    const passwordError = document.getElementById('passwordError');
    const formErrors = document.getElementById('formErrors');
    const errorMessage = document.getElementById('errorMessage');

    usernameInput.addEventListener('blur', validateUsername);
    passwordInput.addEventListener('blur', validatePassword);

    function validateUsername() {
        const email = usernameInput.value.trim();
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

        if (!email) {
            usernameError.textContent = 'Email is required';
            usernameError.classList.remove('hidden');
            return false;
        } else if (!emailRegex.test(email)) {
            usernameError.textContent = 'Please enter a valid email address';
            usernameError.classList.remove('hidden');
            return false;
        } else {
            usernameError.classList.add('hidden');
            return true;
        }
    }

    function validatePassword() {
        if (!passwordInput.value) {
            passwordError.textContent = 'Password is required';
            passwordError.classList.remove('hidden');
            return false;
        } else if (passwordInput.value.length < 6) {
            passwordError.textContent = 'Password must be at least 6 characters';
            passwordError.classList.remove('hidden');
            return false;
        } else {
            passwordError.classList.add('hidden');
            return true;
        }
    }

    // Authenticate Button
    const authenticateBtn = document.getElementById('authenticateBtn');
    const authSpinner = document.getElementById('authSpinner');

    authenticateBtn.addEventListener('click', function() {
        // Validate form
        const isUsernameValid = validateUsername();
        const isPasswordValid = validatePassword();

        if (!isUsernameValid || !isPasswordValid) {
            formErrors.classList.remove('hidden');
            errorMessage.textContent = 'Please correct the errors below.';
            return;
        }

        // Show loading spinner
        authSpinner.classList.remove('hidden');

        // Animate progress bar
        progressBar.style.transition = 'width 1s ease-in-out';
        progressBar.style.width = '100%';

        // Get form values
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;

        // Get broker selection
        const brokerSelect = document.getElementById('brokerSelect');
        const selectedBroker = brokerSelect.value;

        // Check if development mode is enabled
        const devModeToggle = document.getElementById('devModeToggle');
        const devMode = devModeToggle && devModeToggle.checked;

        console.log('Authenticating with broker:', selectedBroker, 'mode:', currentMode, 'devMode:', devMode);

        // Get the API URL with the correct format based on the URL
        let apiUrl;
        const host = window.location.host;
        const isSubdomain = host.split('.').length > 1 && !host.startsWith('localhost');

        if (isSubdomain) {
            // In production mode with subdomain routing, use /api/broker/authenticate
            apiUrl = '/api/broker/authenticate';
        } else {
            // In test mode or fallback, use tenant prefix
            apiUrl = window.location.pathname.split('/').slice(0, 2).join('/') + '/api/broker/authenticate';
        }

        console.log('Authentication API URL:', apiUrl);

        // Prepare request data
        const requestData = {
            username: username,
            password: password,
            broker: selectedBroker,
            mode: currentMode,
            dev_mode: devMode
        };
        console.log('Authentication request data:', requestData);

        // Call the API to authenticate
        fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestData),
            credentials: 'same-origin'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Token is no longer displayed in the UI for security reasons

                // Update JWT status
                const jwtStatus = document.getElementById('jwtStatus');
                jwtStatus.innerHTML = '<i class="fas fa-check-circle mr-1"></i> Active';
                jwtStatus.classList.remove('bg-red-100', 'text-red-800', 'bg-gray-100', 'text-gray-800');
                jwtStatus.classList.add('bg-green-100', 'text-green-800');

                // Update token expiry
                const tokenExpiry = document.getElementById('tokenExpiry');
                tokenExpiry.textContent = 'Token expires in 30 days';

                // Update validation status
                const validationStatus = document.getElementById('validationStatus');
                validationStatus.textContent = 'Not validated';
                validationStatus.classList.remove('bg-green-100', 'text-green-800', 'bg-red-100', 'text-red-800');
                validationStatus.classList.add('bg-gray-100', 'text-gray-800');

                // Update connection status
                updateConnectionStatus('auto');

                // Close modal
                closeLoginTokenModal();

                // Show success message
                showToast('JWT token generated successfully', 'success');
            } else {
                // Show error message
                formErrors.classList.remove('hidden');

                // Format the error message for display
                let displayMessage = data.message || 'Authentication failed';

                // Check if it's a DNS or connection error
                if (displayMessage.includes('DNS resolution failed') ||
                    displayMessage.includes('Name or service not known')) {
                    displayMessage = 'Cannot connect to broker API server. Please check your network connection or contact support.';
                }

                errorMessage.textContent = displayMessage;

                // Reset progress bar
                progressBar.style.transition = 'width 0.3s ease-in-out';
                progressBar.style.width = '0%';

                // Show error toast
                showToast(displayMessage, 'error');

                // Log the full error for debugging
                console.error('Authentication error:', data.message);
            }
        })
        .catch(error => {
            console.error('Network error:', error);

            // Show error message
            formErrors.classList.remove('hidden');

            // Create a user-friendly error message
            const errorMsg = 'Network error connecting to the server. Please check your internet connection and try again.';
            errorMessage.textContent = errorMsg;

            // Reset progress bar
            progressBar.style.transition = 'width 0.3s ease-in-out';
            progressBar.style.width = '0%';

            // Show error toast
            showToast(errorMsg, 'error');

            // Add a retry button
            if (!document.getElementById('retryButton')) {
                const retryButton = document.createElement('button');
                retryButton.id = 'retryButton';
                retryButton.className = 'mt-2 px-4 py-2 bg-indigo-500 text-white rounded-lg hover:bg-indigo-600 transition-all duration-200';
                retryButton.innerHTML = '<i class="fas fa-sync-alt mr-2"></i>Retry';
                retryButton.addEventListener('click', function() {
                    // Remove this button
                    this.remove();
                    // Hide error message
                    formErrors.classList.add('hidden');
                    // Trigger authentication again
                    authenticateBtn.click();
                });
                formErrors.appendChild(retryButton);
            }
        })
        .finally(() => {
            // Hide loading spinner
            authSpinner.classList.add('hidden');
        });
    });

    // Token functionality has been removed for security
    // The token is now only stored on the server

    // Validate Button
    const validateBtn = document.getElementById('validateBtn');
    const validateSpinner = document.getElementById('validateSpinner');

    validateBtn.addEventListener('click', function() {
        // Show loading spinner
        validateSpinner.classList.remove('hidden');

        console.log('Validating token...');

        // Get the API URL with the correct format based on the URL
        let apiUrl;
        const host = window.location.host;
        const isSubdomain = host.split('.').length > 1 && !host.startsWith('localhost');

        if (isSubdomain) {
            // In production mode with subdomain routing, use /api/broker/validate-token
            apiUrl = '/api/broker/validate-token';
        } else {
            // In test mode or fallback, use tenant prefix
            apiUrl = window.location.pathname.split('/').slice(0, 2).join('/') + '/api/broker/validate-token';
        }

        console.log('Validation API URL:', apiUrl);

        fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            // No need to send the token from frontend
            body: JSON.stringify({}),
            credentials: 'same-origin'
        })
        .then(response => {
            console.log('Validation response status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('Validation response data:', data);

            // Update validation status
            const validationStatus = document.getElementById('validationStatus');
            const jwtStatus = document.getElementById('jwtStatus');
            const tokenStatusDisplay = document.getElementById('tokenStatusDisplay');

            if (data.success) {
                // Get token status if available
                const tokenStatus = data.token_status || 'Valid';
                console.log('Token status from validation response:', tokenStatus);

                // Update validation status
                validationStatus.innerHTML = '<i class="fas fa-check-circle mr-1"></i> Valid';
                validationStatus.classList.remove('bg-gray-100', 'text-gray-800', 'bg-red-100', 'text-red-800');
                validationStatus.classList.add('bg-green-100', 'text-green-800');

                // Also update JWT status
                jwtStatus.innerHTML = '<i class="fas fa-check-circle mr-1"></i> Active';
                jwtStatus.classList.remove('bg-red-100', 'text-red-800', 'bg-gray-100', 'text-gray-800');
                jwtStatus.classList.add('bg-green-100', 'text-green-800');

                // Update token status display
                tokenStatusDisplay.innerHTML = `<i class="fas fa-check-circle mr-1"></i> ${tokenStatus}`;
                tokenStatusDisplay.classList.remove('bg-gray-100', 'text-gray-800', 'bg-red-100', 'text-red-800', 'bg-blue-100', 'text-blue-800');
                tokenStatusDisplay.classList.add('bg-green-100', 'text-green-800');

                // Update connection status
                updateConnectionStatus(currentMode, true);

                // Show success message
                showToast('Token validated successfully', 'success');

                console.log('Token validation successful');
            } else {
                // Get token status if available
                const tokenStatus = data.token_status || 'Invalid';
                console.log('Token status from validation response:', tokenStatus);

                // Update validation status
                validationStatus.innerHTML = '<i class="fas fa-times-circle mr-1"></i> Invalid';
                validationStatus.classList.remove('bg-gray-100', 'text-gray-800', 'bg-green-100', 'text-green-800');
                validationStatus.classList.add('bg-red-100', 'text-red-800');

                // Also update JWT status
                jwtStatus.innerHTML = '<i class="fas fa-times-circle mr-1"></i> Inactive';
                jwtStatus.classList.remove('bg-green-100', 'text-green-800', 'bg-gray-100', 'text-gray-800');
                jwtStatus.classList.add('bg-red-100', 'text-red-800');

                // Update token status display
                tokenStatusDisplay.innerHTML = `<i class="fas fa-exclamation-circle mr-1"></i> ${tokenStatus}`;
                tokenStatusDisplay.classList.remove('bg-gray-100', 'text-gray-800', 'bg-green-100', 'text-green-800', 'bg-blue-100', 'text-blue-800');
                tokenStatusDisplay.classList.add('bg-red-100', 'text-red-800');

                // Update connection status
                updateConnectionStatus(currentMode, false);

                // Show error message with more details if available
                let errorMessage = 'Token validation failed';
                if (data.message) {
                    errorMessage = data.message;
                } else if (data.error) {
                    errorMessage = data.error;
                }

                showToast(errorMessage, 'error');

                console.log('Token validation failed:', errorMessage);
            }

            // After validation, refresh the broker setup data to get the latest status
            setTimeout(() => {
                console.log('Refreshing broker setup data after validation...');

                // Get the API URL with the correct format based on the URL
                let refreshApiUrl;
                const host = window.location.host;
                const isSubdomain = host.split('.').length > 1 && !host.startsWith('localhost');

                if (isSubdomain) {
                    // In production mode with subdomain routing, use /api/broker/refresh-setup
                    refreshApiUrl = '/api/broker/refresh-setup';
                } else {
                    // In test mode or fallback, use tenant prefix
                    refreshApiUrl = window.location.pathname.split('/').slice(0, 2).join('/') + '/api/broker/refresh-setup';
                }

                console.log('Refresh API URL:', refreshApiUrl);

                fetch(refreshApiUrl, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    credentials: 'same-origin'
                })
                .then(response => response.json())
                .then(refreshData => {
                    console.log('Refresh after validation response:', refreshData);
                })
                .catch(refreshError => {
                    console.error('Error refreshing after validation:', refreshError);
                });
            }, 1000);
        })
        .catch(error => {
            console.error('Error during validation:', error);

            // Update validation status
            const validationStatus = document.getElementById('validationStatus');
            validationStatus.innerHTML = '<i class="fas fa-exclamation-triangle mr-1"></i> Error';
            validationStatus.classList.remove('bg-gray-100', 'text-gray-800', 'bg-green-100', 'text-green-800');
            validationStatus.classList.add('bg-red-100', 'text-red-800');

            // Show error toast
            showToast('Network error during validation. Please try again.', 'error');
        })
        .finally(() => {
            // Hide loading spinner
            validateSpinner.classList.add('hidden');
        });
    });

    // Check for existing token
    function checkExistingToken() {
        console.log('Checking for existing token...');

        // Get the API URL with the correct format based on the URL
        let apiUrl;
        const host = window.location.host;
        const isSubdomain = host.split('.').length > 1 && !host.startsWith('localhost');

        if (isSubdomain) {
            // In production mode with subdomain routing, use /api/broker/token-status
            apiUrl = '/api/broker/token-status';
        } else {
            // In test mode or fallback, use tenant prefix
            apiUrl = window.location.pathname.split('/').slice(0, 2).join('/') + '/api/broker/token-status';
        }

        console.log('Token status API URL:', apiUrl);

        fetch(apiUrl, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
            credentials: 'same-origin'
        })
        .then(response => {
            console.log('Token status response status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('Token status response data:', JSON.stringify(data, null, 2));

            if (data.success && data.has_token) {
                console.log('Token found in database, updating UI...');

                // Token is no longer displayed in the UI for security reasons
                console.log('Token exists but not displayed for security');

                // Update JWT status based on token validity
                const jwtStatus = document.getElementById('jwtStatus');
                const isValid = data.is_valid;
                console.log('Token validity:', isValid);

                if (isValid) {
                    jwtStatus.innerHTML = '<i class="fas fa-check-circle mr-1"></i> Active';
                    jwtStatus.classList.remove('bg-red-100', 'text-red-800', 'bg-gray-100', 'text-gray-800');
                    jwtStatus.classList.add('bg-green-100', 'text-green-800');
                    console.log('Set JWT status to Active');
                } else {
                    jwtStatus.innerHTML = '<i class="fas fa-times-circle mr-1"></i> Inactive';
                    jwtStatus.classList.remove('bg-green-100', 'text-green-800', 'bg-gray-100', 'text-gray-800');
                    jwtStatus.classList.add('bg-red-100', 'text-red-800');
                    console.log('Set JWT status to Inactive');
                }

                // Update token expiry
                const tokenExpiry = document.getElementById('tokenExpiry');
                if (data.expiry) {
                    const expiryDate = new Date(data.expiry);
                    const now = new Date();
                    const diffTime = Math.abs(expiryDate - now);
                    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                    tokenExpiry.textContent = `Token expires in ${diffDays} days`;
                    console.log(`Set token expiry to ${diffDays} days`);
                } else {
                    tokenExpiry.textContent = 'Token expires in 30 days';
                    console.log('Set default token expiry (30 days)');
                }

                // Update validation status based on token validity
                const validationStatus = document.getElementById('validationStatus');
                const tokenStatusDisplay = document.getElementById('tokenStatusDisplay');

                // Get token status from response
                const tokenStatus = data.token_status || 'Not validated';
                console.log('Token status from server:', tokenStatus);

                if (isValid) {
                    // Update validation status
                    validationStatus.innerHTML = '<i class="fas fa-check-circle mr-1"></i> Valid';
                    validationStatus.classList.remove('bg-gray-100', 'text-gray-800', 'bg-red-100', 'text-red-800');
                    validationStatus.classList.add('bg-green-100', 'text-green-800');
                    console.log('Set validation status to Valid');

                    // Update token status display
                    tokenStatusDisplay.innerHTML = `<i class="fas fa-check-circle mr-1"></i> ${tokenStatus}`;
                    tokenStatusDisplay.classList.remove('bg-gray-100', 'text-gray-800', 'bg-red-100', 'text-red-800', 'bg-blue-100', 'text-blue-800');
                    tokenStatusDisplay.classList.add('bg-green-100', 'text-green-800');
                    console.log('Set token status display to:', tokenStatus);
                } else {
                    // Update validation status
                    validationStatus.innerHTML = '<i class="fas fa-times-circle mr-1"></i> Invalid';
                    validationStatus.classList.remove('bg-gray-100', 'text-gray-800', 'bg-green-100', 'text-green-800');
                    validationStatus.classList.add('bg-red-100', 'text-red-800');
                    console.log('Set validation status to Invalid');

                    // Update token status display
                    tokenStatusDisplay.innerHTML = `<i class="fas fa-exclamation-circle mr-1"></i> ${tokenStatus}`;
                    tokenStatusDisplay.classList.remove('bg-gray-100', 'text-gray-800', 'bg-green-100', 'text-green-800', 'bg-blue-100', 'text-blue-800');
                    tokenStatusDisplay.classList.add('bg-red-100', 'text-red-800');
                    console.log('Set token status display to:', tokenStatus);
                }

                // Get mode and broker from response
                const mode = data.mode || 'auto';
                const activeBroker = data.active_broker || 'exness';

                console.log('Setting mode from database:', mode);
                console.log('Setting broker from database:', activeBroker);

                // Set current mode before calling setMode to avoid unnecessary API calls
                currentMode = mode;
                console.log('Updated currentMode to:', currentMode);

                // Set mode UI
                setModeUI(mode);
                console.log('Updated mode UI');

                // Update connection status based on mode and token validity
                updateConnectionStatus(mode, isValid);
                console.log('Updated connection status');

                // Set broker selection
                const brokerSelect = document.getElementById('brokerSelect');
                if (brokerSelect && activeBroker) {
                    // Find the option with the matching value
                    for (let i = 0; i < brokerSelect.options.length; i++) {
                        if (brokerSelect.options[i].value === activeBroker) {
                            brokerSelect.selectedIndex = i;
                            console.log(`Set broker selection to ${activeBroker} (index ${i})`);
                            break;
                        }
                    }
                }

                // Show success message
                showToast('Existing broker configuration loaded', 'info');
                console.log('Broker configuration loaded successfully');
            } else {
                console.log('No token found in database or token status request failed');
                if (!data.success) {
                    console.error('Token status request failed:', data.message || 'Unknown error');
                }
            }
        })
        .catch(error => {
            console.error('Error checking token status:', error);
            showToast('Error loading broker configuration', 'error');
        });
    }

    // Function to update just the UI for mode without making API calls
    function setModeUI(mode) {
        if (mode === 'auto') {
            autoModeBtn.classList.remove('bg-gray-200', 'text-gray-700');
            autoModeBtn.classList.add('bg-indigo-500', 'text-white');
            manualModeBtn.classList.remove('bg-indigo-500', 'text-white');
            manualModeBtn.classList.add('bg-gray-200', 'text-gray-700');
            modeDescription.textContent = 'Auto mode automatically syncs user data with broker API';
        } else {
            manualModeBtn.classList.remove('bg-gray-200', 'text-gray-700');
            manualModeBtn.classList.add('bg-indigo-500', 'text-white');
            autoModeBtn.classList.remove('bg-indigo-500', 'text-white');
            autoModeBtn.classList.add('bg-gray-200', 'text-gray-700');
            modeDescription.textContent = 'Manual mode requires you to manually sync user data';
        }
    }

    // Function to update connection status indicator
    function updateConnectionStatus(mode, isValid) {
        const connectionStatusIndicator = document.getElementById('connectionStatusIndicator');
        const connectionStatus = document.getElementById('connectionStatus');

        if (mode === 'auto' && isValid) {
            // Connected and auto mode
            connectionStatusIndicator.classList.remove('bg-red-500', 'bg-yellow-500');
            connectionStatusIndicator.classList.add('bg-green-500', 'pulse-animation');
            connectionStatus.textContent = 'Connected';
        } else if (mode === 'manual' && isValid) {
            // Manual mode but token is valid
            connectionStatusIndicator.classList.remove('bg-red-500', 'bg-green-500', 'pulse-animation');
            connectionStatusIndicator.classList.add('bg-yellow-500');
            connectionStatus.textContent = 'Manual Mode';
        } else {
            // Not connected or token invalid
            connectionStatusIndicator.classList.remove('bg-green-500', 'bg-yellow-500', 'pulse-animation');
            connectionStatusIndicator.classList.add('bg-red-500');
            connectionStatus.textContent = 'Disconnected';
        }
    }

    // Initialize with auto mode (but don't make API call yet)
    currentMode = 'auto';
    setModeUI('auto');

    // Refresh Setup Button
    const refreshSetupBtn = document.getElementById('refreshSetupBtn');
    if (refreshSetupBtn) {
        refreshSetupBtn.addEventListener('click', function() {
            // Add spinning animation
            const icon = this.querySelector('i');
            icon.classList.add('fa-spin');

            console.log('Refreshing broker setup data from database...');

            // Get the API URL with the correct format based on the URL
            let refreshApiUrl;
            const host = window.location.host;
            const isSubdomain = host.split('.').length > 1 && !host.startsWith('localhost');

            if (isSubdomain) {
                // In production mode with subdomain routing, use /api/broker/refresh-setup
                refreshApiUrl = '/api/broker/refresh-setup';
            } else {
                // In test mode or fallback, use tenant prefix
                refreshApiUrl = window.location.pathname.split('/').slice(0, 2).join('/') + '/api/broker/refresh-setup';
            }

            console.log('Refresh API URL:', refreshApiUrl);

            // Call the API to refresh setup data
            fetch(refreshApiUrl, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                console.log('Refresh setup response:', data);

                if (data.success && data.has_token) {
                    // Token is no longer displayed in the UI for security reasons

                    // Update JWT status based on token validity
                    const jwtStatus = document.getElementById('jwtStatus');
                    const tokenStatusDisplay = document.getElementById('tokenStatusDisplay');
                    const isValid = data.is_valid;

                    // Get token status from response
                    const tokenStatus = data.token_status || 'Not validated';
                    console.log('Token status from refresh response:', tokenStatus);

                    if (isValid) {
                        // Update JWT status
                        jwtStatus.innerHTML = '<i class="fas fa-check-circle mr-1"></i> Active';
                        jwtStatus.classList.remove('bg-red-100', 'text-red-800', 'bg-gray-100', 'text-gray-800');
                        jwtStatus.classList.add('bg-green-100', 'text-green-800');

                        // Update token status display
                        tokenStatusDisplay.innerHTML = `<i class="fas fa-check-circle mr-1"></i> ${tokenStatus}`;
                        tokenStatusDisplay.classList.remove('bg-gray-100', 'text-gray-800', 'bg-red-100', 'text-red-800', 'bg-blue-100', 'text-blue-800');
                        tokenStatusDisplay.classList.add('bg-green-100', 'text-green-800');
                    } else {
                        // Update JWT status
                        jwtStatus.innerHTML = '<i class="fas fa-times-circle mr-1"></i> Inactive';
                        jwtStatus.classList.remove('bg-green-100', 'text-green-800', 'bg-gray-100', 'text-gray-800');
                        jwtStatus.classList.add('bg-red-100', 'text-red-800');

                        // Update token status display
                        tokenStatusDisplay.innerHTML = `<i class="fas fa-exclamation-circle mr-1"></i> ${tokenStatus}`;
                        tokenStatusDisplay.classList.remove('bg-gray-100', 'text-gray-800', 'bg-green-100', 'text-green-800', 'bg-blue-100', 'text-blue-800');
                        tokenStatusDisplay.classList.add('bg-red-100', 'text-red-800');
                    }

                    // Update token expiry
                    const tokenExpiry = document.getElementById('tokenExpiry');
                    if (data.expiry) {
                        const expiryDate = new Date(data.expiry);
                        const now = new Date();
                        const diffTime = Math.abs(expiryDate - now);
                        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                        tokenExpiry.textContent = `Token expires in ${diffDays} days`;
                    } else {
                        tokenExpiry.textContent = 'Token expires in 30 days';
                    }

                    // Update validation status based on token validity
                    const validationStatus = document.getElementById('validationStatus');
                    if (isValid) {
                        validationStatus.innerHTML = '<i class="fas fa-check-circle mr-1"></i> Valid';
                        validationStatus.classList.remove('bg-gray-100', 'text-gray-800', 'bg-red-100', 'text-red-800');
                        validationStatus.classList.add('bg-green-100', 'text-green-800');
                    } else {
                        validationStatus.innerHTML = '<i class="fas fa-times-circle mr-1"></i> Invalid';
                        validationStatus.classList.remove('bg-gray-100', 'text-gray-800', 'bg-green-100', 'text-green-800');
                        validationStatus.classList.add('bg-red-100', 'text-red-800');
                    }

                    // Get mode and broker from response
                    const mode = data.mode || 'auto';
                    const activeBroker = data.active_broker || 'exness';

                    console.log('Setting mode from database:', mode);
                    console.log('Setting broker from database:', activeBroker);

                    // Set current mode before calling setMode to avoid unnecessary API calls
                    currentMode = mode;

                    // Set mode UI
                    setModeUI(mode);

                    // Update connection status based on mode and token validity
                    updateConnectionStatus(mode, isValid);

                    // Set broker selection
                    const brokerSelect = document.getElementById('brokerSelect');
                    if (brokerSelect && activeBroker) {
                        // Find the option with the matching value
                        for (let i = 0; i < brokerSelect.options.length; i++) {
                            if (brokerSelect.options[i].value === activeBroker) {
                                brokerSelect.selectedIndex = i;
                                break;
                            }
                        }
                    }

                    // Show success message
                    showToast('Broker setup data refreshed successfully', 'success');
                } else {
                    // Show error message
                    showToast('No broker setup data found', 'warning');
                }
            })
            .catch(error => {
                console.error('Error refreshing broker setup:', error);
                showToast('Error refreshing broker setup data', 'error');
            })
            .finally(() => {
                // Remove spinning animation
                icon.classList.remove('fa-spin');
            });
        });
    }

    // Wait for DOM to be fully loaded before checking for existing token
    // This ensures all elements are available
    setTimeout(() => {
        console.log('DOM fully loaded, checking for existing token...');
        // Check for existing token (this will set the mode from the database)
        checkExistingToken();
    }, 100);
});

// Close modal function
function closeLoginTokenModal() {
    document.getElementById('loginTokenModal').classList.add('hidden');

    // Reset form state
    const progressBar = document.getElementById('progressBar');
    if (progressBar) {
        progressBar.style.transition = 'none';
        progressBar.style.width = '0%';
    }

    // Hide any spinners
    const authSpinner = document.getElementById('authSpinner');
    if (authSpinner) {
        authSpinner.classList.add('hidden');
    }
}

// Toast notification function
function showToast(message, type = 'info') {
    // Create toast container if it doesn't exist
    let toastContainer = document.getElementById('toastContainer');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.id = 'toastContainer';
        toastContainer.className = 'fixed bottom-4 right-4 z-50 flex flex-col space-y-2';
        document.body.appendChild(toastContainer);
    }

    // Create toast element
    const toast = document.createElement('div');
    toast.className = `p-3 rounded-lg shadow-lg flex items-center justify-between min-w-[300px] transform transition-all duration-300 translate-y-2 opacity-0`;

    // Set background and icon based on type
    let icon, bgClass;
    switch (type) {
        case 'success':
            bgClass = 'bg-green-100 text-green-800 border-l-4 border-green-500';
            icon = 'fa-check-circle';
            break;
        case 'error':
            bgClass = 'bg-red-100 text-red-800 border-l-4 border-red-500';
            icon = 'fa-exclamation-circle';
            break;
        case 'warning':
            bgClass = 'bg-yellow-100 text-yellow-800 border-l-4 border-yellow-500';
            icon = 'fa-exclamation-triangle';
            break;
        default:
            bgClass = 'bg-indigo-100 text-indigo-800 border-l-4 border-indigo-500';
            icon = 'fa-info-circle';
    }

    toast.classList.add(...bgClass.split(' '));

    // Set toast content
    toast.innerHTML = `
        <div class="flex items-center">
            <i class="fas ${icon} mr-2"></i>
            <span>${message}</span>
        </div>
        <button class="ml-4 text-gray-500 hover:text-gray-700 focus:outline-none">
            <i class="fas fa-times"></i>
        </button>
    `;

    // Add toast to container
    toastContainer.appendChild(toast);

    // Animate in
    setTimeout(() => {
        toast.classList.remove('translate-y-2', 'opacity-0');
    }, 10);

    // Add close button functionality
    const closeBtn = toast.querySelector('button');
    closeBtn.addEventListener('click', () => {
        removeToast(toast);
    });

    // Auto remove after 5 seconds
    setTimeout(() => {
        removeToast(toast);
    }, 5000);
}

function removeToast(toast) {
    // Animate out
    toast.classList.add('translate-y-2', 'opacity-0');

    // Remove after animation completes
    setTimeout(() => {
        toast.remove();
    }, 300);
}

// Add CSS for custom scrollbar
document.addEventListener('DOMContentLoaded', function() {
    const scrollbarStyle = document.createElement('style');
    scrollbarStyle.textContent = `
        .scrollbar-thin::-webkit-scrollbar {
            width: 6px;
        }

        .scrollbar-thin::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 10px;
        }

        .scrollbar-thin::-webkit-scrollbar-thumb {
            background: #cbd5e0;
            border-radius: 10px;
        }

        .scrollbar-thin::-webkit-scrollbar-thumb:hover {
            background: #a0aec0;
        }

        .scrollbar-thumb-indigo-200::-webkit-scrollbar-thumb {
            background: #c3dafe;
        }

        .scrollbar-thumb-indigo-200::-webkit-scrollbar-thumb:hover {
            background: #a3bffa;
        }

        .scrollbar-track-gray-100::-webkit-scrollbar-track {
            background: #f7fafc;
        }
    `;
    document.head.appendChild(scrollbarStyle);
});
