/* Date Range Picker Custom Styles - Professional & Refined */
.litepicker {
    font-family: inherit;
    border-radius: 0.25rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    animation: calendarFadeIn 0.25s ease-out;
    border: 1px solid #E5E7EB;
    background-color: #FFFFFF;
    overflow: hidden;
}

@keyframes calendarFadeIn {
    from { opacity: 0; transform: translateY(-8px); }
    to { opacity: 1; transform: translateY(0); }
}

.litepicker .container__months {
    border-radius: 0.25rem;
    box-shadow: none;
    padding: 12px;
}

.litepicker .container__months .month-item-header {
    padding: 10px 0;
    font-weight: 600;
    color: #374151;
    transition: all 0.2s ease;
    border-bottom: 1px solid #F3F4F6;
    margin-bottom: 8px;
}

.litepicker .container__months .month-item-header div {
    transition: all 0.2s ease;
}

.litepicker .container__months .month-item-header div:hover {
    color: #4F46E5;
}

.litepicker .container__months .month-item-header .button-previous-month,
.litepicker .container__months .month-item-header .button-next-month {
    color: #6B7280;
    padding: 4px 8px;
    border-radius: 0.25rem;
}

.litepicker .container__months .month-item-header .button-previous-month:hover,
.litepicker .container__months .month-item-header .button-next-month:hover {
    background-color: #F9FAFB;
}

.litepicker .container__months .month-item-weekdays-row {
    color: #6B7280;
    font-weight: 500;
    font-size: 0.75rem;
    padding: 8px 0;
}

.litepicker .container__days .day-item {
    color: #4B5563;
    border-radius: 0;
    height: 36px;
    width: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    position: relative;
    font-weight: 400;
    font-size: 0.875rem;
}

.litepicker .container__days .day-item:hover {
    color: #4F46E5;
    background-color: #F5F7FF;
    font-weight: 500;
    z-index: 1;
    outline: 1px solid #E0E7FF;
}

.litepicker .container__days .day-item.is-start-date,
.litepicker .container__days .day-item.is-end-date {
    background-color: #4F46E5;
    color: white;
    font-weight: 500;
    z-index: 2;
}

.litepicker .container__days .day-item.is-in-range {
    background-color: #EEF2FF;
    color: #4F46E5;
    position: relative;
}

.litepicker .container__days .day-item.is-today {
    color: #EF4444;
    font-weight: 500;
}

.litepicker .container__days .day-item.is-locked {
    color: #D1D5DB;
    cursor: not-allowed;
}

.litepicker .container__days .day-item.is-locked:hover {
    color: #D1D5DB;
    background-color: transparent;
    outline: none;
}

.litepicker .container__tooltip {
    background-color: #4F46E5;
    border-radius: 0.25rem;
    padding: 4px 8px;
    font-size: 0.75rem;
    margin-top: -4px;
}

.litepicker .container__tooltip::before {
    border-top-color: #4F46E5;
}

/* Mobile Responsive Adjustments */
@media (max-width: 480px) {
    .litepicker {
        font-size: 0.875rem;
    }
    
    .litepicker .container__days .day-item {
        height: 32px;
        width: 32px;
    }
}
