/* Sidebar transition styles */
:root {
    --sidebar-width: 16rem;
    --main-margin: 16rem;
}
#sidebar {
    width: var(--sidebar-width) !important;
    transition: width 0.3s ease;
}
#mainContent {
    margin-left: var(--main-margin) !important;
    transition: margin-left 0.3s ease;
}

/* Profile menu styles */
#profileMenu {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    width: 224px !important;
}

/* Sidebar styles */
.sidebar-menu-item {
    border-radius: 0.375rem;
    transition: all 0.2s ease;
}

.sidebar-menu-item:hover {
    background-color: rgba(79, 70, 229, 0.1);
}

.sidebar-menu-item.active {
    background-color: rgba(79, 70, 229, 0.1);
}

.sidebar-menu-item.active .sidebar-icon {
    color: #4F46E5;
}

.sidebar-menu-item.active .sidebar-text {
    color: #4F46E5;
    font-weight: 600;
}

/* Sidebar submenu styles */
.sidebar-submenu {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.sidebar-submenu.open {
    max-height: 500px; /* Arbitrary large value */
}

.sidebar-submenu-item {
    padding-left: 2.5rem;
    border-radius: 0.375rem;
    transition: all 0.2s ease;
}

.sidebar-submenu-item:hover {
    background-color: rgba(79, 70, 229, 0.05);
}

.sidebar-submenu-item.active {
    background-color: rgba(79, 70, 229, 0.05);
    color: #4F46E5;
    font-weight: 500;
}

/* Notification styles */
.notification-badge {
    position: absolute;
    top: -0.25rem;
    right: -0.25rem;
    height: 1rem;
    width: 1rem;
    border-radius: 9999px;
    background-color: #EF4444;
    color: white;
    font-size: 0.625rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Toast notification styles */
.toast-container {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 50;
}

.toast {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    border-radius: 0.375rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
    max-width: 24rem;
    animation: slideIn 0.3s ease forwards;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.toast-success {
    background-color: #D1FAE5;
    border-left: 4px solid #10B981;
    color: #065F46;
}

.toast-error {
    background-color: #FEE2E2;
    border-left: 4px solid #EF4444;
    color: #991B1B;
}

.toast-warning {
    background-color: #FEF3C7;
    border-left: 4px solid #F59E0B;
    color: #92400E;
}

.toast-info {
    background-color: #DBEAFE;
    border-left: 4px solid #3B82F6;
    color: #1E40AF;
}

.toast-icon {
    margin-right: 0.75rem;
    flex-shrink: 0;
}

.toast-message {
    flex-grow: 1;
}

.toast-close {
    margin-left: 0.75rem;
    cursor: pointer;
    color: currentColor;
    opacity: 0.7;
    transition: opacity 0.2s ease;
}

.toast-close:hover {
    opacity: 1;
}

/* Mobile responsive styles */
@media (max-width: 768px) {
    /* Mobile profile menu styles */
    #profileMenu {
        position: absolute;
        right: 0;
        width: 224px !important;
        top: 100%;
        margin-top: 0.5rem;
    }

    /* Mobile sidebar styles */
    :root {
        --sidebar-width: 0;
        --main-margin: 0;
    }

    #sidebar {
        transform: translateX(-100%);
        width: 70% !important;
        z-index: 100;
        transition: transform 0.3s ease;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
        height: 100vh;
        position: fixed;
        top: 0;
        left: 0;
    }

    #sidebar.mobile-open {
        transform: translateX(0);
    }

    #sidebar.sidebar-opening {
        animation: slideIn 0.3s ease forwards;
    }

    #sidebar.sidebar-closing {
        animation: slideOut 0.3s ease forwards;
    }

    #sidebar .sidebar-text {
        display: block !important; /* Ensure sidebar text is visible on mobile */
    }

    #mainContent {
        margin-left: 0 !important;
        width: 100%;
        overflow-x: hidden;
    }

    .mobile-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 90;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    .mobile-overlay.active {
        display: block;
        opacity: 1;
    }

    /* Adjust header elements for mobile */
    header h1 {
        text-align: center;
        margin: 0 0 1rem 0; /* Keep bottom margin */
        padding: 0;
        box-sizing: border-box;
        font-size: 2rem; /* Larger font size to match image */
        line-height: 1.2;
        display: block;
        color: #222; /* Darker text color */
        font-weight: 600; /* Semi-bold font */
    }

    /* Position Admin tag below title */
    header h1 + span.bg-indigo-100 {
        display: inline-block;
        margin: 0 auto 1.5rem; /* Center horizontally with auto margins */
        padding: 0.5rem 1.5rem; /* Larger padding */
        border-radius: 2rem; /* Rounded corners */
        font-size: 1.1rem; /* Larger font size */
        font-weight: 500; /* Medium font weight */
        color: #3730a3; /* Indigo text color */
    }

    /* Row 2: Search bar + Icons */
    #headerIcons {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        width: 100%;
        margin-top: 1rem;
        padding: 1rem 0.5rem 0.5rem;
        border-top: 1px solid #f0f0f0; /* Light separator */
    }

    /* Mobile header layout - Two-row structure */
    header .flex.flex-col.md\:flex-row {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
        padding-top: 0.5rem;
        padding-bottom: 0.5rem;
    }

    /* Row 1: Hamburger + Title + Admin tag */
    header .flex.flex-wrap.items-center.w-full.md\:w-auto {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: center; /* Center items horizontally */
        position: relative;
        width: 100%;
        margin-bottom: 0;
        padding: 4rem 0 1.5rem 0; /* Add top padding to make room for hamburger */
    }

    /* Position hamburger menu in top-left corner */
    #mobileMenuToggle {
        position: absolute;
        left: 1.5rem; /* Distance from left edge */
        top: 1.5rem; /* Distance from top edge */
        margin-right: 0;
        z-index: 10;
    }

    /* Make hamburger icon larger */
    #mobileMenuToggle i {
        font-size: 1.5rem;
        color: #222;
    }

    /* Search bar container */
    #headerIcons > div.relative.w-full {
        width: 65%;
        flex: 0 0 65%;
        margin-right: 0;
    }

    /* Ensure search input has correct width and padding */
    #headerIcons #globalSearch {
        width: 100%;
        padding-left: 2.5rem !important;
        font-size: 0.875rem;
    }

    /* Fix search icon positioning */
    #headerIcons .fa-search {
        left: 0.75rem !important;
        z-index: 10;
    }

    /* Prevent search input from expanding on focus */
    #headerIcons #globalSearch:focus {
        width: 100% !important;
    }

    /* Group notification and profile icons together on right */
    #headerIcons > div:not(.relative.w-full) {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        gap: 1rem;
        flex: 0 0 auto;
    }

    /* Reset spacing between notification and profile icons */
    #headerIcons .space-x-3 > * + * {
        margin-left: 0.75rem !important;
    }

    /* Reset general relative width for mobile */
    header .relative {
        width: auto;
    }

    /* Animations for sidebar */
    @keyframes slideIn {
        from { transform: translateX(-100%); }
        to { transform: translateX(0); }
    }

    @keyframes slideOut {
        from { transform: translateX(0); }
        to { transform: translateX(-100%); }
    }

    /* Fix for horizontal scrolling */
    body {
        overflow-x: hidden;
        width: 100%;
        position: relative;
    }

    /* Fix search inputs in all forms for mobile */
    .relative input[type="text"][placeholder*="Search"],
    .relative input[type="text"][name="filter"],
    form .relative input[type="text"] {
        padding-left: 2.5rem !important;
    }

    /* Fix search icon positioning in all forms */
    .relative .fa-search,
    .relative .fas.fa-search,
    .relative i[class*="fa-search"] {
        left: 0.75rem !important;
        z-index: 10;
        position: absolute;
    }

    /* Ensure all search icon containers have proper positioning */
    form .relative,
    .relative:has(i[class*="fa-search"]) {
        position: relative !important;
    }

    .toast-container {
        width: calc(100% - 2rem);
    }

    .toast {
        width: 100%;
    }
}

/* Desktop styles to override mobile changes */
@media (min-width: 768px) {
    header h1 {
        text-align: left;
        margin: 0;
        padding: 0;
        width: auto;
        font-size: 1.5rem;
        font-weight: 700;
        display: inline;
        white-space: normal;
    }

    header h1 + span.bg-indigo-100 {
        display: inline-block;
        margin: 0 0 0 0.5rem;
        padding: 0.25rem 0.75rem;
        font-size: 0.75rem;
    }

    header .flex.flex-wrap.items-center.w-full.md\:w-auto {
        flex-direction: row;
        padding: 0;
        align-items: center;
        justify-content: flex-start;
    }
}

/* Collapsed sidebar styles */
.sidebar-collapsed .sidebar-text {
    display: none !important;
}

.sidebar-collapsed #sidebar i.fas,
.sidebar-collapsed #sidebar i.fab {
    margin: 0 auto;
    width: 1.25rem; /* Ensure all icons have the same width */
    text-align: center;
}

.sidebar-collapsed #sidebar a,
.sidebar-collapsed #sidebar div {
    justify-content: center;
    position: relative;
}

/* Base tooltip styles for collapsed sidebar */
.sidebar-collapsed #sidebar a::after,
.sidebar-collapsed #sidebar #collapseSidebar::after {
    content: attr(data-tooltip);
    position: absolute;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 100;
    margin-left: 10px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
    transition-delay: 0.5s; /* Delay before showing tooltip */
    pointer-events: none;
}

/* Arrow for tooltip */
.sidebar-collapsed #sidebar a::before,
.sidebar-collapsed #sidebar #collapseSidebar::before {
    content: '';
    position: absolute;
    left: 100%;
    top: 50%;
    transform: translateY(-50%);
    border-width: 5px;
    border-style: solid;
    border-color: transparent rgba(0, 0, 0, 0.8) transparent transparent;
    margin-left: 0px;
    z-index: 100;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s, visibility 0.3s;
    transition-delay: 0.5s; /* Delay before showing tooltip */
    pointer-events: none;
}

/* Show tooltip on hover */
.sidebar-collapsed #sidebar a:hover::after,
.sidebar-collapsed #sidebar #collapseSidebar:hover::after,
.sidebar-collapsed #sidebar a:hover::before,
.sidebar-collapsed #sidebar #collapseSidebar:hover::before {
    opacity: 1;
    visibility: visible;
}

/* Sidebar dropdown styles */
.sidebar-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    color: #e2e8f0;
    transition: all 0.2s;
}

/* Ensure consistent icon spacing */
.sidebar-link i:not(#requestsDropdownIcon) {
    width: 1.25rem !important;
    text-align: center !important;
    margin-right: 0.75rem !important;
    margin-left: 0 !important;
}

/* Ensure consistent padding for all sidebar links */
.sidebar-link {
    padding-left: 1rem !important;
}

/* Ensure consistent font size for all sidebar text */
.sidebar-text {
    font-size: 1rem !important;
}

.sidebar-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.sidebar-link.active {
    background-color: rgba(196, 204, 222, 0.2);
    border-left: 3px solid #4f46e5;
}

.sidebar-sublink {
    display: flex;
    align-items: center;
    padding: 0.5rem 0.75rem;
    color: #cbd5e0;
    transition: all 0.2s;
    font-size: 0.875rem;
}

/* Override font size for submenu text */
.sidebar-submenu .sidebar-text {
    font-size: 0.875rem !important;
}

.sidebar-sublink:hover {
    background-color: rgba(255, 255, 255, 0.05);
    color: #f7fafc;
}

.sidebar-sublink.active {
    background-color: rgba(79, 70, 229, 0.2);
    border-left: 3px solid #4f46e5;
    padding-left: calc(0.75rem - 3px);
    color: white;
}

.sidebar-submenu {
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 0.375rem;
    margin-top: 0.25rem;
}

/* Ensure proper alignment in collapsed state */
.sidebar-collapsed #requestsDropdownButton {
    padding: 0.75rem 1rem !important;
    justify-content: center;
}

/* Ensure all sidebar links have consistent padding in collapsed state */
.sidebar-collapsed .sidebar-link {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
}

.sidebar-collapsed #sidebar button i:not(#requestsDropdownIcon) {
    margin: 0 auto;
    width: 1.25rem;
    text-align: center;
}

/* Ensure all sidebar icons have consistent width */
#sidebar i.fas, #sidebar i.fab {
    width: 1.25rem;
    text-align: center;
}

/* Submenu popup for collapsed sidebar - simplified */
.sidebar-collapsed #requestsDropdownMenu.popup-visible {
    display: block;
    position: fixed;
    left: calc(var(--sidebar-width) + 10px);
    width: 220px;
    max-height: none !important;
    background-color: #1e293b;
    border-radius: 0.375rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    z-index: 50;
    padding: 0.5rem 0;
    border: 1px solid rgba(79, 70, 229, 0.2);
    overflow: hidden;
}

/* Show popup menu and hide expanded menu when sidebar is collapsed */
.sidebar-collapsed #requestsDropdownMenu .sidebar-expanded-menu {
    display: none;
}

.sidebar-collapsed #requestsDropdownMenu .sidebar-popup-menu {
    display: block;
}

/* Show expanded menu and hide popup menu when sidebar is expanded */
#requestsDropdownMenu .sidebar-expanded-menu {
    display: block;
}

#requestsDropdownMenu .sidebar-popup-menu {
    display: none;
}

/* Arrow for submenu popup */
.sidebar-collapsed #requestsDropdownMenu.popup-visible::before {
    content: '';
    position: absolute;
    left: -6px;
    top: 15px;
    width: 0;
    height: 0;
    border-top: 6px solid transparent;
    border-bottom: 6px solid transparent;
    border-right: 6px solid #1e293b;
    z-index: 51;
}

/* Default state for collapsed submenu */
.sidebar-collapsed #requestsDropdownMenu {
    display: none;
}

/* Center the icon in collapsed state */
.sidebar-collapsed #requestsDropdownButton .flex-1 {
    justify-content: center !important;
}

/* Custom scrollbar for sidebar */
.sidebar-scrollbar::-webkit-scrollbar {
    width: 4px;
}

.sidebar-scrollbar::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
}

.sidebar-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

.sidebar-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* Hide scrollbar when not hovering for a cleaner look */
.sidebar-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.1) rgba(0, 0, 0, 0.1);
}

/* Ensure the sidebar content has proper padding at the bottom */
.sidebar-scrollbar > div:last-child {
    padding-bottom: 1rem;
}
