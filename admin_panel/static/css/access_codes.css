/* Date Range Picker Custom Styles - Professional & Refined */
.litepicker {
    font-family: inherit;
    border-radius: 0.25rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    animation: calendarFadeIn 0.25s ease-out;
    border: 1px solid #E5E7EB;
    background-color: #FFFFFF;
    overflow: hidden;
}

@keyframes calendarFadeIn {
    from { opacity: 0; transform: translateY(-8px); }
    to { opacity: 1; transform: translateY(0); }
}

.litepicker .container__months {
    border-radius: 0.25rem;
    box-shadow: none;
    padding: 12px;
}

.litepicker .container__months .month-item-header {
    padding: 10px 0;
    font-weight: 600;
    color: #374151;
    transition: all 0.2s ease;
    border-bottom: 1px solid #F3F4F6;
    margin-bottom: 8px;
}

.litepicker .container__months .month-item-header div {
    transition: all 0.2s ease;
}

.litepicker .container__months .month-item-header div:hover {
    color: #4F46E5;
}

.litepicker .container__months .month-item-header .button-previous-month,
.litepicker .container__months .month-item-header .button-next-month {
    color: #6B7280;
    padding: 4px 8px;
    border-radius: 0.25rem;
}

.litepicker .container__months .month-item-header .button-previous-month:hover,
.litepicker .container__months .month-item-header .button-next-month:hover {
    background-color: #F9FAFB;
}

.litepicker .container__months .month-item-weekdays-row {
    color: #6B7280;
    font-weight: 500;
    font-size: 0.75rem;
    padding: 8px 0;
}

.litepicker .container__days .day-item {
    color: #4B5563;
    border-radius: 0;
    height: 36px;
    width: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    position: relative;
    font-weight: 400;
    font-size: 0.875rem;
}

.litepicker .container__days .day-item:hover {
    color: #4F46E5;
    background-color: #F5F7FF;
    font-weight: 500;
    z-index: 1;
    outline: 1px solid #E0E7FF;
}

.litepicker .container__days .day-item.is-start-date,
.litepicker .container__days .day-item.is-end-date {
    background-color: #4F46E5;
    color: white;
    font-weight: 500;
    z-index: 2;
}

.litepicker .container__days .day-item.is-in-range {
    background-color: #EEF2FF;
    color: #4F46E5;
    position: relative;
}

/* Special handling for first and last items in range */
.litepicker .container__days .day-item.is-in-range.is-first-day-in-range {
    border-left: 2px solid #4F46E5;
}

.litepicker .container__days .day-item.is-in-range.is-last-day-in-range {
    border-right: 2px solid #4F46E5;
}

/* Remove pulsing animation for a more professional look */
.litepicker .container__days .day-item.is-in-range::before {
    display: none;
}

/* Footer removed as we're using autoApply */

.litepicker .container__tooltip {
    border-radius: 0.25rem;
    background-color: #F3F4F6;
    color: #4B5563;
    font-size: 0.75rem;
    padding: 2px 6px;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.date-range-input {
    cursor: pointer;
    background-color: white;
    transition: all 0.2s ease;
    border-radius: 0.25rem;
    font-size: 0.875rem;
}

.date-range-input:hover {
    border-color: #818CF8;
}

.date-range-input:focus {
    border-color: #4F46E5;
    box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.1);
}

/* Make placeholder text smaller */
.date-range-input::placeholder {
    font-size: 0.875rem;
    color: #9CA3AF;
}

/* Date restriction tooltip */
#subscriptionDateTooltip {
    color: #DC2626;
    font-size: 0.75rem;
    margin-top: 0.25rem;
    transition: opacity 0.3s ease;
    animation: fadeInOut 3s ease;
}

@keyframes fadeInOut {
    0% { opacity: 0; }
    10% { opacity: 1; }
    90% { opacity: 1; }
    100% { opacity: 0; }
}

/* Today's date styling - Simple Professional */
.litepicker .container__days .day-item.is-today {
    color: #DC2626; /* Subtle red color */
    font-weight: 500;
}

/* Filter section styling */
.filter-section {
    margin-bottom: 1rem;
}

/* Filter pills styling */
.filter-pill {
    display: inline-flex;
    align-items: center;
    background-color: #F3F4F6;
    color: #4B5563;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.filter-pill .remove-filter {
    margin-left: 0.25rem;
    color: #9CA3AF;
    cursor: pointer;
    transition: color 0.2s ease;
}

.filter-pill .remove-filter:hover {
    color: #EF4444;
}

/* Status badge styling */
.status-badge {
    display: inline-block;
    padding: 0.125rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: capitalize;
}

.status-badge.available {
    background-color: #D1FAE5;
    color: #065F46;
}

.status-badge.active {
    background-color: #DBEAFE;
    color: #1E40AF;
}

.status-badge.inactive {
    background-color: #FEE2E2;
    color: #991B1B;
}

.status-badge.used {
    background-color: #FEF3C7;
    color: #92400E;
}

/* Table styling */
.table-container {
    overflow-x: auto;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th {
    background-color: #F9FAFB;
    font-weight: 600;
    text-align: left;
    padding: 0.75rem 1rem;
    color: #374151;
    border-bottom: 1px solid #E5E7EB;
}

.data-table td {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #E5E7EB;
    color: #4B5563;
}

.data-table tr:last-child td {
    border-bottom: none;
}

.data-table tr:hover {
    background-color: #F9FAFB;
}

/* Action buttons */
.action-btn {
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-btn.edit {
    background-color: #EEF2FF;
    color: #4F46E5;
}

.action-btn.edit:hover {
    background-color: #E0E7FF;
}

.action-btn.delete {
    background-color: #FEE2E2;
    color: #DC2626;
}

.action-btn.delete:hover {
    background-color: #FEE2E2;
}

/* Pagination styling */
.pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
}

.pagination-info {
    color: #6B7280;
    font-size: 0.875rem;
}

.pagination-controls {
    display: flex;
    align-items: center;
}

.pagination-btn {
    padding: 0.375rem 0.75rem;
    border-radius: 0.25rem;
    background-color: #F9FAFB;
    color: #4B5563;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    margin: 0 0.25rem;
}

.pagination-btn:hover {
    background-color: #F3F4F6;
}

.pagination-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-btn.active {
    background-color: #4F46E5;
    color: white;
}

/* Mobile responsive styles */
@media (max-width: 768px) {
    .filter-section {
        flex-direction: column;
    }
    
    .filter-section > div {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .data-table th, 
    .data-table td {
        padding: 0.5rem;
    }
    
    .pagination {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .pagination-info {
        margin-bottom: 0.5rem;
    }
}
