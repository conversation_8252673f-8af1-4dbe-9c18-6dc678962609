/* Admin Profile Specific Styles */
.profile-card {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
    overflow: hidden;
    margin-bottom: 1rem;
}

.profile-header {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #e5e7eb;
    background: linear-gradient(to right, #eef2ff, white);
    display: flex;
    align-items: center;
}

.profile-header h2 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #1f2937;
    margin-left: 0.5rem;
}

.profile-header i {
    color: #4f46e5;
    font-size: 1rem;
}

.profile-content {
    padding: 1.5rem;
}

.profile-avatar {
    width: 5.5rem;
    height: 5.5rem;
    border-radius: 9999px;
    background-color: #eef2ff;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    border: 3px solid white;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.profile-avatar i {
    font-size: 2rem;
    color: #4f46e5;
}

.profile-name {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.profile-role {
    font-size: 0.9rem;
    color: #6b7280;
    margin-top: 0.25rem;
    margin-bottom: 1.25rem;
}

.profile-info-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f3f4f6;
}

.profile-info-item:last-child {
    border-bottom: none;
}

.profile-info-icon {
    width: 2rem;
    text-align: center;
    color: #4f46e5;
    font-size: 1rem;
}

.profile-info-content {
    margin-left: 0.75rem;
}

.profile-info-label {
    font-size: 0.8rem;
    color: #6b7280;
    margin-bottom: 0.25rem;
}

.profile-info-value {
    font-size: 1rem;
    font-weight: 500;
    color: #1f2937;
}

.profile-badge {
    display: inline-block;
    padding: 0.125rem 0.375rem;
    background-color: #eef2ff;
    color: #4f46e5;
    border-radius: 9999px;
    font-size: 0.65rem;
    font-weight: 500;
}

/* Password Form Styles */
.password-form {
    display: grid;
    gap: 1rem;
}

.form-group {
    margin-bottom: 1rem;
}

.form-label {
    display: block;
    font-size: 0.9rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
}

.form-input-wrapper {
    position: relative;
}

.form-input-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
    pointer-events: none;
    font-size: 0.9rem;
}

.form-input {
    width: 100%;
    padding: 0.75rem 2.5rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.95rem;
    color: #1f2937;
    transition: border-color 0.2s, box-shadow 0.2s;
}

.form-input:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.1);
}

.form-input-toggle {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
    cursor: pointer;
    font-size: 0.9rem;
}

.form-hint {
    font-size: 0.8rem;
    color: #6b7280;
    margin-top: 0.375rem;
}

.form-divider {
    border-top: 1px solid #f3f4f6;
    padding-top: 1rem;
}

.form-submit {
    display: flex;
    justify-content: flex-end;
    margin-top: 0.5rem;
}

.btn-primary {
    display: inline-flex;
    align-items: center;
    padding: 0.75rem 1.25rem;
    background-color: #4f46e5;
    color: white;
    border-radius: 0.375rem;
    font-size: 0.95rem;
    font-weight: 500;
    transition: background-color 0.2s, box-shadow 0.2s;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    border: none;
    cursor: pointer;
}

.btn-primary:hover {
    background-color: #4338ca;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.btn-primary i {
    margin-right: 0.5rem;
    font-size: 0.95rem;
}

/* Activity Log Styles */
.activity-table {
    min-width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    font-size: 0.8rem;
}

.activity-table th {
    padding: 0.5rem 0.75rem;
    text-align: left;
    font-size: 0.7rem;
    font-weight: 500;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.025em;
    background-color: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
}

.activity-table td {
    padding: 0.625rem 0.75rem;
    border-bottom: 1px solid #f3f4f6;
}

.activity-table tr:hover {
    background-color: #f9fafb;
}

.activity-icon {
    width: 1.5rem;
    height: 1.5rem;
    border-radius: 9999px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.5rem;
}

.activity-icon i {
    font-size: 0.7rem;
}

.activity-icon.login {
    background-color: #d1fae5;
}

.activity-icon.login i {
    color: #10b981;
}

.activity-icon.password {
    background-color: #fef3c7;
}

.activity-icon.password i {
    color: #f59e0b;
}

.activity-icon.add {
    background-color: #e0e7ff;
}

.activity-icon.add i {
    color: #4f46e5;
}

.activity-icon.delete {
    background-color: #fee2e2;
}

.activity-icon.delete i {
    color: #ef4444;
}

.activity-icon.edit {
    background-color: #dbeafe;
}

.activity-icon.edit i {
    color: #3b82f6;
}

.activity-icon.default {
    background-color: #f3f4f6;
}

.activity-icon.default i {
    color: #6b7280;
}

.activity-action {
    font-weight: 500;
    color: #1f2937;
    font-size: 0.8rem;
}

.activity-details {
    font-size: 0.75rem;
    color: #6b7280;
}

.activity-time {
    font-size: 0.75rem;
    color: #6b7280;
    display: flex;
    align-items: center;
    white-space: nowrap;
}

.activity-time i {
    margin-right: 0.375rem;
    color: #9ca3af;
    font-size: 0.7rem;
}

.empty-state {
    padding: 2rem 1rem;
    text-align: center;
}

.empty-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 3rem;
    height: 3rem;
    border-radius: 9999px;
    background-color: #dbeafe;
    color: #3b82f6;
    margin-bottom: 0.75rem;
}

.empty-icon i {
    font-size: 1.125rem;
}

.empty-title {
    font-size: 0.95rem;
    font-weight: 500;
    color: #1f2937;
    margin-bottom: 0.375rem;
}

.empty-message {
    color: #6b7280;
    font-size: 0.8rem;
    max-width: 20rem;
    margin: 0 auto;
}

/* Responsive Grid Layout */
.grid-container {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1.25rem;
    max-width: 1200px;
    margin: 0 auto;
}

@media (min-width: 1024px) {
    .grid-container {
        grid-template-columns: minmax(350px, 1fr) minmax(500px, 2fr);
    }
}

/* Fix for flex alignment */
.flex {
    display: flex;
}

.flex-col {
    flex-direction: column;
}

.items-center {
    align-items: center;
}

.justify-center {
    justify-content: center;
}

.space-y-4 > * + * {
    margin-top: 1rem;
}

.mb-6 {
    margin-bottom: 1.5rem;
}

.mb-8 {
    margin-bottom: 2rem;
}

.ml-auto {
    margin-left: auto;
}
