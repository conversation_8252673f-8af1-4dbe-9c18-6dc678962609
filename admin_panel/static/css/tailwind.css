/* Custom styles beyond Tailwind */

:root {
    --primary-color: #4f46e5;
    --primary-hover: #4338ca;
    --secondary-color: #4f46e5;
    --secondary-hover: #4338ca;
    --success-color: #10b981;
    --success-hover: #059669;
    --danger-color: #ef4444;
    --danger-hover: #dc2626;
    --warning-color: #f59e0b;
    --warning-hover: #d97706;
    --sidebar-bg: #1e293b;
    --sidebar-hover: #334155;
    --sidebar-active: #4f46e5;
    --main-bg: #f8fafc;
    --card-bg: #ffffff;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    --border-color: #e2e8f0;
    --info-bg: #eef2ff;
    --info-color: #4f46e5;
}

/* Global styles */
body {
    background-color: var(--main-bg);
    color: var(--text-primary);
    font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Improved card styling */
.card {
    background-color: var(--card-bg);
    border-radius: 0.75rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    transition: all 0.2s ease;
}

.card:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transform: translateY(-2px);
}

/* Enhanced button styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    font-weight: 500;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
    cursor: pointer;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-hover);
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(79, 70, 229, 0.2);
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: white;
}

.btn-secondary:hover {
    background-color: var(--secondary-hover);
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(14, 165, 233, 0.2);
}

.btn-success {
    background-color: var(--success-color);
    color: white;
}

.btn-success:hover {
    background-color: var(--success-hover);
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(16, 185, 129, 0.2);
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background-color: var(--danger-hover);
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(239, 68, 68, 0.2);
}

/* Fade out animation for flash messages */
.flash-message {
    transition: all 0.3s ease-in-out;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    margin-bottom: 1rem;
}

.flash-message.success {
    background-color: rgba(16, 185, 129, 0.1);
    border-left: 4px solid var(--success-color);
    color: #065f46;
}

.flash-message.error {
    background-color: rgba(239, 68, 68, 0.1);
    border-left: 4px solid var(--danger-color);
    color: #991b1b;
}

.flash-message.info {
    background-color: rgba(79, 70, 229, 0.1);
    border-left: 4px solid var(--primary-color);
    color: #4338ca;
}

/* Custom scrollbar for tables */
.overflow-x-auto::-webkit-scrollbar {
    height: 8px;
}

.overflow-x-auto::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
    background: #94a3b8;
    border-radius: 4px;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
    background: #64748b;
}

/* Enhanced table styles */
table {
    border-collapse: separate;
    border-spacing: 0;
    width: 100%;
}

table th {
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.05em;
    color: var(--text-secondary);
    background-color: #f8fafc;
    padding: 0.75rem 1rem;
}

table td {
    padding: 0.75rem 1rem;
    vertical-align: middle;
    border-top: 1px solid var(--border-color);
}

table tbody tr:hover {
    background-color: rgba(243, 244, 246, 0.5);
}

/* Sidebar styles */
#sidebar {
    width: 256px;
    min-width: 64px;
    background-color: var(--sidebar-bg);
    box-shadow: 4px 0 10px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease-in-out;
}

#sidebar .sidebar-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    color: rgba(255, 255, 255, 0.7);
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
}

#sidebar .sidebar-link:hover {
    background-color: var(--sidebar-hover);
    color: white;
}

#sidebar .sidebar-link.active {
    background-color: rgba(79, 70, 229, 0.1);
    color: white;
    border-left: 3px solid var(--sidebar-active);
}

#sidebar .sidebar-link i {
    width: 1.5rem;
    text-align: center;
    margin-right: 0.75rem;
    font-size: 1.1rem;
}

#mainContent {
    margin-left: 256px;
    transition: margin-left 0.3s ease-in-out;
}

.sidebar-text {
    transition: opacity 0.3s ease-in-out;
}

/* Form controls */
.form-control {
    display: block;
    width: 100%;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    line-height: 1.5;
    color: var(--text-primary);
    background-color: white;
    background-clip: padding-box;
    border: 1px solid var(--border-color);
    border-radius: 0.375rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    border-color: var(--primary-color);
    outline: 0;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.25);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    #sidebar {
        width: 70% !important;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
        position: fixed;
        z-index: 100;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
    }

    #sidebar.mobile-open {
        transform: translateX(0);
    }

    #mainContent {
        margin-left: 0 !important;
        width: 100%;
        padding-bottom: 60px; /* Add space for mobile navigation */
    }

    .sidebar-text {
        display: block !important;
    }

    #sidebar.expanded {
        width: 256px;
    }

    #sidebar.expanded .sidebar-text {
        display: block;
    }

    /* Fix for mobile text overflow */
    p, h1, h2, h3, h4, h5, h6, span, div, td, th {
        word-wrap: break-word;
        overflow-wrap: break-word;
    }

    /* Ensure tables don't cause overflow */
    table {
        table-layout: fixed;
        width: 100%;
    }

    /* Ensure content containers don't overflow */
    .card, .rounded-xl, .bg-white {
        max-width: 100%;
    }

    /* Improve mobile card styling */
    .card {
        padding: 0.75rem !important;
    }

    /* Improve section spacing on mobile */
    .mb-8 {
        margin-bottom: 1.5rem !important;
    }

    /* Improve font sizes for better readability on mobile */
    .text-2xl {
        font-size: 1.25rem !important;
        line-height: 1.75rem !important;
    }

    .text-lg {
        font-size: 1.125rem !important;
        line-height: 1.5rem !important;
    }

    /* Improve mobile card view */
    .divide-y > div {
        padding: 1rem 0.75rem;
    }

    /* Improve spacing in mobile cards */
    .p-4 {
        padding: 0.75rem !important;
    }

    /* Ensure proper spacing between sections */
    .rounded-xl {
        margin-bottom: 1.5rem;
    }

    /* Improve mobile pagination */
    .pagination {
        flex-wrap: wrap;
        justify-content: center;
        padding: 0.5rem;
    }

    /* Improve mobile buttons */
    button, .button, a.button {
        padding: 0.5rem 0.75rem !important;
        font-size: 0.875rem !important;
    }

    /* Improve mobile form elements */
    input, select, textarea {
        font-size: 16px !important; /* Prevents iOS zoom on focus */
        padding: 0.5rem 0.75rem !important;
    }

    /* Improve mobile card spacing */
    .gap-4, .gap-6 {
        gap: 0.75rem !important;
    }

    /* Improve mobile section headers */
    .bg-gradient-to-r {
        padding: 0.75rem !important;
    }

    /* Adjust header for mobile */
    header h1 {
        font-size: 1.25rem !important;
        line-height: 1.5rem !important;
        margin-right: 0.5rem;
    }

    /* Make search input responsive */
    #globalSearch {
        width: 100% !important;
    }

    /* Fix horizontal scrolling issues */
    .overflow-x-auto {
        max-width: 100vw;
        margin-left: 0;
        margin-right: 0;
        padding-left: 0;
        padding-right: 0;
    }

    /* Adjust spacing for mobile */
    .px-6 {
        padding-left: 1rem !important;
        padding-right: 1rem !important;
    }

    /* Ensure flex items wrap properly on mobile */
    .flex {
        flex-wrap: wrap;
    }

    /* Fix for notification icon on mobile */
    .space-x-6 > * + * {
        margin-left: 0.75rem !important;
    }
}