/* Mobile-specific styles for support requests page */
@media (max-width: 768px) {
    /* Main container adjustments */
    #mainContent {
        padding: 0.5rem;
    }

    /* Card container */
    .bg-white.rounded-xl {
        border-radius: 0.5rem;
        margin-bottom: 1rem;
    }

    /* Header section with title */
    .bg-white.rounded-xl .flex.justify-between {
        flex-wrap: wrap;
        gap: 0.75rem;
        padding: 0.75rem !important;
    }

    /* Table to card transformation */
    .min-w-full.divide-y thead {
        display: none;
    }

    .min-w-full.divide-y tbody {
        display: block;
        padding: 0.5rem;
    }

    .min-w-full.divide-y tbody tr {
        display: block;
        margin-bottom: 1rem;
        border-radius: 0.5rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        border: 1px solid #e5e7eb;
        position: relative;
        padding: 1rem;
        background-color: white;
    }

    .min-w-full.divide-y tbody tr td {
        display: block;
        padding: 0.5rem 0;
        border: none;
        text-align: left;
    }

    /* Style the request ID */
    .min-w-full.divide-y tbody tr td:nth-child(1) {
        font-size: 1rem;
        font-weight: 600;
        padding-top: 0;
        padding-bottom: 0.25rem;
        color: #4f46e5;
    }

    /* Style the user name */
    .min-w-full.divide-y tbody tr td:nth-child(2) {
        padding-top: 0.25rem;
        padding-bottom: 0.25rem;
        font-weight: 500;
    }

    /* Style the user email */
    .min-w-full.divide-y tbody tr td:nth-child(3) {
        padding-top: 0.25rem;
        padding-bottom: 0.25rem;
    }

    /* Style the status */
    .min-w-full.divide-y tbody tr td:nth-child(4) {
        position: absolute;
        top: 1rem;
        right: 1rem;
        padding: 0;
    }

    /* Style the created at */
    .min-w-full.divide-y tbody tr td:nth-child(5) {
        padding-top: 0.25rem;
        padding-bottom: 0.25rem;
        font-size: 0.75rem;
        color: #6b7280;
    }

    /* Style the actions */
    .min-w-full.divide-y tbody tr td:nth-child(6) {
        padding-top: 0.75rem;
        border-top: 1px solid #f3f4f6;
        margin-top: 0.5rem;
    }

    /* Add data labels for context */
    .min-w-full.divide-y tbody tr td:nth-child(2)::before {
        content: "Name:";
        font-weight: 500;
        color: #6b7280;
        display: inline-block;
        width: 4rem;
        font-size: 0.75rem;
        text-transform: uppercase;
    }

    .min-w-full.divide-y tbody tr td:nth-child(3)::before {
        content: "Email:";
        font-weight: 500;
        color: #6b7280;
        display: inline-block;
        width: 4rem;
        font-size: 0.75rem;
        text-transform: uppercase;
    }

    .min-w-full.divide-y tbody tr td:nth-child(5)::before {
        content: "Created:";
        font-weight: 500;
        color: #6b7280;
        display: inline-block;
        width: 4rem;
        font-size: 0.75rem;
        text-transform: uppercase;
    }

    /* Adjust status badges */
    .min-w-full.divide-y tbody tr td:nth-child(4) span {
        font-size: 0.65rem;
        padding: 0.25rem 0.5rem;
        white-space: nowrap;
    }

    /* Style the view details button */
    .min-w-full.divide-y tbody tr td:nth-child(6) button {
        width: 100%;
        padding: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f3f4f6;
        border-radius: 0.375rem;
        transition: all 0.2s ease;
    }

    .min-w-full.divide-y tbody tr td:nth-child(6) button:hover {
        background-color: #e5e7eb;
    }

    /* Empty state styling */
    .p-8 .flex.flex-col.items-center.justify-center {
        padding: 2rem 1rem;
    }

    /* Modal adjustments for mobile */
    #requestDetailsModal .bg-white.rounded-xl {
        width: calc(100% - 2rem) !important;
        max-width: 100% !important;
        margin: 1rem;
        max-height: calc(100vh - 2rem) !important;
    }

    /* Improve modal scrolling */
    #requestDetailsModal .max-h-\\[90vh\\] {
        max-height: calc(100vh - 2rem) !important;
    }

    /* Modal header */
    #requestDetailsModal .px-6.py-4.border-b {
        padding: 0.75rem !important;
    }

    /* Modal content */
    #requestDetailsModal .px-6.py-4 {
        padding: 0.75rem !important;
    }

    /* Loading indicator */
    #requestDetailsModal #messagesList .flex.justify-center.items-center.h-20 {
        height: 100px !important;
    }

    #requestDetailsModal #messagesList .fa-spinner {
        font-size: 2rem !important;
    }

    /* Improve modal section spacing */
    #requestDetailsModal .mb-6 {
        margin-bottom: 1rem !important;
    }

    /* Improve modal section headers */
    #requestDetailsModal .flex.items-center.mb-3 {
        margin-bottom: 0.5rem !important;
    }

    /* Improve modal section content */
    #requestDetailsModal .bg-gray-50.p-4 {
        padding: 0.75rem !important;
        border-radius: 0.375rem;
    }

    /* Grid layout in modal */
    #requestDetailsModal .grid.grid-cols-1.md\\:grid-cols-2 {
        grid-template-columns: 1fr !important;
        gap: 0.5rem !important;
    }

    /* Messages list */
    #requestDetailsModal #messagesList {
        max-height: 200px !important;
    }

    /* Message items */
    #requestDetailsModal #messagesList > div {
        padding: 0.75rem !important;
        margin-bottom: 0.5rem;
    }

    #requestDetailsModal #messagesList > div .flex.justify-between {
        flex-direction: column;
        align-items: flex-start;
    }

    #requestDetailsModal #messagesList > div .text-xs.text-gray-500 {
        margin-top: 0.25rem;
        font-size: 0.65rem !important;
    }

    /* Notification form */
    #requestDetailsModal #sendNotificationForm .space-y-4 {
        margin-top: 0.5rem;
    }

    /* Notification response */
    #requestDetailsModal #notificationResponse {
        padding: 0.75rem !important;
        margin-bottom: 0.75rem !important;
    }

    #requestDetailsModal #sendNotificationForm label {
        margin-bottom: 0.25rem !important;
    }

    #requestDetailsModal #sendNotificationForm select,
    #requestDetailsModal #sendNotificationForm textarea {
        font-size: 16px !important; /* Prevents iOS zoom on focus */
    }

    /* Notification template */
    #requestDetailsModal .bg-white.p-3.border {
        padding: 0.5rem !important;
        font-size: 0.875rem !important;
        overflow-x: auto;
    }

    /* Template content */
    #requestDetailsModal .bg-white.p-3.border p {
        margin-bottom: 0.25rem;
        word-break: break-word;
    }

    /* Modal footer */
    #requestDetailsModal .px-6.py-4.border-t {
        padding: 0.75rem !important;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    #requestDetailsModal .px-6.py-4.border-t button {
        flex: 1;
        min-width: 120px;
        padding: 0.5rem !important;
        font-size: 0.875rem !important;
        white-space: nowrap;
    }

    /* Add a subtle hover effect to the cards */
    .min-w-full.divide-y tbody tr:hover {
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        transform: translateY(-2px);
        transition: all 0.2s ease;
    }

    /* Add active state for cards */
    .min-w-full.divide-y tbody tr:active {
        background-color: rgba(79, 70, 229, 0.05);
    }

    /* Ensure the table container doesn't have horizontal scroll */
    .overflow-x-auto {
        overflow-x: visible;
    }
}
