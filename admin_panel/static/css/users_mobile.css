/* Mobile-specific styles for users page */
@media (max-width: 768px) {
    /* Main container adjustments */
    #mainContent {
        padding: 0.5rem;
    }

    /* Card container */
    .bg-white.rounded-xl {
        border-radius: 0.5rem;
        margin-bottom: 1rem;
    }
    /* Header section with title and export button */
    .bg-white.rounded-xl .flex.justify-between {
        flex-wrap: wrap;
        gap: 0.75rem;
    }

    .bg-white.rounded-xl .flex.justify-between .flex.space-x-2 {
        margin-left: auto;
    }

    /* Filter section */
    .bg-gray-50 form.flex.flex-wrap {
        flex-direction: column;
        gap: 0.75rem;
        padding: 0.75rem;
    }

    .bg-gray-50 form.flex.flex-wrap .flex-1 {
        width: 100%;
    }

    /* Search input styling */
    .bg-gray-50 form.flex.flex-wrap .flex-1 input {
        width: 100%;
        padding: 0.75rem 0.75rem 0.75rem 2.5rem;
        font-size: 0.9rem;
        border-radius: 0.5rem;
    }

    /* Search icon positioning */
    .bg-gray-50 form.flex.flex-wrap .flex-1 .absolute {
        left: 0.75rem;
    }

    /* Filter dropdowns and button row */
    .bg-gray-50 form.flex.flex-wrap .w-auto {
        width: 100%;
        display: flex;
    }

    .bg-gray-50 form.flex.flex-wrap .w-auto select,
    .bg-gray-50 form.flex.flex-wrap .w-auto button {
        flex: 1;
        padding: 0.75rem !important;
        font-size: 0.9rem !important;
        height: 2.75rem;
    }

    /* Filter buttons row - horizontal layout */
    .bg-gray-50 form.flex.flex-wrap .w-auto:nth-child(2),
    .bg-gray-50 form.flex.flex-wrap .w-auto:nth-child(3),
    .bg-gray-50 form.flex.flex-wrap .w-auto:nth-child(4) {
        display: inline-flex;
        margin-right: 0.5rem;
    }

    /* Table to card transformation */
    .min-w-full.divide-y thead {
        display: none;
    }

    .min-w-full.divide-y tbody {
        display: block;
        padding: 0.5rem;
    }

    .min-w-full.divide-y tbody tr {
        display: block;
        margin-bottom: 1rem;
        border-radius: 0.5rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        border: 1px solid #e5e7eb;
        position: relative;
        padding: 1rem;
        background-color: white;
    }

    .min-w-full.divide-y tbody tr td {
        display: block;
        padding: 0.5rem 0;
        border: none;
        text-align: left;
        position: relative;
    }

    /* Hide the actions column on mobile */
    .min-w-full.divide-y tbody tr td:last-child {
        position: absolute;
        top: 1rem;
        right: 1rem;
        padding: 0;
    }

    /* Style the user ID */
    .min-w-full.divide-y tbody tr td:first-child {
        font-size: 1.125rem;
        font-weight: 600;
        padding-top: 0;
        padding-right: 2rem; /* Make space for the action button */
    }

    /* Style the user ID link */
    .min-w-full.divide-y tbody tr td:first-child a {
        color: #4f46e5;
        text-decoration: none;
    }

    /* Style the name */
    .min-w-full.divide-y tbody tr td:nth-child(2) {
        padding-top: 0.25rem;
        padding-bottom: 0.5rem;
    }

    /* Style the contact info */
    .min-w-full.divide-y tbody tr td:nth-child(3) {
        padding-top: 0.5rem;
        padding-bottom: 0.5rem;
    }

    /* Style the access code */
    .min-w-full.divide-y tbody tr td:nth-child(4) {
        padding-top: 0.5rem;
        padding-bottom: 0.5rem;
    }

    /* Style the status */
    .min-w-full.divide-y tbody tr td:nth-child(5) {
        position: absolute;
        top: 1rem;
        right: 3.5rem;
        padding: 0;
    }

    /* Ensure status badges are properly sized and positioned */
    .min-w-full.divide-y tbody tr td:nth-child(5) span {
        font-size: 0.65rem;
        padding: 0.25rem 0.5rem;
        display: inline-block;
    }

    /* Style the experience level */
    .min-w-full.divide-y tbody tr td:nth-child(6) {
        padding-top: 0.5rem;
        padding-bottom: 0;
        margin-top: 0.5rem;
    }

    /* Add data labels for context */
    .min-w-full.divide-y tbody tr td:nth-child(3)::before {
        content: "Contact: ";
        font-weight: 500;
        color: #6b7280;
        display: block;
        font-size: 0.75rem;
        text-transform: uppercase;
        margin-bottom: 0.25rem;
    }

    .min-w-full.divide-y tbody tr td:nth-child(4)::before {
        content: "Access Code: ";
        font-weight: 500;
        color: #6b7280;
        display: block;
        font-size: 0.75rem;
        text-transform: uppercase;
        margin-bottom: 0.25rem;
    }

    .min-w-full.divide-y tbody tr td:nth-child(6)::before {
        content: "Experience: ";
        font-weight: 500;
        color: #6b7280;
        display: block;
        font-size: 0.75rem;
        text-transform: uppercase;
        margin-bottom: 0.25rem;
    }

    /* Ensure email and whatsapp info is properly displayed */
    .min-w-full.divide-y tbody tr td:nth-child(3) .text-sm {
        display: block;
    }

    .min-w-full.divide-y tbody tr td:nth-child(3) .flex.items-center {
        display: flex;
        align-items: center;
    }

    /* Adjust status badges */
    .min-w-full.divide-y tbody tr td:nth-child(5) span,
    .min-w-full.divide-y tbody tr td:nth-child(6) span {
        font-size: 0.65rem;
        padding: 0.25rem 0.5rem;
        white-space: nowrap;
    }

    /* Pagination section */
    .px-6.py-4.bg-gray-50.border-t.flex {
        flex-direction: column;
        gap: 1rem;
        align-items: center;
        padding: 1rem !important;
    }

    .px-6.py-4.bg-gray-50.border-t.flex .text-sm.text-gray-500 {
        text-align: center;
        width: 100%;
        font-size: 0.875rem;
    }

    .px-6.py-4.bg-gray-50.border-t.flex .flex.space-x-1 {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 0.5rem;
    }

    /* Pagination buttons */
    .px-6.py-4.bg-gray-50.border-t.flex .flex.space-x-1 a,
    .px-6.py-4.bg-gray-50.border-t.flex .flex.space-x-1 span {
        min-width: 2.5rem;
        height: 2.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* No users found section */
    .px-6.py-12.text-center {
        padding: 2rem 1rem;
    }

    /* Ensure proper spacing in the card */
    .min-w-full.divide-y tbody tr td .flex.items-center.mb-1:last-child {
        margin-bottom: 0;
    }

    /* Fix for text overflow in cards */
    .min-w-full.divide-y tbody tr td span {
        word-break: break-word;
        overflow-wrap: break-word;
    }

    /* Ensure proper spacing between cards */
    .min-w-full.divide-y tbody tr + tr {
        margin-top: 1rem;
    }

    /* Ensure the table container doesn't have horizontal scroll */
    .overflow-x-auto {
        overflow-x: visible;
    }

    /* Add a subtle hover effect to the cards */
    .min-w-full.divide-y tbody tr:hover {
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        transform: translateY(-2px);
        transition: all 0.2s ease;
    }
}
