/* Mobile-specific styles for telegram bot users page */
@media (max-width: 768px) {
    /* Main container adjustments */
    #mainContent {
        padding: 0.5rem;
    }

    /* Card container */
    .bg-white.rounded-xl {
        border-radius: 0.5rem;
        margin-bottom: 1rem;
    }

    /* Header section with title and back button */
    .px-6.py-4.border-b.border-gray-200.flex.justify-between {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
        padding: 0.75rem !important;
    }

    /* Bot name and username */
    .px-6.py-4.border-b.border-gray-200.flex.justify-between .flex.items-center {
        width: 100%;
    }

    .px-6.py-4.border-b.border-gray-200.flex.justify-between .flex.items-center h2 {
        font-size: 1.25rem;
        line-height: 1.5rem;
        margin-right: 0.5rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: calc(100% - 2rem);
    }

    .px-6.py-4.border-b.border-gray-200.flex.justify-between .flex.items-center p {
        font-size: 0.875rem;
    }

    /* Back button */
    .px-6.py-4.border-b.border-gray-200.flex.justify-between a {
        align-self: flex-start;
        margin-top: 0.5rem;
        width: 100%;
        text-align: center;
        padding: 0.5rem 0;
    }

    /* Search section */
    .px-6.py-3.bg-gray-50 {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.75rem;
        padding: 0.75rem !important;
    }

    /* User count text */
    .px-6.py-3.bg-gray-50 .text-sm.text-gray-600 {
        width: 100%;
        text-align: center;
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
    }

    .px-6.py-3.bg-gray-50 .flex.space-x-2 {
        width: 100%;
    }

    .px-6.py-3.bg-gray-50 .relative.max-w-xs {
        width: 100%;
        max-width: 100%;
    }

    .px-6.py-3.bg-gray-50 input {
        width: 100%;
        padding: 0.75rem 0.75rem 0.75rem 2.5rem;
        font-size: 0.9rem;
    }

    /* Table to card transformation */
    .min-w-full.divide-y thead {
        display: none;
    }

    .min-w-full.divide-y tbody {
        display: block;
        padding: 0.5rem;
    }

    .min-w-full.divide-y tbody tr {
        display: block;
        margin-bottom: 1rem;
        border-radius: 0.5rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        border: 1px solid #e5e7eb;
        position: relative;
        padding: 1rem;
        background-color: white;
    }

    .min-w-full.divide-y tbody tr td {
        display: block;
        padding: 0.5rem 0;
        border: none;
        text-align: left;
        white-space: normal;
    }

    /* Style the user ID */
    .min-w-full.divide-y tbody tr td:nth-child(1) {
        font-size: 1.125rem;
        font-weight: 600;
        padding-top: 0;
        padding-bottom: 0.25rem;
    }

    /* Style the name */
    .min-w-full.divide-y tbody tr td:nth-child(2) {
        padding-top: 0.25rem;
        padding-bottom: 0.25rem;
    }

    /* Style the access code */
    .min-w-full.divide-y tbody tr td:nth-child(3) {
        padding-top: 0.25rem;
        padding-bottom: 0.25rem;
        font-family: monospace;
    }

    /* Style the status */
    .min-w-full.divide-y tbody tr td:nth-child(4) {
        position: absolute;
        top: 1rem;
        right: 1rem;
        padding: 0;
    }

    /* Style the actions */
    .min-w-full.divide-y tbody tr td:nth-child(5) {
        padding-top: 0.75rem;
        border-top: 1px solid #f3f4f6;
        margin-top: 0.5rem;
        text-align: center;
    }

    .min-w-full.divide-y tbody tr td:nth-child(5) a {
        display: inline-block;
        padding: 0.5rem 1rem;
        background-color: #f3f4f6;
        border-radius: 0.375rem;
        transition: all 0.2s ease;
        width: 100%;
        text-align: center;
    }

    .min-w-full.divide-y tbody tr td:nth-child(5) a:hover {
        background-color: #e5e7eb;
    }

    .min-w-full.divide-y tbody tr td:nth-child(5) a i {
        margin-right: 0.5rem;
    }

    /* Add data labels for context */
    .min-w-full.divide-y tbody tr td:nth-child(2)::before {
        content: "Name:";
        font-weight: 500;
        color: #6b7280;
        display: inline-block;
        width: 5rem;
        font-size: 0.75rem;
        text-transform: uppercase;
    }

    .min-w-full.divide-y tbody tr td:nth-child(3)::before {
        content: "Access Code:";
        font-weight: 500;
        color: #6b7280;
        display: inline-block;
        width: 5rem;
        font-size: 0.75rem;
        text-transform: uppercase;
    }

    /* Adjust status badges */
    .min-w-full.divide-y tbody tr td:nth-child(4) span {
        font-size: 0.65rem;
        padding: 0.25rem 0.5rem;
        white-space: nowrap;
    }

    /* Empty state styling */
    .px-6.py-12.text-center {
        padding: 2rem 1rem;
    }

    /* Add a subtle hover effect to the cards */
    .min-w-full.divide-y tbody tr:hover {
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        transform: translateY(-2px);
        transition: all 0.2s ease;
    }

    /* Add active state for cards */
    .min-w-full.divide-y tbody tr:active {
        background-color: rgba(79, 70, 229, 0.05);
    }

    /* Ensure the table container doesn't have horizontal scroll */
    .overflow-x-auto {
        overflow-x: visible;
    }

    /* Modify the view button to be more descriptive */
    .min-w-full.divide-y tbody tr td:nth-child(5) a::after {
        content: " View Details";
    }
}
