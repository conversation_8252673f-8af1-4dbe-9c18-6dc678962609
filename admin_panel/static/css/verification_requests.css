/* Desktop-only scrollable table styles (min-width: 769px) */
@media (min-width: 769px) {
    .overflow-x-auto {
        position: relative;
        width: 100%;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }

    /* Add shadow to indicate scrollable content */
    .overflow-x-auto::after {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        width: 5px;
        background: linear-gradient(to right, rgba(0,0,0,0), rgba(0,0,0,0.05));
        pointer-events: none;
    }

    /* Ensure table takes full width */
    .overflow-x-auto table {
        width: 100%;
        min-width: 1000px; /* Ensures horizontal scrolling on desktop */
    }

    /* Sticky header styles */
    .overflow-x-auto th {
        position: sticky;
        top: 0;
        z-index: 10;
        background-color: #f9fafb; /* bg-gray-50 */
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    }
}

/* Mobile styles for back button */
@media (max-width: 640px) {
    .back-button-text {
        display: none;
    }

    .back-button {
        padding: 0.5rem;
        border-radius: 9999px;
    }

    .back-button i {
        margin-right: 0;
    }
}

/* Status badge styling */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-badge.pending {
    background-color: #FEF3C7;
    color: #92400E;
}

.status-badge.approved {
    background-color: #D1FAE5;
    color: #065F46;
}

.status-badge.denied {
    background-color: #FEE2E2;
    color: #991B1B;
}

/* Table styling */
.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table th {
    text-align: left;
    padding: 0.75rem 1rem;
    font-weight: 600;
    color: #374151;
    background-color: #F9FAFB;
    border-bottom: 1px solid #E5E7EB;
}

.data-table td {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #E5E7EB;
    color: #4B5563;
}

.data-table tr:hover {
    background-color: #F9FAFB;
}

/* Modal styling */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 50;
    backdrop-filter: blur(4px);
}

.modal-container {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    overflow: hidden;
    max-width: 28rem;
    width: 100%;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #E5E7EB;
}

.modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #111827;
}

.modal-close-button {
    background: transparent;
    border: none;
    color: #6B7280;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 9999px;
    transition: all 0.2s;
}

.modal-close-button:hover {
    background-color: #F3F4F6;
    color: #1F2937;
}

.modal-body {
    padding: 1.5rem;
    overflow-y: auto;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    padding: 1rem 1.5rem;
    border-top: 1px solid #E5E7EB;
    gap: 0.5rem;
}

/* Button styling */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-weight: 500;
    transition: all 0.2s;
    cursor: pointer;
}

.btn-primary {
    background-color: #4F46E5;
    color: white;
    border: none;
}

.btn-primary:hover {
    background-color: #4338CA;
}

.btn-secondary {
    background-color: white;
    color: #4B5563;
    border: 1px solid #D1D5DB;
}

.btn-secondary:hover {
    background-color: #F9FAFB;
}

.btn-danger {
    background-color: #EF4444;
    color: white;
    border: none;
}

.btn-danger:hover {
    background-color: #DC2626;
}

/* Form styling */
.form-group {
    margin-bottom: 1rem;
}

.form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
}

.form-control {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #D1D5DB;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    transition: all 0.2s;
}

.form-control:focus {
    outline: none;
    border-color: #4F46E5;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

/* Pagination styling */
.pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
}

.pagination-info {
    color: #6B7280;
    font-size: 0.875rem;
}

.pagination-controls {
    display: flex;
    align-items: center;
}

.pagination-btn {
    padding: 0.375rem 0.75rem;
    border-radius: 0.25rem;
    background-color: #F9FAFB;
    color: #4B5563;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    margin: 0 0.25rem;
}

.pagination-btn:hover {
    background-color: #F3F4F6;
}

.pagination-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-btn.active {
    background-color: #4F46E5;
    color: white;
}

/* Mobile responsive styles */
@media (max-width: 768px) {
    .data-table th,
    .data-table td {
        padding: 0.5rem;
    }

    .pagination {
        flex-direction: column;
        align-items: flex-start;
    }

    .pagination-info {
        margin-bottom: 0.5rem;
    }

    .modal-container {
        max-width: 90%;
    }
}
