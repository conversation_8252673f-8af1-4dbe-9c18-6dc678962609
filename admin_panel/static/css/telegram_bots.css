/* Token display styling */
.token-display {
    position: relative;
    display: flex;
    align-items: center;
}

.token-mask {
    font-family: monospace;
    letter-spacing: 1px;
}

.toggle-token {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 0.25rem;
    transition: background-color 0.2s;
}

.toggle-token:hover {
    background-color: rgba(79, 70, 229, 0.1);
}

/* Status badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
}

.status-active {
    background-color: rgba(16, 185, 129, 0.1);
    color: rgb(6, 95, 70);
}

.status-inactive {
    background-color: rgba(239, 68, 68, 0.1);
    color: rgb(153, 27, 27);
}

/* Bot card for mobile view */
@media (max-width: 768px) {
    .bot-card {
        border: 1px solid #e5e7eb;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1rem;
        background-color: white;
    }

    .bot-card-header {
        display: flex;
        align-items: center;
        margin-bottom: 0.75rem;
    }

    .bot-avatar {
        width: 2.5rem;
        height: 2.5rem;
        border-radius: 9999px;
        background-color: rgba(59, 130, 246, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 0.75rem;
    }

    .bot-info {
        flex: 1;
    }

    .bot-name {
        font-weight: 600;
        font-size: 1rem;
        color: #1f2937;
    }

    .bot-username {
        font-size: 0.875rem;
        color: #6b7280;
    }

    .bot-card-content {
        margin-top: 0.75rem;
        padding-top: 0.75rem;
        border-top: 1px solid #e5e7eb;
    }

    .bot-card-footer {
        display: flex;
        justify-content: space-between;
        margin-top: 1rem;
        padding-top: 0.75rem;
        border-top: 1px solid #e5e7eb;
    }
}

/* Modal styles */
.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: flex-start;
    justify-content: center;
    padding-top: 5rem;
    z-index: 50;
    overflow-y: auto;
}

.modal-content {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    width: 100%;
    max-width: 28rem;
    padding: 1.5rem;
    position: relative;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
}

.modal-close {
    background: none;
    border: none;
    color: #9ca3af;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 0.25rem;
    transition: background-color 0.2s;
}

.modal-close:hover {
    background-color: rgba(156, 163, 175, 0.1);
    color: #6b7280;
}

.modal-body {
    margin-bottom: 1.5rem;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
}

/* Form styles */
.form-group {
    margin-bottom: 1rem;
}

.form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
}

.form-input {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    line-height: 1.25rem;
    color: #1f2937;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.25);
}

.form-input-group {
    display: flex;
}

.form-input-addon {
    display: inline-flex;
    align-items: center;
    padding: 0 0.75rem;
    font-size: 0.875rem;
    font-weight: 400;
    line-height: 1.25rem;
    color: #6b7280;
    text-align: center;
    white-space: nowrap;
    background-color: #f3f4f6;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.form-input-group .form-input {
    flex: 1 1 0%;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.form-checkbox {
    display: flex;
    align-items: center;
}

.form-checkbox-input {
    height: 1rem;
    width: 1rem;
    color: #3b82f6;
    border-radius: 0.25rem;
    border: 1px solid #d1d5db;
}

.form-checkbox-input:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.25);
}

.form-checkbox-label {
    margin-left: 0.5rem;
    font-size: 0.875rem;
    color: #1f2937;
}

.form-help-text {
    margin-top: 0.25rem;
    font-size: 0.75rem;
    color: #6b7280;
}

/* Button styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    line-height: 1.25rem;
    border-radius: 0.375rem;
    transition: all 0.15s ease-in-out;
    cursor: pointer;
}

.btn-primary {
    background-color: #4f46e5;
    color: white;
    border: 1px solid transparent;
}

.btn-primary:hover {
    background-color: #4338ca;
}

.btn-secondary {
    background-color: #f3f4f6;
    color: #1f2937;
    border: 1px solid #d1d5db;
}

.btn-secondary:hover {
    background-color: #e5e7eb;
}

.btn-danger {
    background-color: #ef4444;
    color: white;
    border: 1px solid transparent;
}

.btn-danger:hover {
    background-color: #dc2626;
}

.btn-icon {
    margin-right: 0.5rem;
}

/* Test result styles */
.test-result {
    text-align: center;
}

.test-result-icon {
    margin: 0 auto;
    width: 3rem;
    height: 3rem;
    border-radius: 9999px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
}

.test-result-icon-success {
    background-color: rgba(16, 185, 129, 0.1);
    color: rgb(6, 95, 70);
}

.test-result-icon-error {
    background-color: rgba(239, 68, 68, 0.1);
    color: rgb(153, 27, 27);
}

.test-result-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.test-result-message {
    font-size: 0.875rem;
    color: #6b7280;
    margin-bottom: 1rem;
}

.test-result-details {
    background-color: #f9fafb;
    border-radius: 0.375rem;
    padding: 0.75rem;
    text-align: left;
}

.test-result-detail {
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.test-result-detail:last-child {
    margin-bottom: 0;
}

/* Loading spinner */
.spinner {
    border: 0.25rem solid rgba(156, 163, 175, 0.25);
    border-top: 0.25rem solid #4f46e5;
    border-radius: 50%;
    width: 2rem;
    height: 2rem;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
