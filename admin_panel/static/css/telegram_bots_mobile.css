/* Mobile-specific styles for telegram bots page */
@media (max-width: 768px) {
    /* Main container adjustments */
    #mainContent {
        padding: 0.5rem;
    }

    /* Card container */
    .bg-white.rounded-xl {
        border-radius: 0.5rem;
        margin-bottom: 1rem;
    }

    /* Header section with title and add button */
    .bg-white.rounded-xl .flex.justify-between {
        flex-wrap: wrap;
        gap: 0.75rem;
        padding: 0.75rem !important;
    }

    .bg-white.rounded-xl .flex.justify-between button {
        margin-left: auto;
        font-size: 0.875rem !important;
        padding: 0.5rem 0.75rem !important;
    }

    /* Search section */
    .px-6.py-3.bg-gray-50 {
        padding: 0.75rem !important;
    }

    .px-6.py-3.bg-gray-50 .flex.justify-between {
        flex-direction: column;
        gap: 0.75rem;
        align-items: flex-start;
    }

    .px-6.py-3.bg-gray-50 .flex.justify-between .text-sm {
        width: 100%;
    }

    .px-6.py-3.bg-gray-50 .flex.justify-between .flex.space-x-2 {
        width: 100%;
    }

    .px-6.py-3.bg-gray-50 .flex.justify-between .flex.space-x-2 .relative {
        width: 100%;
    }

    .px-6.py-3.bg-gray-50 .flex.justify-between .flex.space-x-2 input {
        width: 100%;
        padding: 0.75rem 0.75rem 0.75rem 2.5rem;
        font-size: 0.9rem;
    }

    /* Table to card transformation */
    .min-w-full.divide-y thead {
        display: none;
    }

    .min-w-full.divide-y tbody {
        display: block;
        padding: 0.5rem;
    }

    .min-w-full.divide-y tbody tr {
        display: block;
        margin-bottom: 1rem;
        border-radius: 0.5rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        border: 1px solid #e5e7eb;
        position: relative;
        padding: 1rem;
        background-color: white;
    }

    .min-w-full.divide-y tbody tr td {
        display: block;
        padding: 0.5rem 0;
        border: none;
        text-align: left;
    }

    /* Style the bot name and avatar */
    .min-w-full.divide-y tbody tr td:first-child {
        padding-top: 0;
        padding-bottom: 0.75rem;
        border-bottom: 1px solid #f3f4f6;
        margin-bottom: 0.5rem;
    }

    .min-w-full.divide-y tbody tr td:first-child .flex.items-center {
        display: flex;
        align-items: center;
    }

    /* Style the username */
    .min-w-full.divide-y tbody tr td:nth-child(2) {
        padding-top: 0.5rem;
        padding-bottom: 0.5rem;
    }

    /* Style the token */
    .min-w-full.divide-y tbody tr td:nth-child(3) {
        padding-top: 0.5rem;
        padding-bottom: 0.5rem;
    }

    /* Style the status */
    .min-w-full.divide-y tbody tr td:nth-child(4) {
        position: absolute;
        top: 1rem;
        right: 1rem;
        padding: 0;
    }

    /* Style the actions */
    .min-w-full.divide-y tbody tr td:nth-child(5) {
        padding-top: 0.75rem;
        border-top: 1px solid #f3f4f6;
        margin-top: 0.5rem;
    }

    .min-w-full.divide-y tbody tr td:nth-child(5) .flex.space-x-2 {
        justify-content: flex-start;
        gap: 0.75rem;
    }

    .min-w-full.divide-y tbody tr td:nth-child(5) .flex.space-x-2 button,
    .min-w-full.divide-y tbody tr td:nth-child(5) .flex.space-x-2 a {
        padding: 0.5rem !important;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 2.5rem;
        height: 2.5rem;
    }

    /* Add tooltips for action buttons on mobile */
    .min-w-full.divide-y tbody tr td:nth-child(5) .flex.space-x-2 button[title]::after,
    .min-w-full.divide-y tbody tr td:nth-child(5) .flex.space-x-2 a[title]::after {
        content: attr(title);
        position: absolute;
        bottom: -1.5rem;
        left: 50%;
        transform: translateX(-50%);
        font-size: 0.65rem;
        white-space: nowrap;
        background-color: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        opacity: 0;
        transition: opacity 0.2s ease;
        pointer-events: none;
    }

    .min-w-full.divide-y tbody tr td:nth-child(5) .flex.space-x-2 button:active[title]::after,
    .min-w-full.divide-y tbody tr td:nth-child(5) .flex.space-x-2 a:active[title]::after {
        opacity: 1;
    }

    /* Add data labels for context */
    .min-w-full.divide-y tbody tr td:nth-child(2)::before {
        content: "Username";
        font-weight: 500;
        color: #6b7280;
        display: block;
        font-size: 0.75rem;
        text-transform: uppercase;
        margin-bottom: 0.25rem;
    }

    .min-w-full.divide-y tbody tr td:nth-child(3)::before {
        content: "Token";
        font-weight: 500;
        color: #6b7280;
        display: block;
        font-size: 0.75rem;
        text-transform: uppercase;
        margin-bottom: 0.25rem;
    }

    .min-w-full.divide-y tbody tr td:nth-child(5)::before {
        content: "Actions";
        font-weight: 500;
        color: #6b7280;
        display: block;
        font-size: 0.75rem;
        text-transform: uppercase;
        margin-bottom: 0.25rem;
    }

    /* Adjust token display for mobile */
    .min-w-full.divide-y tbody tr td:nth-child(3) .token-display {
        width: 100%;
    }

    .min-w-full.divide-y tbody tr td:nth-child(3) .token-display .flex.items-center {
        width: 100%;
        overflow: hidden;
    }

    .min-w-full.divide-y tbody tr td:nth-child(3) .token-mask {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        flex: 1;
        font-size: 0.75rem;
    }

    /* Adjust status badges */
    .min-w-full.divide-y tbody tr td:nth-child(4) span {
        font-size: 0.65rem;
        padding: 0.25rem 0.5rem;
        white-space: nowrap;
    }

    /* Empty state styling */
    .px-6.py-12.text-center {
        padding: 2rem 1rem;
    }

    .px-6.py-12.text-center button {
        margin: 0 auto;
        font-size: 0.875rem !important;
        padding: 0.5rem 0.75rem !important;
    }

    /* Modal adjustments for mobile */
    .fixed.inset-0 .relative.top-20.mx-auto {
        top: 10px;
        margin: 0.5rem;
        width: calc(100% - 1rem);
        max-height: calc(100vh - 1rem);
        overflow-y: auto;
    }

    /* Improve modal animations on mobile */
    .fixed.inset-0 .transform.transition-all {
        transition: all 0.2s ease-out;
    }

    /* Improve modal header spacing */
    .fixed.inset-0 .flex.justify-between.items-center.mb-5 {
        margin-bottom: 1rem;
    }

    /* Improve modal form spacing */
    .fixed.inset-0 .space-y-5 {
        margin-bottom: 1rem;
    }

    .fixed.inset-0 .relative.top-20.mx-auto .p-6 {
        padding: 1rem;
    }

    /* Form fields in modals */
    .fixed.inset-0 input,
    .fixed.inset-0 select {
        font-size: 16px !important; /* Prevents iOS zoom on focus */
    }

    /* Modal buttons */
    .fixed.inset-0 .flex.justify-end.space-x-3 button {
        padding: 0.5rem 0.75rem !important;
        font-size: 0.875rem !important;
    }

    /* Add a subtle hover effect to the cards */
    .min-w-full.divide-y tbody tr:hover {
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        transform: translateY(-2px);
        transition: all 0.2s ease;
    }

    /* Add active state for cards */
    .min-w-full.divide-y tbody tr:active {
        background-color: rgba(79, 70, 229, 0.05);
    }

    /* Improve empty state on mobile */
    .px-6.py-12.text-center .inline-flex.items-center.justify-center {
        margin: 0 auto 1rem auto;
    }

    .px-6.py-12.text-center .text-lg {
        font-size: 1.125rem;
        line-height: 1.5rem;
    }

    .px-6.py-12.text-center .text-gray-500 {
        font-size: 0.875rem;
        line-height: 1.25rem;
        margin-bottom: 1.5rem;
    }

    /* Ensure the table container doesn't have horizontal scroll on mobile */
    .overflow-x-auto {
        overflow-x: visible !important;
        min-width: auto !important;
    }

    /* Reset any desktop-specific table styles */
    .overflow-x-auto table {
        min-width: auto !important;
        width: 100% !important;
    }

    /* Remove sticky headers on mobile */
    .overflow-x-auto th {
        position: static !important;
        top: auto !important;
        z-index: auto !important;
        box-shadow: none !important;
    }
}
