/* Mobile-specific styles for access codes page */
@media (max-width: 768px) {
    /* Action buttons container */
    .access-codes-header .flex.space-x-3 {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
        width: 100%;
    }

    /* Make buttons wrap and take equal width */
    .access-codes-header .flex.space-x-3 button {
        flex: 1 1 auto;
        min-width: 120px;
        margin: 0 !important;
        justify-content: center;
        padding: 0.5rem 0.75rem !important;
        font-size: 0.875rem !important;
    }

    /* Search and counter layout */
    .access-codes-search-container {
        flex-direction: column;
        align-items: flex-start !important;
    }

    .access-codes-search-container .relative {
        width: 100%;
        max-width: 100%;
    }

    .access-codes-search-container .flex.flex-col.md\:flex-row {
        width: 100%;
        justify-content: flex-end;
        margin-top: 0.75rem;
    }

    /* Style the search input */
    #searchInput {
        width: 100%;
        padding: 0.875rem 1rem 0.875rem 2.5rem;
        font-size: 1rem;
        border-radius: 0.5rem;
    }

    /* Style the search icon */
    #searchInput + div .fas.fa-search {
        left: 1rem;
    }

    /* Style the total codes counter */
    .access-codes-search-container .text-sm.text-gray-600 {
        font-size: 0.9rem;
        font-weight: 500;
        text-align: right;
        width: 100%;
        margin-top: 0.5rem;
        padding-right: 0.5rem;
    }

    /* Table to card transformation */
    .overflow-x-auto {
        overflow-x: visible;
    }

    /* Hide table header on mobile */
    .access-codes-table thead {
        display: none;
    }

    /* Make table body display as block */
    .access-codes-table tbody {
        display: block;
        padding: 0.5rem;
    }

    /* Style each row as a card */
    .access-codes-table tbody tr {
        display: grid;
        grid-template-columns: 2.5rem 1fr;
        margin-bottom: 1rem;
        border-radius: 0.5rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        border: 1px solid #e5e7eb;
        position: relative;
        padding: 1rem;
        background-color: white;
    }

    /* Style each cell as a block element */
    .access-codes-table tbody td {
        display: block;
        padding: 0.5rem 0;
        border: none;
        text-align: left;
        white-space: normal;
    }

    /* Style the checkbox column */
    .access-codes-table tbody td:first-child {
        grid-column: 1;
        grid-row: 1;
        padding: 0;
        z-index: 10; /* Higher z-index to ensure it's above other elements */
        width: 2rem;
        height: 2rem;
        display: flex;
        align-items: flex-start;
        justify-content: center;
        background-color: transparent;
        pointer-events: auto; /* Ensure checkbox is clickable */
        margin-top: 0.25rem;
    }

    /* Style the checkbox itself */
    .access-codes-table tbody td:first-child input[type="checkbox"] {
        width: 1.25rem;
        height: 1.25rem;
        position: relative;
        z-index: 20; /* Even higher z-index */
        margin: 0;
        opacity: 1 !important;
        cursor: pointer;
    }

    /* Style the access code column */
    .access-codes-table tbody td:nth-child(2) {
        grid-column: 2;
        grid-row: 1;
        font-size: 1.125rem;
        font-weight: 600;
        padding-top: 0;
        padding-left: 0; /* No need for padding as we're using grid */
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        position: relative; /* Ensure proper stacking context */
        min-height: 2rem; /* Ensure minimum height for proper alignment */
    }

    /* Style the access code link */
    .access-codes-table tbody td:nth-child(2) a {
        max-width: calc(100% - 5rem); /* Reduced width to avoid overlap with status */
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        position: relative; /* Ensure proper stacking context */
        z-index: 1;
    }

    /* Style the status column */
    .access-codes-table tbody td:nth-child(3) {
        position: absolute;
        top: 1rem;
        right: 1rem;
        padding: 0;
        grid-column: 2;
        grid-row: 1;
        justify-self: end;
    }

    /* Style the user ID column */
    .access-codes-table tbody td:nth-child(4) {
        padding-top: 0.5rem;
        padding-bottom: 0.25rem;
        grid-column: 1 / span 2;
        grid-row: 2;
    }

    /* Style the subscription date column */
    .access-codes-table tbody td:nth-child(5) {
        padding-top: 0.25rem;
        padding-bottom: 0.25rem;
        font-size: 0.875rem;
        grid-column: 1 / span 2;
        grid-row: 3;
    }

    /* Style the expiration column */
    .access-codes-table tbody td:nth-child(6) {
        padding-top: 0.25rem;
        padding-bottom: 0.5rem;
        grid-column: 1 / span 2;
        grid-row: 4;
    }

    /* Style the actions column */
    .access-codes-table tbody td:nth-child(7) {
        padding-top: 0.75rem;
        border-top: 1px solid #f3f4f6;
        margin-top: 0.5rem;
        grid-column: 1 / span 2;
        grid-row: 5;
    }

    /* Add data labels for context */
    .access-codes-table tbody td:nth-child(4)::before {
        content: "User ID:";
        font-weight: 500;
        color: #6b7280;
        display: inline-block;
        width: 5rem;
        font-size: 0.75rem;
        text-transform: uppercase;
    }

    .access-codes-table tbody td:nth-child(5)::before {
        content: "Subscribed:";
        font-weight: 500;
        color: #6b7280;
        display: inline-block;
        width: 5rem;
        font-size: 0.75rem;
        text-transform: uppercase;
    }

    .access-codes-table tbody td:nth-child(6)::before {
        content: "Expires:";
        font-weight: 500;
        color: #6b7280;
        display: inline-block;
        width: 5rem;
        font-size: 0.75rem;
        text-transform: uppercase;
    }

    /* Style the status tags */
    .access-codes-table .inline-flex.items-center.text-xs {
        padding: 0.25rem 0.5rem;
        font-size: 0.65rem;
        white-space: nowrap;
    }

    /* Style the action buttons */
    .access-codes-table td:nth-child(7) .flex {
        display: flex;
        justify-content: space-between;
        width: 100%;
    }

    .access-codes-table td:nth-child(7) button {
        padding: 0.5rem !important;
        flex: 1;
        margin: 0 0.25rem;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* Add tooltips for action buttons on mobile */
    .access-codes-table td:nth-child(7) button[title]::after {
        content: attr(title);
        position: absolute;
        bottom: -1.5rem;
        left: 50%;
        transform: translateX(-50%);
        font-size: 0.65rem;
        white-space: nowrap;
        background-color: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        opacity: 0;
        transition: opacity 0.2s ease;
        pointer-events: none;
    }

    .access-codes-table td:nth-child(7) button:active[title]::after {
        opacity: 1;
    }

    /* Add hover effect to cards */
    .access-codes-table tbody tr:hover {
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        transform: translateY(-2px);
        transition: all 0.2s ease;
    }

    /* Add active state for cards */
    .access-codes-table tbody tr:active {
        background-color: rgba(79, 70, 229, 0.05);
    }

    /* Style the pagination container */
    .access-codes-pagination {
        flex-direction: column;
        gap: 1rem;
        align-items: center;
    }

    .access-codes-pagination > div:first-child {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 0.5rem;
    }

    /* Style the pagination controls */
    .access-codes-pagination > div:last-child {
        display: flex;
        width: 100%;
        justify-content: space-between;
    }

    /* Make the pagination buttons more touch-friendly */
    .access-codes-pagination a {
        padding: 0.5rem 0.75rem;
        min-width: 2.5rem;
        text-align: center;
    }

    /* Style the per page selector */
    .access-codes-pagination select {
        width: 100%;
        max-width: 12rem;
        margin: 0 auto;
    }

    /* Style the empty state for better mobile display */
    .access-codes-empty {
        padding: 2rem 1rem;
    }

    .access-codes-empty .flex.justify-center.space-x-4 {
        flex-direction: column;
        gap: 0.75rem;
    }

    .access-codes-empty .flex.justify-center.space-x-4 button {
        width: 100%;
        margin: 0 !important;
        padding: 0.75rem 1rem !important;
    }

    /* Modal adjustments for mobile */
    #addCodeModal .bg-white,
    #uploadFileModal .bg-white,
    #codeDetailsModal .bg-white,
    #editCodeModal .bg-white,
    #deleteCodeModal .bg-white,
    #deleteConfirmModal .bg-white {
        width: calc(100% - 2rem) !important;
        max-width: 100% !important;
        margin: 1rem;
        max-height: calc(100vh - 2rem) !important;
        overflow-y: auto;
    }

    /* Modal header */
    #addCodeModal .p-6,
    #uploadFileModal .p-6,
    #codeDetailsModal .p-6,
    #editCodeModal .p-6,
    #deleteCodeModal .p-6,
    #deleteConfirmModal .p-6 {
        padding: 1rem !important;
    }

    /* Modal form fields */
    #addCodeModal input,
    #uploadFileModal input,
    #editCodeModal input,
    #addCodeModal select,
    #uploadFileModal select,
    #editCodeModal select {
        font-size: 16px !important; /* Prevents iOS zoom on focus */
    }

    /* Modal buttons */
    #addCodeModal .flex.justify-end.space-x-3 button,
    #uploadFileModal .flex.justify-end.space-x-3 button,
    #codeDetailsModal .flex.justify-end button,
    #editCodeModal .flex.justify-end.space-x-3 button,
    #deleteCodeModal .flex.justify-end.space-x-3 button,
    #deleteConfirmModal .flex.justify-end.space-x-3 button {
        padding: 0.5rem 0.75rem !important;
        font-size: 0.875rem !important;
    }

    /* Fix for the search input placeholder text */
    #searchInput::placeholder {
        font-size: 0.9rem;
    }

    /* Adjust the info text below search */
    .access-codes-search-container .text-xs.text-gray-500 {
        font-size: 0.75rem;
    }
}
