/* Mobile-specific styles for verification requests page */
@media (max-width: 768px) {
    /* Main container adjustments */
    #mainContent {
        padding: 0.5rem;
    }

    /* Card container */
    .bg-white.rounded-xl {
        border-radius: 0.5rem;
        margin-bottom: 1rem;
    }

    /* Header section with title */
    .bg-white.rounded-xl .flex.justify-between {
        flex-wrap: wrap;
        gap: 0.75rem;
        padding: 0.75rem !important;
    }

    /* Table to card transformation */
    .min-w-full.divide-y thead {
        display: none;
    }

    .min-w-full.divide-y tbody {
        display: block;
        padding: 0.5rem;
    }

    .min-w-full.divide-y tbody tr {
        display: block;
        margin-bottom: 1rem;
        border-radius: 0.5rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        border: 1px solid #e5e7eb;
        position: relative;
        padding: 1rem;
        background-color: white;
    }

    /* Special styling for rows with access code issues */
    .min-w-full.divide-y tbody tr.border-l-4.border-red-400 {
        border-left-width: 4px;
        border-left-color: #f87171;
    }

    .min-w-full.divide-y tbody tr td {
        display: block;
        padding: 0.5rem 0;
        border: none;
        text-align: left;
    }

    /* Style the request ID */
    .min-w-full.divide-y tbody tr td:nth-child(1) {
        font-size: 1rem;
        font-weight: 600;
        padding-top: 0;
        padding-bottom: 0.25rem;
        color: #4f46e5;
    }

    /* Style the user name */
    .min-w-full.divide-y tbody tr td:nth-child(2) {
        padding-top: 0.25rem;
        padding-bottom: 0.25rem;
        font-weight: 500;
    }

    /* Style the user email */
    .min-w-full.divide-y tbody tr td:nth-child(3) {
        padding-top: 0.25rem;
        padding-bottom: 0.25rem;
    }

    /* Style the WhatsApp */
    .min-w-full.divide-y tbody tr td:nth-child(4) {
        padding-top: 0.25rem;
        padding-bottom: 0.25rem;
    }

    /* Style the access code */
    .min-w-full.divide-y tbody tr td:nth-child(5) {
        padding-top: 0.25rem;
        padding-bottom: 0.25rem;
    }

    /* Style the status */
    .min-w-full.divide-y tbody tr td:nth-child(6) {
        position: absolute;
        top: 1rem;
        right: 1rem;
        padding: 0;
    }

    /* Style the created at */
    .min-w-full.divide-y tbody tr td:nth-child(7) {
        padding-top: 0.25rem;
        padding-bottom: 0.25rem;
        font-size: 0.75rem;
        color: #6b7280;
    }

    /* Style the actions */
    .min-w-full.divide-y tbody tr td:nth-child(8) {
        padding-top: 0.75rem;
        border-top: 1px solid #f3f4f6;
        margin-top: 0.5rem;
    }

    /* Add data labels for context */
    .min-w-full.divide-y tbody tr td:nth-child(2)::before {
        content: "Name:";
        font-weight: 500;
        color: #6b7280;
        display: inline-block;
        width: 4rem;
        font-size: 0.75rem;
        text-transform: uppercase;
    }

    .min-w-full.divide-y tbody tr td:nth-child(3)::before {
        content: "Email:";
        font-weight: 500;
        color: #6b7280;
        display: inline-block;
        width: 4rem;
        font-size: 0.75rem;
        text-transform: uppercase;
    }

    .min-w-full.divide-y tbody tr td:nth-child(4)::before {
        content: "WhatsApp:";
        font-weight: 500;
        color: #6b7280;
        display: inline-block;
        width: 4rem;
        font-size: 0.75rem;
        text-transform: uppercase;
    }

    .min-w-full.divide-y tbody tr td:nth-child(5)::before {
        content: "Access Code:";
        font-weight: 500;
        color: #6b7280;
        display: inline-block;
        width: 4rem;
        font-size: 0.75rem;
        text-transform: uppercase;
    }

    .min-w-full.divide-y tbody tr td:nth-child(7)::before {
        content: "Created:";
        font-weight: 500;
        color: #6b7280;
        display: inline-block;
        width: 4rem;
        font-size: 0.75rem;
        text-transform: uppercase;
    }

    /* Adjust status badges */
    .min-w-full.divide-y tbody tr td:nth-child(6) span {
        font-size: 0.65rem;
        padding: 0.25rem 0.5rem;
        white-space: nowrap;
    }

    /* Style the view details button */
    .min-w-full.divide-y tbody tr td:nth-child(8) button {
        width: 100%;
        padding: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f3f4f6;
        border-radius: 0.375rem;
        transition: all 0.2s ease;
    }

    .min-w-full.divide-y tbody tr td:nth-child(8) button:hover {
        background-color: #e5e7eb;
    }

    /* Empty state styling */
    .p-8 .flex.flex-col.items-center.justify-center {
        padding: 2rem 1rem;
    }

    /* Modal adjustments for mobile */
    #requestDetailsModal .bg-white.rounded-xl {
        width: calc(100% - 2rem) !important;
        max-width: 100% !important;
        margin: 1rem;
        max-height: calc(100vh - 2rem) !important;
    }

    /* Improve modal scrolling */
    #requestDetailsModal .max-h-\[90vh\] {
        max-height: calc(100vh - 2rem) !important;
    }

    /* Improve modal loading indicator */
    #requestDetailsModal #loadingIndicator {
        padding: 2rem 1rem;
    }

    #requestDetailsModal #loadingIndicator .animate-spin {
        height: 3rem;
        width: 3rem;
    }

    /* Modal header */
    #requestDetailsModal .px-6.py-5 {
        padding: 0.75rem !important;
    }

    /* Modal content */
    #requestDetailsModal .px-6.py-6 {
        padding: 0.75rem !important;
    }

    /* Improve modal section spacing */
    #requestDetailsModal .space-y-6 > div {
        margin-bottom: 1.5rem;
    }

    /* Improve modal section headers */
    #requestDetailsModal .flex.items-center.mb-3.border-b {
        padding-bottom: 0.5rem;
        margin-bottom: 0.75rem;
    }

    /* Improve modal section content */
    #requestDetailsModal .bg-gray-50.p-5 {
        padding: 0.75rem !important;
        border-radius: 0.375rem;
    }

    /* Grid layout in modal */
    #requestDetailsModal .grid.grid-cols-1.md\:grid-cols-2 {
        grid-template-columns: 1fr !important;
        gap: 0.5rem !important;
    }

    /* Modal footer */
    #requestDetailsModal #requestActionButtons {
        padding: 0.75rem !important;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    #requestDetailsModal #requestActionButtons button,
    #requestDetailsModal #requestActionButtons form {
        flex: 1;
        min-width: 120px;
    }

    #requestDetailsModal #requestActionButtons button {
        padding: 0.5rem !important;
        font-size: 0.875rem !important;
        white-space: nowrap;
    }

    /* Improve admin notes section */
    #requestDetailsModal #adminNotesList {
        max-height: 150px !important;
    }

    #requestDetailsModal #adminNoteText {
        font-size: 16px !important; /* Prevents iOS zoom on focus */
    }

    /* Improve verification history section */
    #requestDetailsModal #historyList {
        max-height: 200px !important;
    }

    /* Improve warning messages */
    #requestDetailsModal .bg-red-50.border-l-4.border-red-400 {
        padding: 0.75rem !important;
        margin-bottom: 0.75rem !important;
        font-size: 0.875rem !important;
    }

    /* Confirmation modal */
    #confirmationModal .bg-white.rounded-lg {
        width: calc(100% - 2rem) !important;
        margin: 1rem;
    }

    #confirmationModal .px-6.py-4 {
        padding: 0.75rem !important;
    }

    #confirmationModal .px-6.py-3 {
        padding: 0.75rem !important;
    }

    /* Add a subtle hover effect to the cards */
    .min-w-full.divide-y tbody tr:hover {
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        transform: translateY(-2px);
        transition: all 0.2s ease;
    }

    /* Add active state for cards */
    .min-w-full.divide-y tbody tr:active {
        background-color: rgba(79, 70, 229, 0.05);
    }

    /* Ensure the table container doesn't have horizontal scroll on mobile */
    .overflow-x-auto {
        overflow-x: visible !important;
        min-width: auto !important;
    }

    /* Reset any desktop-specific table styles */
    .overflow-x-auto table {
        min-width: auto !important;
        width: 100% !important;
    }

    /* Remove sticky headers on mobile */
    .overflow-x-auto th {
        position: static !important;
        top: auto !important;
        z-index: auto !important;
        box-shadow: none !important;
    }
}
