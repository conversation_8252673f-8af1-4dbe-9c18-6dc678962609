"""
Test script to verify logging functionality.
"""
import logging
import os
import sys

# Add parent directory to path so we can import modules
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import logging setup
from utils.logging_setup import setup_logging
from config import Config

# Setup logging
setup_logging()

# Get a logger
logger = logging.getLogger(__name__)

def test_logging():
    """Test different log levels"""
    logger.debug("This is a DEBUG message")
    logger.info("This is an INFO message")
    logger.warning("This is a WARNING message")
    logger.error("This is an ERROR message")
    logger.critical("This is a CRITICAL message")
    
    # Print log file location
    print(f"\nLog file location: {Config.LOG_FILE}")
    print(f"Log level: {Config.LOG_LEVEL}")
    
    # Try to read the log file
    try:
        with open(Config.LOG_FILE, 'r') as f:
            last_lines = f.readlines()[-10:]  # Get last 10 lines
            print("\nLast 10 lines from log file:")
            for line in last_lines:
                print(line.strip())
    except Exception as e:
        print(f"Error reading log file: {e}")

if __name__ == "__main__":
    test_logging()
