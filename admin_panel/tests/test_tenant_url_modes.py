"""
Test script to verify tenant URL generation based on mode.
"""
import sys
import os
import json

# Add parent directory to path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import Config, config_data
from utils.url_helpers import tenant_url_for, get_tenant_prefix

# Mock Flask request context
class MockRequest:
    def __init__(self, host='localhost', path='/'):
        self.host = host
        self.path = path
        self.environ = {'tenant': 'test_tenant'}

class MockG:
    def __init__(self):
        self.tenant = 'test_tenant'

class MockSession:
    def __init__(self):
        self.data = {'tenant': 'test_tenant'}
    
    def get(self, key, default=None):
        return self.data.get(key, default)

# Mock Flask context
class MockFlaskContext:
    def __init__(self, host='localhost', path='/'):
        self.request = MockRequest(host, path)
        self.g = MockG()
        self.session = MockSession()
        
    def __enter__(self):
        # Set up global mocks
        import utils.url_helpers
        utils.url_helpers.request = self.request
        utils.url_helpers.g = self.g
        utils.url_helpers.session = self.session
        utils.url_helpers.has_request_context = lambda: True
        utils.url_helpers.has_app_context = lambda: True
        utils.url_helpers.url_for = lambda endpoint, **kwargs: f"/{endpoint}"
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        pass

def test_tenant_url_generation():
    """Test tenant URL generation based on mode."""
    print("Testing tenant URL generation based on mode...")
    
    # Test with test mode
    print("\nTesting with mode = 'test'")
    config_data['mode'] = 'test'
    
    with MockFlaskContext(host='localhost', path='/test_tenant/dashboard') as ctx:
        # Test tenant_url_for
        url = tenant_url_for('dashboard')
        print(f"tenant_url_for('dashboard') in test mode: {url}")
        
        # Test get_tenant_prefix
        prefix = get_tenant_prefix()
        print(f"get_tenant_prefix() in test mode: {prefix}")
    
    # Test with prod mode
    print("\nTesting with mode = 'prod'")
    config_data['mode'] = 'prod'
    
    with MockFlaskContext(host='test_tenant.localhost', path='/dashboard') as ctx:
        # Test tenant_url_for
        url = tenant_url_for('dashboard')
        print(f"tenant_url_for('dashboard') in prod mode: {url}")
        
        # Test get_tenant_prefix
        prefix = get_tenant_prefix()
        print(f"get_tenant_prefix() in prod mode: {prefix}")
    
    # Reset mode to original value
    with open(os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'config.json'), 'r') as f:
        original_config = json.load(f)
    
    config_data['mode'] = original_config.get('mode', 'test')
    print(f"\nReset mode to: {config_data['mode']}")

if __name__ == '__main__':
    test_tenant_url_generation()
