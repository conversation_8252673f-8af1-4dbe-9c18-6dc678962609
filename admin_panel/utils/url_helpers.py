"""
URL helper functions for tenant-aware URL generation.
"""
import logging
from flask import g, session, url_for, request, has_request_context, has_app_context
from config import Config

logger = logging.getLogger(__name__)

def tenant_url_for(endpoint, **values):
    """
    Generate a URL with the tenant prefix based on the application mode.

    In test mode: localhost/tenant_name/
    In prod mode: tenant_name.localhost/

    Args:
        endpoint: The endpoint for url_for
        **values: Additional values for url_for

    Returns:
        str: URL with tenant prefix according to the current mode
    """
    # Skip tenant prefix for static files and welcome page
    if endpoint == 'static' or endpoint == 'welcome':
        return url_for(endpoint, **values)

    # Get tenant from request.environ first (most reliable), then g or session
    tenant = None
    try:
        if has_request_context():
            tenant = request.environ.get('tenant')

        if not tenant and has_app_context():
            tenant_from_g = getattr(g, 'tenant', None)
            tenant_from_session = session.get('tenant') if has_request_context() else None
            tenant = tenant_from_g or tenant_from_session
    except RuntimeError:
        # Handle case where app context is not available
        pass

    # If no tenant, just return the regular URL
    if not tenant:
        return url_for(endpoint, **values)

    # Get the application mode
    mode = Config.get_mode()

    # Log the current mode and tenant for debugging
    logger.debug(f"tenant_url_for: mode={mode}, tenant={tenant}, endpoint={endpoint}")

    # Generate the URL based on the mode
    if mode == "prod":
        # Check if we're already using subdomain routing
        using_subdomain = False
        if has_request_context():
            host = request.host
            if '.' in host and host.split('.')[0] == tenant:
                using_subdomain = True
                logger.debug(f"Already using subdomain routing: {host}")

        # If we're already using subdomain routing, just use the regular URL without tenant prefix
        if using_subdomain:
            url = url_for(endpoint, **values)

            # Make sure the URL doesn't have the tenant prefix
            if url.startswith(f"/{tenant}/"):
                # Remove tenant prefix
                url = url[len(f"/{tenant}"):]
                logger.debug(f"Removed tenant prefix from URL: {url}")

            logger.debug(f"Using URL with existing subdomain: {url}")
            return url

        # FALLBACK: If subdomain routing isn't working yet, use path-based routing
        # This ensures the application keeps working during the transition
        url = url_for(endpoint, **values)

        # Add tenant prefix if available
        if url.startswith('/'):
            # Skip if URL already has tenant prefix
            parts = url.lstrip('/').split('/', 1)
            if parts and parts[0] == tenant:
                return url

            # Skip if URL is for static files or welcome page
            if parts and (parts[0] == 'static' or parts[0] == 'welcome'):
                return url

            tenant_url = f"/{tenant}{url}"
            logger.debug(f"Fallback: Using path-based URL in prod mode: {tenant_url}")
            return tenant_url
    else:
        # In test mode, use path format: localhost/tenant_name/
        # Generate the URL
        url = url_for(endpoint, **values)

        # Add tenant prefix if available
        if url.startswith('/'):
            # Skip if URL already has tenant prefix
            parts = url.lstrip('/').split('/', 1)
            if parts and parts[0] == tenant:
                return url

            # Skip if URL is for static files or welcome page
            if parts and (parts[0] == 'static' or parts[0] == 'welcome'):
                return url

            tenant_url = f"/{tenant}{url}"
            logger.debug(f"Test mode tenant URL generated: {tenant_url} (tenant: {tenant}, original: {url})")
            return tenant_url

    return url

def get_tenant_prefix():
    """
    Get the tenant prefix for URLs based on the application mode.

    In test mode: /tenant_name/
    In prod mode: / (since the tenant is in the subdomain)

    Returns:
        str: Tenant prefix (with leading and trailing slashes)
    """
    # Get tenant from request.environ first (most reliable), then g or session
    tenant = None
    try:
        if has_request_context():
            tenant = request.environ.get('tenant')

        if not tenant and has_app_context():
            tenant_from_g = getattr(g, 'tenant', None)
            tenant_from_session = session.get('tenant') if has_request_context() else None
            tenant = tenant_from_g or tenant_from_session
    except RuntimeError:
        # Handle case where app context is not available
        pass

    # If no tenant, return root
    if not tenant:
        return "/"

    # Get the application mode
    mode = Config.get_mode()

    # Log the current mode and tenant for debugging
    logger.debug(f"get_tenant_prefix: mode={mode}, tenant={tenant}")

    # Return prefix based on mode
    if mode == "prod":
        # Check if we're already using subdomain routing
        using_subdomain = False
        if has_request_context():
            host = request.host
            if '.' in host and host.split('.')[0] == tenant:
                using_subdomain = True
                logger.debug(f"Already using subdomain routing: {host}")

        # If we're already using subdomain routing, no path prefix is needed
        if using_subdomain:
            logger.debug("Using empty prefix with existing subdomain")
            return "/"

        # FALLBACK: If subdomain routing isn't working yet, use path-based prefix
        # This ensures the application keeps working during the transition
        logger.debug(f"Fallback: Using path-based prefix in prod mode: /{tenant}/")
        return f"/{tenant}/"
    else:
        # In test mode, use path-based prefix
        return f"/{tenant}/"

def get_current_tenant():
    """
    Get the current tenant name.

    Returns:
        str: Current tenant name or None
    """
    # Get tenant from request.environ first (most reliable), then g or session
    tenant = None
    try:
        if has_request_context():
            tenant = request.environ.get('tenant')

        if not tenant and has_app_context():
            tenant_from_g = getattr(g, 'tenant', None)
            tenant_from_session = session.get('tenant') if has_request_context() else None
            tenant = tenant_from_g or tenant_from_session
    except RuntimeError:
        # Handle case where app context is not available
        pass

    return tenant
