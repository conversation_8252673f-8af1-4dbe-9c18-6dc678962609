"""
Tenant-specific session management for multi-tenant support.
This module provides a custom session interface that uses tenant-specific session cookies.
"""
import logging
from flask.sessions import SecureCookieSessionInterface
from flask import request, g, has_request_context
from utils.session_serializer import CustomSessionSerializer

logger = logging.getLogger(__name__)

class TenantSessionInterface(SecureCookieSessionInterface):
    """
    Custom session interface that uses tenant-specific session cookies.
    This allows multiple tenants to be logged in simultaneously in the same browser.
    """

    # Use custom session serializer
    serializer = CustomSessionSerializer()

    def get_cookie_name(self, app):
        """
        Get the session cookie name based on the tenant.

        Args:
            app: The Flask application

        Returns:
            str: The session cookie name
        """
        # Check if we're in a request context
        if not has_request_context():
            logger.debug(f"No request context, using default session cookie: {app.session_cookie_name}")
            return app.session_cookie_name

        # Get tenant from environ or g
        tenant = request.environ.get('tenant') or getattr(g, 'tenant', None)

        # Use tenant-specific session name if tenant is available
        if tenant:
            cookie_name = f"session_{tenant}"
            logger.debug(f"Using tenant-specific session cookie: {cookie_name} for tenant: {tenant}")
            return cookie_name

        # Fall back to default session name
        logger.debug(f"Using default session cookie: {app.session_cookie_name} (no tenant found)")
        return app.session_cookie_name

    def save_session(self, app, session, response):
        """
        Save the session to the response.

        Args:
            app: The Flask application
            session: The session to save
            response: The response to save the session to
        """
        # Check if we're in a request context
        if not has_request_context():
            super().save_session(app, session, response)
            return

        # Get tenant from environ or g
        tenant = request.environ.get('tenant') or getattr(g, 'tenant', None)

        # Make sure session is marked as modified to force save
        session.modified = True

        # Override cookie settings for tenant-specific cookies
        if tenant:
            # Store original cookie settings
            original_cookie_path = self.get_cookie_path(app)
            original_cookie_name = app.session_cookie_name
            original_session_cookie_domain = app.config.get('SESSION_COOKIE_DOMAIN')
            original_session_cookie_secure = app.config.get('SESSION_COOKIE_SECURE')
            original_session_cookie_samesite = app.config.get('SESSION_COOKIE_SAMESITE')

            # Set tenant-specific cookie path to ensure isolation
            # This ensures the cookie is only sent for requests to this tenant's paths
            tenant_cookie_path = f"/{tenant}"

            # Temporarily modify cookie settings
            app.session_cookie_name = f"session_{tenant}"
            app.config['SESSION_COOKIE_PATH'] = tenant_cookie_path

            # Ensure secure settings
            app.config['SESSION_COOKIE_SECURE'] = False  # Allow non-HTTPS in development
            app.config['SESSION_COOKIE_SAMESITE'] = None  # Default SameSite policy
            app.config['SESSION_COOKIE_DOMAIN'] = None  # Use default domain

            try:
                # Ensure admin_id is in the session if debug cookie exists
                debug_cookie_name = f"debug_{tenant}"
                if debug_cookie_name in request.cookies and 'admin_id' not in session:
                    debug_cookie_value = request.cookies.get(debug_cookie_name)
                    if debug_cookie_value.startswith('admin_id='):
                        admin_id = debug_cookie_value.replace('admin_id=', '')
                        logger.warning(f"Restoring admin_id from debug cookie: {admin_id}")
                        session['admin_id'] = admin_id
                        session.modified = True

                # Call parent method to save session with tenant-specific settings
                super().save_session(app, session, response)

                # Set or clear backup cookies
                if response.headers.get('Set-Cookie'):
                    # Set a backup cookie with the session data only if not logged out
                    if 'admin_id' in session and not session.get('logged_out'):
                        # Create a backup cookie with just the admin_id
                        backup_cookie_name = f"backup_{tenant}"
                        backup_cookie_value = f"admin_id={session['admin_id']}"
                        response.set_cookie(
                            backup_cookie_name,
                            backup_cookie_value,
                            path=tenant_cookie_path,
                            httponly=False,  # Allow JavaScript to read for debugging
                            max_age=86400     # 24 hour expiration
                        )
                    elif session.get('logged_out'):
                        # If logged out, clear any existing backup cookies
                        backup_cookie_name = f"backup_{tenant}"
                        debug_cookie_name = f"debug_{tenant}"
                        response.delete_cookie(backup_cookie_name, path=tenant_cookie_path)
                        response.delete_cookie(debug_cookie_name, path=tenant_cookie_path)
            finally:
                # Restore original cookie settings
                app.session_cookie_name = original_cookie_name
                app.config['SESSION_COOKIE_PATH'] = original_cookie_path
                app.config['SESSION_COOKIE_DOMAIN'] = original_session_cookie_domain
                app.config['SESSION_COOKIE_SECURE'] = original_session_cookie_secure
                app.config['SESSION_COOKIE_SAMESITE'] = original_session_cookie_samesite
        else:
            # Use default session save behavior for non-tenant requests
            super().save_session(app, session, response)

        # Return the response with the session cookie set

    def open_session(self, app, request):
        """
        Open the session from the request.

        Args:
            app: The Flask application
            request: The request to open the session from

        Returns:
            The session
        """
        # Get tenant from environ
        tenant = request.environ.get('tenant')

        # Get cookie name
        cookie_name = f"session_{tenant}" if tenant else app.session_cookie_name

        if tenant:
            # Store original cookie settings
            original_cookie_name = app.session_cookie_name
            original_cookie_path = app.config.get('SESSION_COOKIE_PATH')

            # Temporarily modify cookie settings
            app.session_cookie_name = cookie_name
            app.config['SESSION_COOKIE_PATH'] = f"/{tenant}"

            try:
                # Call parent method to open session with tenant-specific cookie settings
                session = super().open_session(app, request)

                # Process session after opening
                if session:
                    # Ensure tenant is in session
                    if 'tenant' not in session:
                        session['tenant'] = tenant

                    # Check if admin_id is missing but debug cookie exists
                    if 'admin_id' not in session and not session.get('logged_out'):
                        # Only restore from cookies if not explicitly logged out
                        # Check for debug cookie
                        debug_cookie_name = f"debug_{tenant}"
                        if debug_cookie_name in request.cookies:
                            debug_cookie_value = request.cookies.get(debug_cookie_name)
                            if debug_cookie_value.startswith('admin_id='):
                                admin_id = debug_cookie_value.replace('admin_id=', '')
                                session['admin_id'] = admin_id
                                session.modified = True

                        # Check for backup cookie
                        backup_cookie_name = f"backup_{tenant}"
                        if backup_cookie_name in request.cookies:
                            backup_cookie_value = request.cookies.get(backup_cookie_name)
                            if backup_cookie_value.startswith('admin_id='):
                                admin_id = backup_cookie_value.replace('admin_id=', '')
                                session['admin_id'] = admin_id
                                session.modified = True

                    # Force session to be marked as modified
                    session.modified = True
                else:
                    # Create a new session with tenant information
                    session = self.new_session(app)
                    session['tenant'] = tenant

                    # Only restore from cookies if not explicitly logged out
                    if not session.get('logged_out'):
                        # Check for debug cookie
                        debug_cookie_name = f"debug_{tenant}"
                        if debug_cookie_name in request.cookies:
                            debug_cookie_value = request.cookies.get(debug_cookie_name)
                            if debug_cookie_value.startswith('admin_id='):
                                admin_id = debug_cookie_value.replace('admin_id=', '')
                                logger.warning(f"Setting admin_id from debug cookie: {admin_id}")
                                session['admin_id'] = admin_id
                                session.modified = True

                        # Check for backup cookie
                        backup_cookie_name = f"backup_{tenant}"
                        if backup_cookie_name in request.cookies:
                            backup_cookie_value = request.cookies.get(backup_cookie_name)
                            if backup_cookie_value.startswith('admin_id='):
                                admin_id = backup_cookie_value.replace('admin_id=', '')
                                logger.warning(f"Setting admin_id from backup cookie: {admin_id}")
                                session['admin_id'] = admin_id
                                session.modified = True
                    else:
                        logger.warning(f"Not restoring session from cookies because user is logged out")
            finally:
                # Restore original cookie settings
                app.session_cookie_name = original_cookie_name
                app.config['SESSION_COOKIE_PATH'] = original_cookie_path
        else:
            # Use default session open behavior for non-tenant requests
            session = super().open_session(app, request)

        return session

    def new_session(self, _=None):
        """
        Create a new session.

        Args:
            _: Placeholder parameter (not used)

        Returns:
            A new session
        """
        return self.session_class()
