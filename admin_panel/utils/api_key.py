"""
API Key management utilities
"""
import secrets
import logging
import hashlib
from datetime import datetime, timezone
from bson.objectid import ObjectId
from utils.db_common import mongo_db
from utils.url_helpers import get_current_tenant

logger = logging.getLogger(__name__)

def generate_api_key():
    """Generate a secure API key"""
    return secrets.token_hex(32)  # 64 character hex string

def create_api_key(name, admin_id):
    """
    Create a new API key

    Args:
        name (str): A descriptive name for the API key
        admin_id (str): ID of the admin creating the key

    Returns:
        tuple: (api_key, api_key_id) or (None, error_message)
    """
    try:
        # Get current tenant
        tenant = get_current_tenant()
        logger.info(f"Creating API key for tenant: {tenant}")

        # Generate a new API key
        api_key = generate_api_key()

        # Hash the API key for storage
        key_hash = hashlib.sha256(api_key.encode()).hexdigest()

        # Create API key document with hashed key
        api_key_doc = {
            "key_hash": key_hash,
            "key_prefix": api_key[:6],  # Store first 6 chars for reference
            "name": name,
            "created_by": admin_id,
            "created_at": datetime.now(timezone.utc),
            "last_used": None,
            "status": "active",
            "tenant": tenant  # Store tenant information
        }

        # Insert into database
        result = mongo_db.db["api_keys"].insert_one(api_key_doc)

        if result.inserted_id:
            logger.info(f"API key '{name}' created by admin {admin_id} for tenant {tenant}")
            return api_key, str(result.inserted_id)
        else:
            logger.error(f"Failed to create API key '{name}' for tenant {tenant}")
            return None, "Failed to create API key"

    except Exception as e:
        logger.error(f"Error creating API key: {e}", exc_info=True)
        return None, f"Error: {str(e)}"

def validate_api_key(api_key):
    """
    Validate an API key

    Args:
        api_key (str): The API key to validate

    Returns:
        bool: True if valid, False otherwise
    """
    try:
        # Get current tenant
        tenant = get_current_tenant()
        logger.debug(f"Validating API key for tenant: {tenant}")

        # Hash the provided API key
        key_hash = hashlib.sha256(api_key.encode()).hexdigest()

        # Find the API key in the database using the hash
        # If tenant is specified, check for tenant-specific key first
        query = {"key_hash": key_hash, "status": "active"}
        if tenant:
            # Try to find a tenant-specific key first
            tenant_query = {**query, "tenant": tenant}
            api_key_doc = mongo_db.db["api_keys"].find_one(tenant_query)

            # If no tenant-specific key found, try to find a global key (no tenant field)
            if not api_key_doc:
                global_query = {**query, "tenant": {"$exists": False}}
                api_key_doc = mongo_db.db["api_keys"].find_one(global_query)
        else:
            # No tenant context, just check for active key
            api_key_doc = mongo_db.db["api_keys"].find_one(query)

        if api_key_doc:
            # Update last used timestamp
            mongo_db.db["api_keys"].update_one(
                {"_id": api_key_doc["_id"]},
                {"$set": {"last_used": datetime.now(timezone.utc)}}
            )
            return True
        else:
            logger.warning(f"Invalid API key attempt for tenant: {tenant}")
            return False

    except Exception as e:
        logger.error(f"Error validating API key: {e}", exc_info=True)
        return False

def get_api_keys():
    """
    Get all API keys for the current tenant

    Returns:
        list: List of API key documents
    """
    try:
        # Get current tenant
        tenant = get_current_tenant()
        logger.debug(f"Getting API keys for tenant: {tenant}")

        # If tenant is specified, get tenant-specific keys
        if tenant:
            # Get both tenant-specific keys and global keys (no tenant field)
            keys = list(mongo_db.db["api_keys"].find({
                "$or": [
                    {"tenant": tenant},
                    {"tenant": {"$exists": False}}
                ]
            }))
            logger.debug(f"Found {len(keys)} API keys for tenant {tenant}")
            return keys
        else:
            # No tenant context, return all keys
            keys = list(mongo_db.db["api_keys"].find())
            logger.debug(f"Found {len(keys)} API keys (no tenant context)")
            return keys
    except Exception as e:
        logger.error(f"Error getting API keys: {e}", exc_info=True)
        return []

def get_api_key(api_key_id):
    """
    Get an API key by ID

    Args:
        api_key_id (str): The API key ID

    Returns:
        dict: The API key document or None
    """
    try:
        # Get current tenant
        tenant = get_current_tenant()
        logger.debug(f"Getting API key {api_key_id} for tenant: {tenant}")

        # Basic query to find the key by ID
        query = {"_id": ObjectId(api_key_id)}

        # If tenant is specified, add tenant check
        if tenant:
            # Try to find a tenant-specific key or a global key
            query = {
                "_id": ObjectId(api_key_id),
                "$or": [
                    {"tenant": tenant},
                    {"tenant": {"$exists": False}}
                ]
            }

        api_key = mongo_db.db["api_keys"].find_one(query)
        if not api_key:
            logger.warning(f"API key {api_key_id} not found for tenant {tenant}")
        return api_key
    except Exception as e:
        logger.error(f"Error getting API key {api_key_id}: {e}", exc_info=True)
        return None

def update_api_key_status(api_key_id, status):
    """
    Update an API key's status

    Args:
        api_key_id (str): The API key ID
        status (str): The new status ('active' or 'revoked')

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Get current tenant
        tenant = get_current_tenant()
        logger.debug(f"Updating API key {api_key_id} status to {status} for tenant: {tenant}")

        # First check if the API key exists and belongs to this tenant
        api_key = get_api_key(api_key_id)
        if not api_key:
            logger.warning(f"Cannot update API key {api_key_id}: not found for tenant {tenant}")
            return False
        result = mongo_db.db["api_keys"].update_one(
            {"_id": ObjectId(api_key_id)},
            {"$set": {"status": status}}
        )

        if result.modified_count > 0:
            logger.info(f"API key {api_key_id} status updated to {status} for tenant {tenant}")
            return True
        else:
            logger.warning(f"API key {api_key_id} status not updated (no changes)")
            return False
    except Exception as e:
        logger.error(f"Error updating API key {api_key_id}: {e}", exc_info=True)
        return False

def delete_api_key(api_key_id):
    """
    Delete an API key

    Args:
        api_key_id (str): The API key ID

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Get current tenant
        tenant = get_current_tenant()
        logger.debug(f"Deleting API key {api_key_id} for tenant: {tenant}")

        # First check if the API key exists and belongs to this tenant
        api_key = get_api_key(api_key_id)
        if not api_key:
            logger.warning(f"Cannot delete API key {api_key_id}: not found for tenant {tenant}")
            return False
        result = mongo_db.db["api_keys"].delete_one({"_id": ObjectId(api_key_id)})

        if result.deleted_count > 0:
            logger.info(f"API key {api_key_id} deleted for tenant {tenant}")
            return True
        else:
            logger.warning(f"API key {api_key_id} not deleted")
            return False
    except Exception as e:
        logger.error(f"Error deleting API key {api_key_id}: {e}", exc_info=True)
        return False
