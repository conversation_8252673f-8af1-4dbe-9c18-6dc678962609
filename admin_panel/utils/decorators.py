# admin_panel/utils/decorators.py
from functools import wraps
from flask import session, redirect, url_for, g, request
import logging
from utils.url_helpers import tenant_url_for
from config import Config

logger = logging.getLogger(__name__)

# Define login_required decorator
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Get tenant from environ or g
        tenant = request.environ.get('tenant') or getattr(g, 'tenant', None)

        # Check if user is logged in

        # First check if user is explicitly logged out
        if session.get('logged_out'):
            # Use tenant_url_for to generate the correct URL based on the mode
            redirect_url = tenant_url_for('auth.login')
            logger.debug(f"User is logged out, redirecting to: {redirect_url}")
            return redirect(redirect_url)

        # Check if admin is logged in for the current tenant
        if 'admin_id' not in session:
            # Check if debug cookie exists but session doesn't have admin_id
            debug_cookie_name = f"debug_{tenant}"
            backup_cookie_name = f"backup_{tenant}"
            admin_id = None

            # Try to get admin_id from debug cookie
            if debug_cookie_name in request.cookies:
                debug_cookie_value = request.cookies.get(debug_cookie_name)
                if debug_cookie_value.startswith('admin_id='):
                    admin_id = debug_cookie_value.replace('admin_id=', '')

            # Try to get admin_id from backup cookie
            if not admin_id and backup_cookie_name in request.cookies:
                backup_cookie_value = request.cookies.get(backup_cookie_name)
                if backup_cookie_value.startswith('admin_id='):
                    admin_id = backup_cookie_value.replace('admin_id=', '')

            # If we found an admin_id, add it to the session and continue
            if admin_id:
                session['admin_id'] = admin_id
                session.modified = True

                # Add session_id if it doesn't exist
                if 'session_id' not in session:
                    import secrets
                    session['session_id'] = secrets.token_hex(8)

                # Continue with the request
                return f(*args, **kwargs)

            # Use tenant_url_for to generate the correct URL based on the mode
            redirect_url = tenant_url_for('auth.login')
            logger.debug(f"User not logged in, redirecting to: {redirect_url}")
            return redirect(redirect_url)
        else:
            # Add session_id if it doesn't exist
            if 'session_id' not in session:
                import secrets
                session['session_id'] = secrets.token_hex(8)
                session.modified = True

        return f(*args, **kwargs)
    return decorated_function