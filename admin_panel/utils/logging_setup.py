"""
Centralized logging configuration for the admin panel application.
This module sets up logging based on the configuration in config.json.
"""
import os
import logging
from logging.handlers import RotatingFileHandler
import sys
from config import Config

def setup_logging():
    """
    Configure the logging system based on application configuration.
    Sets up console and file handlers with appropriate formatting.
    """
    # Create logs directory if it doesn't exist
    log_dir = os.path.dirname(Config.LOG_FILE)
    os.makedirs(log_dir, exist_ok=True)

    # Get the root logger
    root_logger = logging.getLogger()

    # Clear any existing handlers to avoid duplicates
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # Set the log level from configuration
    log_level = getattr(logging, Config.LOG_LEVEL.upper(), logging.INFO)

    # Force DEBUG level for development
    log_level = logging.DEBUG

    root_logger.setLevel(log_level)

    # Create formatters
    file_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - [%(filename)s:%(lineno)d] - %(message)s'
    )
    console_formatter = logging.Formatter(
        '%(asctime)s - %(levelname)s - %(message)s'
    )

    # Create and configure file handler with rotation
    # 5 MB per file, keep 5 backup files
    file_handler = RotatingFileHandler(
        Config.LOG_FILE,
        maxBytes=5*1024*1024,  # 5 MB
        backupCount=5
    )
    file_handler.setFormatter(file_formatter)
    file_handler.setLevel(log_level)

    # Create and configure console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(console_formatter)
    console_handler.setLevel(log_level)

    # Add handlers to root logger
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)

    # Set specific loggers to DEBUG level
    logging.getLogger('utils.tenant_session').setLevel(logging.DEBUG)
    logging.getLogger('utils.tenant_middleware').setLevel(logging.DEBUG)
    logging.getLogger('utils.decorators').setLevel(logging.DEBUG)
    logging.getLogger('controllers.auth').setLevel(logging.DEBUG)

    # Log that logging has been configured
    logging.info(f"Logging configured with level {log_level}")
    logging.info(f"Log file: {Config.LOG_FILE}")

    if Config.DEBUG:
        logging.info("Debug mode is enabled, all logs will be at DEBUG level")

def get_logger(name):
    """
    Get a logger with the given name.
    This is a convenience function to get a logger with the correct configuration.

    Args:
        name: The name of the logger, typically __name__

    Returns:
        A configured logger instance
    """
    return logging.getLogger(name)
