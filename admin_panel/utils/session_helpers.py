"""
Session helper functions for multi-tenant support.
This module provides utility functions for working with tenant-specific sessions.
"""
import logging
from flask import session, g, request, has_request_context

logger = logging.getLogger(__name__)

def is_logged_in_for_tenant(tenant_name):
    """
    Check if the user is logged in for a specific tenant.
    
    Args:
        tenant_name (str): The tenant name to check
        
    Returns:
        bool: True if the user is logged in for the tenant, False otherwise
    """
    if not tenant_name or not has_request_context():
        return False
    
    # Get current tenant from request.environ, g, or session
    current_tenant = request.environ.get('tenant') or getattr(g, 'tenant', None) or session.get('tenant')
    
    # Check if the current tenant matches the requested tenant
    if current_tenant != tenant_name:
        logger.debug(f"Current tenant ({current_tenant}) does not match requested tenant ({tenant_name})")
        return False
    
    # Check if the user is logged in
    if 'admin_id' not in session:
        logger.debug(f"User not logged in for tenant: {tenant_name}")
        return False
    
    return True

def get_tenant_session_cookie_name(tenant_name):
    """
    Get the session cookie name for a tenant.
    
    Args:
        tenant_name (str): The tenant name
        
    Returns:
        str: The session cookie name for the tenant
    """
    if not tenant_name:
        return "session"
    
    return f"session_{tenant_name}"
