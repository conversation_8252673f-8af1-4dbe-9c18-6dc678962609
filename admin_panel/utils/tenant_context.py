"""
Tenant context management for multi-tenant support.
This module handles tenant identification and database selection.
"""
import logging
from pymongo import MongoClient
from flask import request, session, g
from config import Config

logger = logging.getLogger(__name__)

# MongoDB client (shared across all tenants)
mongo_client = MongoClient(Config.MONGO_URI)

def get_tenant_from_url():
    """
    Extract tenant name from the URL path.
    The tenant name is the first segment of the path.

    Returns:
        str: The tenant name or None if not found
    """
    path = request.path.lstrip('/')
    parts = path.split('/', 1)

    # If the path has at least one segment
    if parts and parts[0]:
        return parts[0]

    return None

def tenant_db_exists(tenant_name):
    """
    Check if a tenant database exists.

    Args:
        tenant_name (str): The tenant name

    Returns:
        bool: True if the tenant database exists, False otherwise
    """
    if not tenant_name:
        return False

    # Construct the database name
    db_name = f"{tenant_name}_custdb"

    # Check if the database exists in MongoDB
    # Note: MongoDB creates databases on demand, so we need to check if it has collections
    try:
        db = mongo_client[db_name]
        # A database with no collections is considered non-existent
        return len(db.list_collection_names()) > 0
    except Exception as e:
        logger.error(f"Error checking if tenant database exists: {e}")
        return False

def get_tenant_db(tenant_name):
    """
    Get a MongoDB database for a tenant.

    Args:
        tenant_name (str): The tenant name

    Returns:
        Database: The MongoDB database for the tenant or None if it doesn't exist
    """
    if not tenant_name:
        return None

    # Construct the database name
    db_name = f"{tenant_name}_custdb"

    # Check if the database exists
    if tenant_db_exists(tenant_name):
        return mongo_client[db_name]

    return None

def set_tenant_context(tenant_name):
    """
    Set the current tenant in the application context.

    Args:
        tenant_name (str): The tenant name
    """
    if tenant_name:
        g.tenant = tenant_name
        # Note: The session cookie name is already tenant-specific due to TenantSessionInterface
        session['tenant'] = tenant_name
        logger.debug(f"Set tenant context: {tenant_name}")
    else:
        g.tenant = None
        if 'tenant' in session:
            session.pop('tenant')
        logger.debug("Cleared tenant context")

def get_current_tenant():
    """
    Get the current tenant from the application context.

    Returns:
        str: The current tenant name or None if not set
    """
    # First check request.environ (most reliable source)
    tenant = request.environ.get('tenant') if hasattr(request, 'environ') else None

    # Then check g and session
    if not tenant:
        tenant = getattr(g, 'tenant', None) or session.get('tenant')

    return tenant
