"""
Custom session serializer for Flask sessions.
This module provides a custom session serializer that ensures session data is properly preserved.
"""
import logging
from flask.json.tag import TaggedJSONSerializer

logger = logging.getLogger(__name__)

class CustomSessionSerializer(TaggedJSONSerializer):
    """
    Custom session serializer that ensures session data is properly preserved.
    """

    def dumps(self, obj):
        """
        Serialize session data to JSON.

        Args:
            obj: The session data to serialize

        Returns:
            str: The serialized session data
        """
        return super().dumps(obj)

    def loads(self, value):
        """
        Deserialize session data from JSON.

        Args:
            value: The serialized session data

        Returns:
            dict: The deserialized session data
        """
        try:
            # Call parent method to deserialize session data
            result = super().loads(value)
            return result
        except Exception as e:
            logger.error(f"Error deserializing session data: {e}")
            # Return empty session data on error
            return {}
