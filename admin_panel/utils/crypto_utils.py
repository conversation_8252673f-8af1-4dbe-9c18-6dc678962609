"""
Cryptography utilities for encrypting and decrypting sensitive data.
"""
import base64
import os
import logging
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import json

logger = logging.getLogger(__name__)

# Load config.json for encryption key
def load_config():
    """Load configuration from config.json"""
    try:
        config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config.json')
        with open(config_path, 'r') as f:
            config = json.load(f)
        return config
    except Exception as e:
        logger.error(f"Error loading config: {e}", exc_info=True)
        return {}

def get_encryption_key():
    """
    Get or generate an encryption key for sensitive data.
    
    The key is stored in config.json under admin_panel.security.encryption_key
    If no key exists, a new one is generated and saved to the config.
    
    Returns:
        bytes: The encryption key
    """
    try:
        config = load_config()
        
        # Check if encryption key exists in config
        encryption_key_b64 = config.get('admin_panel', {}).get('security', {}).get('encryption_key')
        
        if not encryption_key_b64:
            # Generate a new key
            logger.info("Generating new encryption key")
            encryption_key = Fernet.generate_key()
            encryption_key_b64 = base64.b64encode(encryption_key).decode('utf-8')
            
            # Save the key to config
            if 'admin_panel' not in config:
                config['admin_panel'] = {}
            if 'security' not in config['admin_panel']:
                config['admin_panel']['security'] = {}
            
            config['admin_panel']['security']['encryption_key'] = encryption_key_b64
            
            # Write updated config back to file
            config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config.json')
            with open(config_path, 'w') as f:
                json.dump(config, f, indent=2)
                
            logger.info("New encryption key saved to config.json")
            return base64.b64decode(encryption_key_b64)
        else:
            # Use existing key
            return base64.b64decode(encryption_key_b64)
            
    except Exception as e:
        logger.error(f"Error getting encryption key: {e}", exc_info=True)
        # Generate a temporary key if we can't get one from config
        # This is not ideal as it won't persist, but it's better than failing
        return Fernet.generate_key()

def encrypt_data(data):
    """
    Encrypt sensitive data using Fernet symmetric encryption.
    
    Args:
        data (str): The data to encrypt
        
    Returns:
        str: Base64-encoded encrypted data
    """
    if not data:
        return None
        
    try:
        # Get encryption key
        key = get_encryption_key()
        
        # Create Fernet cipher
        cipher = Fernet(key)
        
        # Encrypt the data
        encrypted_data = cipher.encrypt(data.encode('utf-8'))
        
        # Return base64 encoded string for storage
        return base64.b64encode(encrypted_data).decode('utf-8')
    except Exception as e:
        logger.error(f"Error encrypting data: {e}", exc_info=True)
        return None

def decrypt_data(encrypted_data):
    """
    Decrypt sensitive data that was encrypted with encrypt_data.
    
    Args:
        encrypted_data (str): Base64-encoded encrypted data
        
    Returns:
        str: The decrypted data
    """
    if not encrypted_data:
        return None
        
    try:
        # Get encryption key
        key = get_encryption_key()
        
        # Create Fernet cipher
        cipher = Fernet(key)
        
        # Decode base64 string to bytes
        encrypted_bytes = base64.b64decode(encrypted_data)
        
        # Decrypt the data
        decrypted_data = cipher.decrypt(encrypted_bytes)
        
        # Return decoded string
        return decrypted_data.decode('utf-8')
    except Exception as e:
        logger.error(f"Error decrypting data: {e}", exc_info=True)
        return None
