"""
Tenant middleware for handling tenant-prefixed URLs.
This module provides middleware for rewriting URLs to handle tenant prefixes.
"""
import logging
from werkzeug.wsgi import get_path_info, get_script_name
from werkzeug.urls import url_parse, url_unparse
from flask import request, g, session
from config import Config

logger = logging.getLogger(__name__)

class TenantMiddleware:
    """
    Middleware for handling tenant-prefixed URLs.
    This middleware rewrites URLs to handle tenant prefixes.

    In test mode: Extracts tenant from URL path (localhost/tenant_name/)
    In prod mode: Extracts tenant from subdomain (tenant_name.localhost/)
    """

    def __init__(self, app):
        self.app = app
        self.logger = logging.getLogger(__name__)

    def __call__(self, environ, start_response):
        # Get the application mode
        mode = Config.get_mode()

        # Get the original path
        path = get_path_info(environ)

        # Log the request details for debugging
        host = environ.get('HTTP_HOST', 'unknown')
        self.logger.debug(f"Request: {host}{path}, Mode: {mode}")

        # Skip for static files and the welcome page
        if path.startswith('/static') or path == '/welcome':
            # Clear tenant in environ for static files and welcome page
            if 'tenant' in environ:
                del environ['tenant']
            return self.app(environ, start_response)

        # Extract tenant based on mode
        tenant_name = None

        if mode == "prod":
            # In production mode, extract tenant from subdomain
            host = environ.get('HTTP_HOST', '')
            self.logger.debug(f"Prod mode - Host: {host}")

            # Check if we have a subdomain
            if '.' in host:
                # Extract subdomain (tenant name)
                subdomain = host.split('.')[0]

                # If we have a valid subdomain, use it as tenant
                if subdomain and subdomain != 'localhost':
                    tenant_name = subdomain
                    self.logger.debug(f"Extracted tenant from subdomain: {tenant_name}")

                    # IMPORTANT: In prod mode with subdomain, we need to check if the path
                    # still contains the tenant prefix and remove it if it does
                    if path.startswith(f"/{tenant_name}/"):
                        # Remove tenant prefix from path
                        new_path = path[len(f"/{tenant_name}"):]
                        if not new_path:
                            new_path = "/"

                        environ['PATH_INFO'] = new_path

                        # Store original path for reference
                        environ['ORIGINAL_PATH_INFO'] = path

                        self.logger.debug(f"Removing redundant tenant prefix: {path} -> {new_path} (Tenant: {tenant_name})")

            # IMPORTANT: Also check for path-based tenant in prod mode as a fallback
            # This handles cases where the subdomain routing isn't working yet
            if not tenant_name:
                path_parts = path.lstrip('/').split('/', 1)

                # If the path has at least one segment
                if path_parts and path_parts[0]:
                    tenant_name = path_parts[0]
                    self.logger.debug(f"Fallback: Extracted tenant from path in prod mode: {tenant_name}")

                    # Rewrite the URL to remove the tenant prefix
                    if len(path_parts) > 1:
                        # Remove tenant prefix from path
                        new_path = f"/{path_parts[1]}"
                        environ['PATH_INFO'] = new_path

                        # Store original path for reference
                        environ['ORIGINAL_PATH_INFO'] = path

                        self.logger.debug(f"Rewriting URL: {path} -> {new_path} (Tenant: {tenant_name})")
                    else:
                        # If no path after tenant, default to root
                        environ['PATH_INFO'] = "/"
                        environ['ORIGINAL_PATH_INFO'] = path

                        self.logger.debug(f"Rewriting URL: {path} -> / (Tenant: {tenant_name})")
        else:
            # In test mode, extract tenant from URL path
            path_parts = path.lstrip('/').split('/', 1)

            # If the path has at least one segment
            if path_parts and path_parts[0]:
                tenant_name = path_parts[0]
                self.logger.debug(f"Extracted tenant from path: {tenant_name}")

                # Rewrite the URL to remove the tenant prefix
                if len(path_parts) > 1:
                    # Remove tenant prefix from path
                    new_path = f"/{path_parts[1]}"
                    environ['PATH_INFO'] = new_path

                    # Store original path for reference
                    environ['ORIGINAL_PATH_INFO'] = path

                    self.logger.debug(f"Rewriting URL: {path} -> {new_path} (Tenant: {tenant_name})")
                else:
                    # If no path after tenant, default to root
                    environ['PATH_INFO'] = "/"
                    environ['ORIGINAL_PATH_INFO'] = path

                    self.logger.debug(f"Rewriting URL: {path} -> / (Tenant: {tenant_name})")

        # If we found a tenant, store it in environ
        if tenant_name:
            # Store tenant in environ for later use
            environ['tenant'] = tenant_name

            # Store the tenant name in a custom header for debugging
            def custom_start_response(status, headers, exc_info=None):
                headers.append(('X-Tenant', tenant_name))
                return start_response(status, headers, exc_info)

            return self.app(environ, custom_start_response)
        else:
            # Clear tenant in environ if no tenant found
            if 'tenant' in environ:
                del environ['tenant']

            self.logger.warning(f"No tenant found for request: {host}{path}")

        return self.app(environ, start_response)
