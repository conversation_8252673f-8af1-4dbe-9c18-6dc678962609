# admin_panel/utils/telegram_utils.py
import requests
import logging
from datetime import datetime, timedelta, timezone
from utils.mongo_db import MongoDB

logger = logging.getLogger(__name__)
mongo_db = MongoDB()

def get_bot_credentials(bot_token=None):
    """
    Get bot token and channel ID from MongoDB.

    If bot_token is provided, it will find that specific bot.
    If not, it will try to find an active bot or the most recently updated one.

    Returns:
        tuple: (bot_token, channel_id)
    """
    try:
        if bot_token:
            # Find the specific bot by token
            bot = mongo_db.db["telegram_bots"].find_one({"token": bot_token})
        else:
            # Try to find an active bot
            bot = mongo_db.db["telegram_bots"].find_one({"is_active": True})

            # If no active bot found, get the most recently updated one
            if not bot:
                bot = mongo_db.db["telegram_bots"].find_one({}, sort=[("updated_at", -1)])

        if bot and "token" in bot and "channel_id" in bot:
            return bot["token"], bot["channel_id"]
        else:
            logger.error("No bot credentials found in the database")
            return None, None

    except Exception as e:
        logger.error(f"Error retrieving bot credentials from database: {e}")
        return None, None

def get_telegram_bot_info(token):
    """
    Get bot information from Telegram API
    """
    try:
        url = f"https://api.telegram.org/bot{token}/getMe"
        response = requests.get(url, timeout=10)
        response.raise_for_status() # Raise an exception for bad status codes
        data = response.json()

        if data.get('ok'):
            return data.get('result')
        else:
            logger.error(f"Telegram API error for token {token[:10]}...: {data.get('description')}")
            return None
    except requests.exceptions.RequestException as e:
        logger.error(f"Error connecting to Telegram API for token {token[:10]}...: {str(e)}")
        return None
    except Exception as e:
        logger.error(f"Unexpected error getting bot info for token {token[:10]}...: {str(e)}")
        return None

def check_telegram_membership(user_id, bot_token=None, channel_id=None):
    """Checks if a user is a member of the specified channel.

    If bot_token is not provided, it will get the token from the database.
    If channel_id is not provided, it will get it from the database.
    """
    # If bot_token is not provided, get it from the database
    if not bot_token or not channel_id:
        db_token, db_channel_id = get_bot_credentials(bot_token)

        # Use the provided token if available, otherwise use the one from the database
        if not bot_token and db_token:
            bot_token = db_token

        # Use the provided channel_id if available, otherwise use the one from the database
        if not channel_id and db_channel_id:
            channel_id = db_channel_id

    # If we still don't have valid credentials, log an error and return
    if not bot_token or not channel_id:
        logger.error("Could not get valid bot credentials from database")
        return {
            'is_member': False,
            'status': 'unknown',
            'error': 'Invalid bot credentials'
        }
    status_info = {
        'is_member': False,
        'status': 'unknown',
        'error': None
    }
    try:
        api_url = f"https://api.telegram.org/bot{bot_token}/getChatMember"
        response = requests.get(api_url, params={
            "chat_id": channel_id,
            "user_id": user_id
        }, timeout=10)
        response.raise_for_status()
        data = response.json()

        if data.get("ok"):
            status = data.get("result", {}).get("status", "")
            status_info['is_member'] = status in ["member", "administrator", "creator"]
            status_info['status'] = status
        else:
            status_info['error'] = data.get("description", "Failed to check membership (API error)")
            logger.warning(f"Telegram API error checking membership for user {user_id}: {status_info['error']}")

    except requests.exceptions.RequestException as e:
        logger.error(f"Error checking Telegram membership for user {user_id}: {e}")
        status_info['error'] = f"Network error: {str(e)}"
    except Exception as e:
        logger.error(f"Unexpected error checking Telegram membership for user {user_id}: {e}")
        status_info['error'] = f"Unexpected error: {str(e)}"

    return status_info

def update_user_telegram_bot_id(user_id, bot_id):
    """
    Update the telegram_bot_id field in the master_user_data collection
    """
    if not user_id or not bot_id:
        logger.warning(f"Cannot update telegram_bot_id: Invalid user_id ({user_id}) or bot_id ({bot_id})")
        return

    try:
        # Convert user_id to int if it's a string
        if isinstance(user_id, str) and user_id.isdigit():
            user_id = int(user_id)

        # Ensure bot_id is an integer
        if isinstance(bot_id, str) and bot_id.isdigit():
            bot_id = int(bot_id)
        elif not isinstance(bot_id, int):
            logger.warning(f"Invalid bot_id format: {bot_id} (type: {type(bot_id)})")
            return

        # Update the user's telegram_bot_id
        result = mongo_db.db["master_user_data"].update_one(
            {"user_id": user_id},
            {"$set": {
                "telegram_bot_id": bot_id,
                "last_updated": datetime.now(timezone.utc)
            }}
        )

        if result.modified_count > 0:
            logger.info(f"Updated telegram_bot_id for user {user_id} to {bot_id}")
        elif result.matched_count > 0:
            logger.info(f"User {user_id} already has telegram_bot_id {bot_id}")
        else:
            logger.warning(f"User {user_id} not found in master_user_data collection")

    except Exception as e:
        logger.error(f"Error updating telegram_bot_id for user {user_id}: {e}")

def update_user_interaction_time(user_id):
    """
    Update the last_interaction_time field in the master_user_data collection
    """
    if not user_id:
        logger.warning(f"Cannot update last_interaction_time: Invalid user_id ({user_id})")
        return

    try:
        # Convert user_id to int if it's a string
        if isinstance(user_id, str) and user_id.isdigit():
            user_id = int(user_id)

        # Get current time with timezone
        current_time = datetime.now(timezone.utc)

        # Update the user's last_interaction_time
        result = mongo_db.db["master_user_data"].update_one(
            {"user_id": user_id},
            {"$set": {
                "last_interaction_time": current_time,
                "last_updated": current_time
            }}
        )

        if result.modified_count > 0:
            logger.info(f"Updated last_interaction_time for user {user_id}")
        elif result.matched_count > 0:
            logger.debug(f"Updated last_interaction_time for user {user_id} (no changes)")
        else:
            logger.warning(f"User {user_id} not found in master_user_data collection")

    except Exception as e:
        logger.error(f"Error updating last_interaction_time for user {user_id}: {e}")

def create_telegram_invite_link(bot_token=None, channel_id=None, days_valid=7):
    """Creates a one-time use invite link for the channel.

    If bot_token is not provided, it will get the token from the database.
    If channel_id is not provided, it will get it from the database.
    """
    # If bot_token is not provided, get it from the database
    if not bot_token or not channel_id:
        db_token, db_channel_id = get_bot_credentials(bot_token)

        # Use the provided token if available, otherwise use the one from the database
        if not bot_token and db_token:
            bot_token = db_token

        # Use the provided channel_id if available, otherwise use the one from the database
        if not channel_id and db_channel_id:
            channel_id = db_channel_id

    # If we still don't have valid credentials, log an error and return
    if not bot_token or not channel_id:
        logger.error("Could not get valid bot credentials from database")
        return None, "Invalid bot credentials"

    try:
        api_url = f"https://api.telegram.org/bot{bot_token}/createChatInviteLink"
        expire_date = int((datetime.now(timezone.utc) + timedelta(days=days_valid)).timestamp())

        response = requests.post(api_url, data={
            "chat_id": channel_id,
            "member_limit": 1,
            "expire_date": expire_date
        }, timeout=10)
        response.raise_for_status()
        data = response.json()

        if data.get("ok"):
            return data["result"]["invite_link"], None
        else:
            error_msg = data.get("description", "Failed to create invite link (API error)")
            logger.error(f"Error creating invite link: {error_msg}")
            return None, error_msg

    except requests.exceptions.RequestException as e:
        logger.error(f"Error connecting to Telegram API for invite link: {e}")
        return None, f"Network error: {str(e)}"
    except Exception as e:
        logger.error(f"Unexpected error creating invite link: {e}")
        return None, f"Unexpected error: {str(e)}"

def send_telegram_message(user_id, text, bot_token=None, inline_button=None, inline_buttons=None):
    """Sends a message to a specific Telegram user.

    Args:
        user_id: The Telegram user ID to send the message to
        text: The message text to send
        bot_token: The Telegram bot token to use. If None, it will be retrieved from the database.
        inline_button: A single button dict with 'text' and 'url' keys (legacy support)
        inline_buttons: A list of button dicts, each with 'text' and 'url' keys

    Returns:
        Tuple of (success, error_message)
    """
    # If bot_token is not provided, get it from the database
    if not bot_token:
        db_token, _ = get_bot_credentials()
        if db_token:
            bot_token = db_token
        else:
            logger.error("Could not get valid bot token from database")
            return False, "Invalid bot token"
    try:
        # Get bot info to update user's telegram_bot_id
        bot_info = get_telegram_bot_info(bot_token)
        if bot_info and bot_info.get('id'):
            # Update user's telegram_bot_id in master_user_data
            update_user_telegram_bot_id(user_id, bot_info.get('id'))

        send_url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
        payload = {
            "chat_id": user_id,
            "text": text,
            "parse_mode": "HTML" # Or "MarkdownV2" or None
        }

        # Handle inline keyboard buttons
        if inline_buttons and isinstance(inline_buttons, list) and len(inline_buttons) > 0:
            # Create a keyboard with multiple buttons, one button per row
            keyboard = []
            for button in inline_buttons:
                button_data = {"text": button['text']}

                # Button can have either url or callback_data, not both
                if 'url' in button:
                    button_data["url"] = button['url']
                elif 'callback_data' in button:
                    button_data["callback_data"] = button['callback_data']

                keyboard.append([button_data])

            payload["reply_markup"] = {
                "inline_keyboard": keyboard
            }
        # Legacy support for single button
        elif inline_button and 'text' in inline_button and 'url' in inline_button:
            payload["reply_markup"] = {
                "inline_keyboard": [[
                    {
                        "text": inline_button['text'],
                        "url": inline_button['url']
                    }
                ]]
            }

        send_response = requests.post(send_url, json=payload, timeout=10)
        send_response.raise_for_status()
        send_data = send_response.json()

        if send_data.get("ok"):
            # Update user's last_interaction_time
            update_user_interaction_time(user_id)
            return True, None
        else:
            error_msg = send_data.get("description", "Failed to send message (API error)")
            logger.error(f"Error sending message to user {user_id}: {error_msg}")
            return False, error_msg

    except requests.exceptions.RequestException as e:
        logger.error(f"Error connecting to Telegram API to send message to {user_id}: {e}")
        return False, f"Network error: {str(e)}"
    except Exception as e:
        logger.error(f"Unexpected error sending message to user {user_id}: {e}")
        return False, f"Unexpected error: {str(e)}"

def kick_telegram_user(user_id, bot_token=None, channel_id=None):
    """Kicks (bans) a user from the Telegram channel.

    If bot_token is not provided, it will get the token from the database.
    If channel_id is not provided, it will get it from the database.
    """
    # If bot_token is not provided, get it from the database
    if not bot_token or not channel_id:
        db_token, db_channel_id = get_bot_credentials(bot_token)

        # Use the provided token if available, otherwise use the one from the database
        if not bot_token and db_token:
            bot_token = db_token

        # Use the provided channel_id if available, otherwise use the one from the database
        if not channel_id and db_channel_id:
            channel_id = db_channel_id

    # If we still don't have valid credentials, log an error and return
    if not bot_token or not channel_id:
        logger.error("Could not get valid bot credentials from database")
        return False, "Invalid bot credentials"
    try:
        api_url = f"https://api.telegram.org/bot{bot_token}/banChatMember"
        response = requests.post(api_url, data={
            "chat_id": channel_id,
            "user_id": user_id,
            "revoke_messages": False
        }, timeout=10)
        response.raise_for_status()
        data = response.json()

        if data.get("ok"):
            return True, None
        else:
            error_msg = data.get("description", "Failed to kick user (API error)")
            logger.error(f"Error kicking user {user_id}: {error_msg}")
            return False, error_msg

    except requests.exceptions.RequestException as e:
        logger.error(f"Error connecting to Telegram API to kick user {user_id}: {e}")
        return False, f"Network error: {str(e)}"
    except Exception as e:
        logger.error(f"Unexpected error kicking user {user_id}: {e}")
        return False, f"Unexpected error: {str(e)}"

def unban_telegram_user(user_id, bot_token=None, channel_id=None):
    """Unbans a user from the Telegram channel.

    If bot_token is not provided, it will get the token from the database.
    If channel_id is not provided, it will get it from the database.
    """
    # If bot_token is not provided, get it from the database
    if not bot_token or not channel_id:
        db_token, db_channel_id = get_bot_credentials(bot_token)

        # Use the provided token if available, otherwise use the one from the database
        if not bot_token and db_token:
            bot_token = db_token

        # Use the provided channel_id if available, otherwise use the one from the database
        if not channel_id and db_channel_id:
            channel_id = db_channel_id

    # If we still don't have valid credentials, log an error and return
    if not bot_token or not channel_id:
        logger.error("Could not get valid bot credentials from database")
        return False, "Invalid bot credentials"
    try:
        api_url = f"https://api.telegram.org/bot{bot_token}/unbanChatMember"
        response = requests.post(api_url, data={
            "chat_id": channel_id,
            "user_id": user_id,
            "only_if_banned": True
        }, timeout=10)
        response.raise_for_status()
        data = response.json()

        if data.get("ok"):
            return True, None
        else:
            error_msg = data.get("description", "Failed to unban user (API error)")
            logger.error(f"Error unbanning user {user_id}: {error_msg}")
            return False, error_msg

    except requests.exceptions.RequestException as e:
        logger.error(f"Error connecting to Telegram API to unban user {user_id}: {e}")
        return False, f"Network error: {str(e)}"
    except Exception as e:
        logger.error(f"Unexpected error unbanning user {user_id}: {e}")
        return False, f"Unexpected error: {str(e)}"

def get_channel_info(bot_token, channel_id):
    """Gets information about a Telegram channel.

    Args:
        bot_token: The Telegram bot token
        channel_id: The channel ID to get info for

    Returns:
        dict: Channel information or None if there was an error
    """
    try:
        api_url = f"https://api.telegram.org/bot{bot_token}/getChat"
        response = requests.get(api_url, params={
            "chat_id": channel_id
        }, timeout=10)
        response.raise_for_status()
        data = response.json()

        if data.get("ok"):
            return data.get("result")
        else:
            error_msg = data.get("description", "Failed to get channel info (API error)")
            logger.warning(f"Telegram API error getting channel info: {error_msg}")
            return None
    except Exception as e:
        logger.error(f"Error getting channel info for channel {channel_id}: {e}")
        return None

def check_bot_channel_status(bot_token, channel_id=None):
    """Checks if a bot is a member of the specified channel and has admin privileges.

    Args:
        bot_token: The Telegram bot token
        channel_id: The channel ID to check. If not provided, it will try to get it from the bot record.

    Returns:
        dict: A dictionary with the following keys:
            - is_member: True if the bot is a member of the channel
            - is_admin: True if the bot has admin privileges in the channel
            - status: The bot's status in the channel (member, administrator, creator, etc.)
            - error: Error message if any
            - channel_info: Information about the channel (if available)
    """
    # Get bot info to get the bot's user ID
    bot_info = get_telegram_bot_info(bot_token)
    if not bot_info:
        return {
            'is_member': False,
            'is_admin': False,
            'status': 'unknown',
            'error': 'Could not get bot information',
            'channel_info': None
        }

    bot_user_id = bot_info.get('id')

    # If channel_id is not provided, try to get it from the database
    if not channel_id:
        _, db_channel_id = get_bot_credentials(bot_token)
        if db_channel_id:
            channel_id = db_channel_id
        else:
            logger.error(f"Could not get valid channel ID from database for bot token {bot_token[:10]}...")
            status_info['error'] = "Could not get valid channel ID from database"
            return status_info

    status_info = {
        'is_member': False,
        'is_admin': False,
        'status': 'unknown',
        'error': None,
        'channel_info': None
    }

    # Get channel info
    channel_info = get_channel_info(bot_token, channel_id)
    if channel_info:
        status_info['channel_info'] = channel_info

    try:
        api_url = f"https://api.telegram.org/bot{bot_token}/getChatMember"
        response = requests.get(api_url, params={
            "chat_id": channel_id,
            "user_id": bot_user_id
        }, timeout=10)
        response.raise_for_status()
        data = response.json()

        if data.get("ok"):
            status = data.get("result", {}).get("status", "")
            status_info['is_member'] = status in ["member", "administrator", "creator"]
            status_info['is_admin'] = status in ["administrator", "creator"]
            status_info['status'] = status
        else:
            status_info['error'] = data.get("description", "Failed to check bot status (API error)")
            logger.warning(f"Telegram API error checking bot status: {status_info['error']}")

    except requests.exceptions.RequestException as e:
        logger.error(f"Error checking Telegram bot status: {e}")
        status_info['error'] = f"Network error: {str(e)}"
    except Exception as e:
        logger.error(f"Unexpected error checking Telegram bot status: {e}")
        status_info['error'] = f"Unexpected error: {str(e)}"

    return status_info