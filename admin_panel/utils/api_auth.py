"""
API authentication utilities
"""
from functools import wraps
from flask import request, jsonify
from utils.api_key import validate_api_key
import logging
import json
import os

logger = logging.getLogger(__name__)

# Load config.json for autorunner password
def load_config():
    """Load configuration from config.json"""
    try:
        config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 'config.json')
        with open(config_path, 'r') as f:
            config = json.load(f)
        return config
    except Exception as e:
        logger.error(f"Error loading config: {e}", exc_info=True)
        return {}

def autorunner_validation(api_key):
    """
    Validate if the provided key matches the autorunner password from config

    Args:
        api_key (str): The API key to validate against autorunner password

    Returns:
        bool: True if valid autorunner password, False otherwise
    """
    try:
        config = load_config()
        autorunner_pass = config.get('admin_panel', {}).get('security', {}).get('autorunner_pass')

        if not autorunner_pass:
            logger.warning("Autorunner password not found in config")
            return False

        # Check if the provided key matches the autorunner password
        if api_key == autorunner_pass:
            logger.info("Autorunner authentication successful")
            return True

        return False
    except Exception as e:
        logger.error(f"Error in autorunner validation: {e}", exc_info=True)
        return False

def api_key_required(f):
    """
    Decorator to require API key authentication

    The API key should be provided in the 'X-API-Key' header.
    Validates against:
    1. Regular API keys in the database
    2. Autorunner password from config.json
    """
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Get API key from header
        api_key = request.headers.get('X-API-Key')

        if not api_key:
            logger.warning("API request missing X-API-Key header")
            return jsonify({
                "success": False,
                "error": "API key required",
                "message": "Please provide an API key in the X-API-Key header"
            }), 401

        # Validate API key against database keys and autorunner password
        if not validate_api_key(api_key) and not autorunner_validation(api_key):
            # Mask the key in logs for security
            masked_key = f"{api_key[:6]}..." if len(api_key) > 6 else "***"
            logger.warning(f"Invalid API key used: {masked_key}")
            return jsonify({
                "success": False,
                "error": "Invalid API key",
                "message": "The provided API key is invalid or has been revoked"
            }), 401

        return f(*args, **kwargs)

    return decorated_function
