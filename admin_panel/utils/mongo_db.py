from pymongo import MongoClient
from config import Config
import logging
from bson.objectid import ObjectId
from urllib.parse import urlparse
from flask import g, session, request, has_request_context, has_app_context

logger = logging.getLogger(__name__)

class MongoDB:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(MongoDB, cls).__new__(cls)
            # Use URI from configuration with explicit database name
            cls._instance.client = MongoClient(Config.MONGO_URI)

            # Initialize with default database
            cls._instance.default_db_name = "telegram_bot"
            cls._instance.db = cls._instance.client[cls._instance.default_db_name]

            # Track the current tenant to avoid unnecessary database switching
            cls._instance.current_tenant = None

            # Initialize collection references
            cls._instance._init_collections()

            logger.info(f"Connected to MongoDB: {Config.MONGO_URI}")
            logger.info(f"Using default database '{cls._instance.default_db_name}'")

        # Check if we need to switch to a tenant database
        cls._instance._check_tenant_context()

        return cls._instance

    def _init_collections(self):
        """Initialize collection references"""
        self.activity_logs = self.db["activity_logs"]
        self.admins = self.db["admins"]
        self.access_codes = self.db["access_codes"]
        self.master_user_data = self.db["master_user_data"]

    def _check_tenant_context(self):
        """Check if we need to switch to a tenant database based on request context"""
        # Skip tenant context check if not in application context
        if not has_app_context():
            logger.debug("No application context, skipping tenant context check")
            return

        # Skip tenant context check if not in request context
        if not has_request_context():
            logger.debug("No request context, skipping tenant context check")
            return

        try:
            # Get tenant from request environ first (most reliable)
            tenant = None
            if has_request_context():
                tenant = request.environ.get('tenant')

            # If no tenant in environ, check URL path
            if not tenant:
                tenant = self._get_tenant_from_url()

            # Also check session and g for tenant information
            if not tenant:
                # Safely check g and session
                tenant_from_g = getattr(g, 'tenant', None) if has_app_context() else None
                tenant_from_session = session.get('tenant') if has_request_context() else None
                tenant = tenant_from_g or tenant_from_session

            # If we have a tenant
            if tenant:
                # Check if tenant database exists
                tenant_db_name = f"{tenant}_custdb"
                if self._tenant_db_exists(tenant_db_name):
                    # Switch to tenant database if it's different from current
                    if self.db.name != tenant_db_name:
                        logger.info(f"MongoDB._check_tenant_context: Switching from {self.db.name} to {tenant_db_name}")
                        self.switch_database(tenant_db_name)
                        self.current_tenant = tenant
                        return
                    else:
                        logger.debug(f"MongoDB._check_tenant_context: Already using correct database {tenant_db_name}")
                else:
                    logger.warning(f"MongoDB._check_tenant_context: Tenant database {tenant_db_name} does not exist")

            # If no tenant or tenant database doesn't exist, and we're not already using default
            elif self.current_tenant is not None and self.db.name != self.default_db_name:
                logger.info(f"MongoDB._check_tenant_context: No tenant, switching from {self.db.name} to {self.default_db_name}")
                self.switch_database(self.default_db_name)
                self.current_tenant = None

        except RuntimeError as e:
            # Handle case where app context is not available
            logger.debug(f"Runtime error in tenant context check: {e}")
            return
        except Exception as e:
            # Log other exceptions but don't crash
            logger.error(f"Error in tenant context check: {e}")
            return

    def _get_tenant_from_url(self):
        """Extract tenant name from the URL path"""
        try:
            # Check if we're in a request context
            if not has_request_context():
                return None

            path = request.path.lstrip('/')
            parts = path.split('/', 1)

            # If the path has at least one segment
            if parts and parts[0]:
                return parts[0]
        except RuntimeError:
            # This happens when there's no request context
            return None
        except Exception as e:
            logger.error(f"Error extracting tenant from URL: {e}")

        return None

    def _tenant_db_exists(self, db_name):
        """Check if a tenant database exists"""
        try:
            # Check if the database has collections
            db = self.client[db_name]
            return len(db.list_collection_names()) > 0
        except Exception as e:
            logger.error(f"Error checking if tenant database exists: {e}")
            return False

    def switch_database(self, db_name):
        """Switch to a different database"""
        try:
            # Check if we're already connected to this database
            if hasattr(self, 'db') and self.db.name == db_name:
                logger.debug(f"Already connected to database: {db_name}")
                return True

            # Get the current database name for logging
            current_db_name = self.db.name if hasattr(self, 'db') else 'None'

            # Switch to the new database
            self.db = self.client[db_name]
            self._init_collections()
            logger.info(f"Switched database: {current_db_name} -> {db_name}")

            # Update tenant context in session and g if this is a tenant database
            # Only if we're in an application context
            if db_name != self.default_db_name and db_name.endswith('_custdb'):
                tenant_name = db_name[:-7]  # Remove '_custdb' suffix
                self.current_tenant = tenant_name

                # Only update Flask context objects if in application context
                if has_app_context():
                    g.tenant = tenant_name

                # Only update session if in request context
                if has_request_context():
                    session['tenant'] = tenant_name

                logger.debug(f"Updated tenant context: {tenant_name}")
            elif db_name == self.default_db_name:
                self.current_tenant = None
                logger.debug("Cleared tenant context")

            return True
        except RuntimeError as e:
            # Handle case where app context is not available
            logger.debug(f"Runtime error in switch_database: {e}")

            # Still switch the database even if we can't update Flask context
            self.db = self.client[db_name]
            self._init_collections()

            # Update tenant tracking
            if db_name != self.default_db_name and db_name.endswith('_custdb'):
                self.current_tenant = db_name[:-7]  # Remove '_custdb' suffix
                logger.debug(f"Updated tenant tracking: {self.current_tenant}")
            elif db_name == self.default_db_name:
                self.current_tenant = None
                logger.debug("Cleared tenant tracking")

            return True
        except Exception as e:
            logger.error(f"Error switching database: {e}")
            return False

    def get_subscriptions(self, query=None, limit=None):
        """Get subscriptions with optional filtering from master_user_data"""
        query = query or {}

        # If status: active is in the query, change it to user_status: active
        if query.get('status') == 'active':
            query.pop('status')
            query['user_status'] = 'active'

        cursor = self.master_user_data.find(query)
        if limit:
            cursor = cursor.limit(limit)

        # Transform the data to match the old subscriptions format
        result = []
        for user in list(cursor):
            # Create a subscription-like object from master_user_data
            subscription = {
                'user_id': user.get('user_id'),
                'access_code': user.get('access_code'),
                'status': user.get('user_status', 'inactive'),  # Use the new user_status field
                'subscription_time': user.get('registration_time'),
                'subscribed_on': user.get('subscribed_on'),  # Include the subscribed_on field
                'added_at': user.get('added_at'),  # Include the added_at field at root level
                'user_details': {
                    'name': user.get('name'),
                    'email': user.get('email'),
                    'whatsapp': user.get('whatsapp'),
                    'trading_experience': user.get('trading_experience'),
                },
                'user_status': user.get('user_status')  # Include the new field directly
            }
            result.append(subscription)

        return result

    def get_subscription(self, user_id):
        """Get a specific subscription by user_id from master_user_data"""
        user = self.master_user_data.find_one({"user_id": user_id})
        if not user:
            return None

        # Create a subscription-like object from master_user_data
        subscription = {
            'user_id': user.get('user_id'),
            'access_code': user.get('access_code'),
            'status': user.get('user_status', 'inactive'),  # Use the new user_status field
            'subscription_time': user.get('registration_time'),
            'subscribed_on': user.get('subscribed_on'),  # Include the subscribed_on field
            'added_at': user.get('added_at'),  # Include the added_at field at root level
            'user_details': {
                'name': user.get('name'),
                'email': user.get('email'),
                'whatsapp': user.get('whatsapp'),
                'trading_experience': user.get('trading_experience'),
            },
            'user_status': user.get('user_status'),  # Include the new field directly
            'broker_reg': user.get('broker_reg'),
            'user_verify': user.get('user_verify')
        }
        return subscription

    def add_activity_log(self, action, details, admin_id):
        """Add an activity log entry"""
        from datetime import datetime, timezone
        log_entry = {
            "action": action,
            "details": details,
            "admin_id": admin_id,
            "timestamp": datetime.now(timezone.utc)
        }
        result = self.activity_logs.insert_one(log_entry)
        return result.inserted_id

    def get_recent_logs(self, limit=10):
        """Get recent activity logs"""
        return list(self.activity_logs.find().sort("timestamp", -1).limit(limit))

    def get_admin_by_username(self, username):
        """Get admin by username"""
        return self.admins.find_one({"username": username})

    def get_admin_by_id(self, admin_id):
        """Get admin by ID"""
        try:
            # Try to convert to ObjectId if it's a string
            if isinstance(admin_id, str):
                return self.admins.find_one({"_id": ObjectId(admin_id)})
            # If admin_id is an integer, search by numeric ID
            elif isinstance(admin_id, int):
                return self.admins.find_one({"admin_id": admin_id})
            # If it's already an ObjectId
            elif isinstance(admin_id, ObjectId):
                return self.admins.find_one({"_id": admin_id})
            else:
                logger.error(f"Invalid admin_id type: {type(admin_id)}")
                return None
        except Exception as e:
            logger.error(f"Error retrieving admin by ID: {e}")
            return None

    def create_admin(self, username, password_hash):
        """Create a new admin"""
        admin_data = {
            "username": username,
            "password_hash": password_hash
        }
        result = self.admins.insert_one(admin_data)
        return result.inserted_id

    def init_admin(self, username, password_hash):
        """Initialize admin if none exists"""
        if self.admins.count_documents({}) == 0:
            return self.create_admin(username, password_hash)
        return None

    def get_access_codes(self):
        """Get all access codes"""
        return list(self.access_codes.find())

    def get_available_access_codes(self):
        """Get access codes that are not in use"""
        # Get all access codes
        all_codes = self.get_access_codes()

        # Get codes that are in use from master_user_data
        used_codes = [user['access_code'] for user in self.master_user_data.find()
                      if 'access_code' in user]

        # Filter out used codes
        available_codes = [code for code in all_codes
                          if code['code'] not in used_codes]

        return available_codes

    def update_admin_password(self, admin_id, new_password_hash):
        """Update an admin's password hash"""
        try:
            # Convert to ObjectId if it's a string
            if isinstance(admin_id, str):
                object_id = ObjectId(admin_id)
                result = self.admins.update_one(
                    {"_id": object_id},
                    {"$set": {"password_hash": new_password_hash}}
                )
            # If admin_id is an integer, update by numeric ID
            elif isinstance(admin_id, int):
                result = self.admins.update_one(
                    {"admin_id": admin_id},
                    {"$set": {"password_hash": new_password_hash}}
                )
            # If it's already an ObjectId
            elif isinstance(admin_id, ObjectId):
                result = self.admins.update_one(
                    {"_id": admin_id},
                    {"$set": {"password_hash": new_password_hash}}
                )
            else:
                logger.error(f"Invalid admin_id type: {type(admin_id)}")
                return False

            return result.modified_count > 0
        except Exception as e:
            logger.error(f"Error updating admin password: {e}")
            return False