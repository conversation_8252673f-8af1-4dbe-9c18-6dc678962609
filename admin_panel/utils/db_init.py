from werkzeug.security import generate_password_hash
from utils.mongo_db import MongoDB
import logging

logger = logging.getLogger(__name__)

def init_db():
    """Initialize the database with default admin if none exists"""
    mongo_db = MongoDB()
    
    # Check if admin exists, if not create one
    admin_id = mongo_db.init_admin('admin', generate_password_hash('Admin@1234'))
    if admin_id:
        logger.info(f"Created default admin user with ID: {admin_id}")